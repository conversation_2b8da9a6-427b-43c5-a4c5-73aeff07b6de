import "./bootstrap";
import "../css/app.css";
import "floating-vue/dist/style.css";

import { createApp, h } from "vue";
import { createInertiaApp } from "@inertiajs/vue3";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { ZiggyVue } from "../../vendor/tightenco/ziggy";
import { createPinia } from "pinia";
import FloatingVue from "floating-vue";

import { VsxIcon } from "vue-iconsax";

const appName = import.meta.env.VITE_APP_NAME || "Laravel";

createInertiaApp({
    title: (title) => (title ? `${title} - ${appName}` : appName),
    resolve: (name) =>
        resolvePageComponent(
            `./Pages/${name}.vue`,
            import.meta.glob("./Pages/**/*.vue"),
        ),
    setup({ el, App, props, plugin }) {
        const pinia = createPinia();

        return createApp({ render: () => h(App, props) })
            .component("VsxIcon", VsxIcon)
            .use(plugin)
            .use(ZiggyVue)
            .use(pinia)
            .use(FloatingVue, {
                themes: {
                    light: {
                        $resetCss: true,
                        triggers: ["hover", "focus", "touch"],
                        autoHide: true,
                        placement: "right",
                    },
                },
            })
            .mount(el);
    },
    progress: {
        color: "rgba(241, 57, 151, 1)",
    },
});
