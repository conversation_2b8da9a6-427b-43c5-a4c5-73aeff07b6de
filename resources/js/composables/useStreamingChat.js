import { ref, onUnmounted } from 'vue'
import axios from 'axios'

export function useStreamingChat() {
    const isStreaming = ref(false)
    const streamedContent = ref('')
    const error = ref(null)
    let eventSource = null

    const startStream = async (payload) => {
        return new Promise((resolve, reject) => {
            try {
                isStreaming.value = true
                streamedContent.value = ''
                error.value = null
                const sessionId = `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
                const streamPayload = {
                    ...payload,
                    stream: true,
                    session_id: sessionId
                }

                const url = '/api/chat/stream'
                const params = new URLSearchParams()

                Object.keys(streamPayload).forEach(key => {
                    if (Array.isArray(streamPayload[key])) {
                        streamPayload[key].forEach((item, index) => {
                            if (typeof item === 'object') {
                                Object.keys(item).forEach(subKey => {
                                    params.append(`${key}[${index}][${subKey}]`, item[subKey])
                                })
                            } else {
                                params.append(`${key}[${index}]`, item)
                            }
                        })
                    } else if (typeof streamPayload[key] === 'object' && streamPayload[key] !== null) {
                        Object.keys(streamPayload[key]).forEach(subKey => {
                            params.append(`${key}[${subKey}]`, streamPayload[key][subKey])
                        })
                    } else {
                        params.append(key, streamPayload[key])
                    }
                })

                streamViaFetch(streamPayload, resolve, reject)

            } catch (err) {
                error.value = err.message
                isStreaming.value = false
                reject(err)
            }
        })
    }

    const streamViaFetch = async (payload, resolve, reject) => {
        try {
            const response = await fetch('/api/chat/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(payload)
            })

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const reader = response.body.getReader()
            const decoder = new TextDecoder()

            while (true) {
                const { done, value } = await reader.read()
                
                if (done) {
                    break
                }

                const chunk = decoder.decode(value, { stream: true })
                const lines = chunk.split('\n')

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6))
                            handleStreamMessage(data, resolve, reject)
                        } catch (e) {
                            console.warn('Invalid JSON in stream:', line, e)
                        }
                    }
                }
            }
        } catch (err) {
            error.value = err.message
            isStreaming.value = false
            reject(err)
        }
    }

    const handleStreamMessage = (data, resolve, reject) => {
        switch (data.type) {
            case 'start':
                break
            case 'chunk':
                streamedContent.value += data.content
                break
            case 'complete':
                isStreaming.value = false
                resolve({
                    content: streamedContent.value,
                    type: 'complete'
                })
                break
            case 'error':
                error.value = data.message
                isStreaming.value = false
                reject(new Error(data.message))
                break
        }
    }

    const sendMessage = async (payload) => {
        try {
            const response = await axios.post('/api/chat/message', payload)
            return response.data
        } catch (err) {
            error.value = err.response?.data?.message || err.message
            throw err
        }
    }

    const stopStream = () => {
        if (eventSource) {
            eventSource.close()
            eventSource = null
        }
        isStreaming.value = false
    }

    onUnmounted(() => {
        stopStream()
    })

    return {
        isStreaming,
        streamedContent,
        error,
        startStream,
        sendMessage,
        stopStream
    }
} 