import { reactive, computed } from 'vue'

export function useImageModal() {
    const state = reactive({
        isOpen: false,
        images: [],
        currentIndex: 0
    })

    const currentImage = computed(() => {
        return state.images[state.currentIndex] || null
    })

    const openModal = (images, initialIndex = 0) => {
        state.images = images
        state.currentIndex = initialIndex
        state.isOpen = true
    }

    const closeModal = () => {
        state.isOpen = false
    }

    const navigateImage = (direction) => {
        if (state.images.length <= 1) return
        
        const newIndex = (state.currentIndex + direction + state.images.length) % state.images.length
        state.currentIndex = newIndex
    }

    const goToImage = (index) => {
        if (index >= 0 && index < state.images.length) {
            state.currentIndex = index
        }
    }

    const hasMultipleImages = computed(() => state.images.length > 1)
    const isFirstImage = computed(() => state.currentIndex === 0)
    const isLastImage = computed(() => state.currentIndex === state.images.length - 1)

    return {

        isOpen: computed(() => state.isOpen),
        images: computed(() => state.images),
        currentIndex: computed(() => state.currentIndex),
        currentImage,

        hasMultipleImages,
        isFirstImage,
        isLastImage,

        openModal,
        closeModal,
        navigateImage,
        goToImage
    }
} 