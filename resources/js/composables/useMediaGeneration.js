import { ref } from 'vue'
import axios from 'axios'

export function useMediaGeneration() {
    const isGenerating = ref(false)
    const generationError = ref(null)
    const generatedMedia = ref([])

    const getHeaders = () => {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }

        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken
        }

        return headers
    }

    const generateImage = async (prompt, options = {}) => {
        isGenerating.value = true
        generationError.value = null

        try {
            const payload = {
                model: options.model || 'midjourney',
                async: options.async !== false,
                ...options
            }

            if (options.post_content) {
                payload.post_content = options.post_content
                if (options.selected_social_platform) payload.selected_social_platform = options.selected_social_platform
                if (options.audience_gender) payload.audience_gender = options.audience_gender
                if (options.audience_age) payload.audience_age = options.audience_age
                if (options.audience_income) payload.audience_income = options.audience_income
                if (options.spelling_variant) payload.spelling_variant = options.spelling_variant
            } else {
                payload.prompt = prompt
            }

            const response = await axios.post('/api/media/image/generate', payload, {
                headers: getHeaders()
            })

            if (response.data.success) {
                const mediaItem = {
                    id: Date.now(),
                    type: 'image',
                    prompt: response.data.data.generated_prompt || prompt,
                    originalPrompt: prompt,
                    originalPostContent: response.data.data.original_post_content || null,
                    model: payload.model,
                    taskId: response.data.data.task_id,
                    status: response.data.data.status || 'pending',
                    result: response.data.data.result || null,
                    timestamp: new Date().toISOString()
                }

                generatedMedia.value.push(mediaItem)
                return mediaItem
            } else {
                throw new Error(response.data.error || 'Failed to generate image')
            }
        } catch (error) {
            generationError.value = error.response?.data?.details || error.message
            throw error
        } finally {
            isGenerating.value = false
        }
    }

    const generateVideo = async (prompt, options = {}) => {
        isGenerating.value = true
        generationError.value = null

        try {
            const payload = {
                model: options.model || 'kling',
                async: options.async !== false,
                ...options
            }

            if (options.post_content) {
                payload.post_content = options.post_content
                if (options.selected_social_platform) payload.selected_social_platform = options.selected_social_platform
                if (options.audience_gender) payload.audience_gender = options.audience_gender
                if (options.audience_age) payload.audience_age = options.audience_age
                if (options.audience_income) payload.audience_income = options.audience_income
                if (options.spelling_variant) payload.spelling_variant = options.spelling_variant
            } else {
                payload.prompt = prompt
            }

            const response = await axios.post('/api/media/video/generate', payload, {
                headers: getHeaders()
            })

            if (response.data.success) {
                const mediaItem = {
                    id: Date.now(),
                    type: 'video',
                    prompt: response.data.data.generated_prompt || prompt,
                    originalPrompt: prompt,
                    originalPostContent: response.data.data.original_post_content || null,
                    model: payload.model,
                    taskId: response.data.data.task_id,
                    status: response.data.data.status || 'pending',
                    result: response.data.data.result || null,
                    timestamp: new Date().toISOString()
                }

                generatedMedia.value.push(mediaItem)
                return mediaItem
            } else {
                throw new Error(response.data.error || 'Failed to generate video')
            }
        } catch (error) {
            generationError.value = error.response?.data?.details || error.message
            throw error
        } finally {
            isGenerating.value = false
        }
    }

    const checkTaskStatus = async (taskId) => {
        try {
            const response = await axios.get(`/api/media/task/${taskId}/status`, {
                headers: getHeaders()
            })

            if (response.data.success) {
                const mediaIndex = generatedMedia.value.findIndex(item => item.taskId === taskId)
                if (mediaIndex !== -1) {
                    generatedMedia.value[mediaIndex].status = response.data.data.status

                    // Store the complete API response when task is completed
                    if (response.data.data.status === 'completed') {
                        // Store the full response data so GeneratedMedia.vue can access result.output
                        generatedMedia.value[mediaIndex].result = response.data.data
                    }
                }
                return response.data.data
            } else {
                throw new Error(response.data.error || 'Failed to get task status')
            }
        } catch (error) {
            generationError.value = error.response?.data?.details || error.message
            throw error
        }
    }

    const cancelTask = async (taskId) => {
        try {
            const response = await axios.delete(`/api/media/task/${taskId}/cancel`, {
                headers: getHeaders()
            })

            if (response.data.success) {
                const mediaIndex = generatedMedia.value.findIndex(item => item.taskId === taskId)
                if (mediaIndex !== -1) {
                    generatedMedia.value[mediaIndex].status = 'cancelled'
                }
                return true
            } else {
                throw new Error(response.data.message || 'Failed to cancel task')
            }
        } catch (error) {
            generationError.value = error.response?.data?.details || error.message
            throw error
        }
    }

    const getAvailableModels = async () => {
        try {
            const response = await axios.get('/api/media/models', {
                headers: getHeaders()
            })

            if (response.data.success) {
                return response.data.data
            } else {
                throw new Error(response.data.error || 'Failed to get available models')
            }
        } catch (error) {
            generationError.value = error.response?.data?.details || error.message
            throw error
        }
    }

    const generateImageFromPost = (postContent, options = {}) => {
        return generateImage(postContent, { ...options, post_content: postContent })
    }

    const generateVideoFromPost = (postContent, options = {}) => {
        return generateVideo(postContent, { ...options, post_content: postContent })
    }

    const generateMidjourneyImage = (prompt, options = {}) => {
        return generateImage(prompt, { ...options, model: 'midjourney' })
    }

    const generateFluxImage = (prompt, options = {}) => {
        return generateImage(prompt, { ...options, model: 'flux' })
    }

    const generateKlingVideo = (prompt, options = {}) => {
        return generateVideo(prompt, { ...options, model: 'kling' })
    }

    const generateLumaVideo = (prompt, options = {}) => {
        return generateVideo(prompt, { ...options, model: 'luma' })
    }

    const generateMidjourneyImageFromPost = (postContent, options = {}) => {
        return generateImageFromPost(postContent, { ...options, model: 'midjourney' })
    }

    const generateFluxImageFromPost = (postContent, options = {}) => {
        return generateImageFromPost(postContent, { ...options, model: 'flux' })
    }

    const generateKlingVideoFromPost = (postContent, options = {}) => {
        return generateVideoFromPost(postContent, { ...options, model: 'kling' })
    }

    const generateLumaVideoFromPost = (postContent, options = {}) => {
        return generateVideoFromPost(postContent, { ...options, model: 'luma' })
    }

    const clearError = () => {
        generationError.value = null
    }

    const getMediaByTaskId = (taskId) => {
        return generatedMedia.value.find(item => item.taskId === taskId)
    }

    const removeMedia = (taskId) => {
        const index = generatedMedia.value.findIndex(item => item.taskId === taskId)
        if (index !== -1) {
            generatedMedia.value.splice(index, 1)
        }
    }

    return {
        isGenerating,
        generationError,
        generatedMedia,
        generateImage,
        generateVideo,
        checkTaskStatus,
        cancelTask,
        getAvailableModels,
        generateImageFromPost,
        generateVideoFromPost,
        generateMidjourneyImage,
        generateFluxImage,
        generateKlingVideo,
        generateLumaVideo,
        generateMidjourneyImageFromPost,
        generateFluxImageFromPost,
        generateKlingVideoFromPost,
        generateLumaVideoFromPost,
        clearError,
        getMediaByTaskId,
        removeMedia
    }
} 