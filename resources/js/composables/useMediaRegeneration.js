import { ref } from 'vue'
import { useMediaGeneration } from './useMediaGeneration'

export function useMediaRegeneration() {
    const {
        isGenerating,
        generateImage,
        generateVideo,
        clearError
    } = useMediaGeneration()

    const regenerationModal = ref({
        isOpen: false,
        mediaType: 'image',
        currentMedia: [],
        originalPrompt: '',
        generatedPrompt: '',
        originalPostContent: '',
        messageId: null,
        useTemplate: true
    })

    const hasCompletedMedia = (messageMedia, mediaType) => {
        return messageMedia.some(item => item.type === mediaType && item.status === 'completed')
    }

    const getCompletedMediaByType = (messageMedia, mediaType) => {
        const completedMedia = messageMedia.find(item => item.type === mediaType && item.status === 'completed')

        if (!completedMedia || !completedMedia.result) return []

        if (mediaType === 'image') {
            return extractImageResults(completedMedia.result)
        } else if (mediaType === 'video') {
            return extractVideoResults(completedMedia.result)
        }

        return []
    }

    const extractImageResults = (result) => {
        if (!result) return []

        if (result.images && Array.isArray(result.images)) {
            return result.images.map((imageItem, index) => ({
                url: imageItem.url || imageItem, // Handle both {url: "..."} and direct URL string
                alt: `Generated image ${index + 1}`
            }))
        }

        if (Array.isArray(result)) {
            return result.map((item, index) => ({
                url: item.url || item,
                alt: `Generated image ${index + 1}`
            }))
        }

        if (result.url) {
            return [{
                url: result.url,
                alt: 'Generated image'
            }]
        }

        return []
    }

    const extractVideoResults = (result) => {
        if (!result) return []

        if (Array.isArray(result)) {
            return result.map(item => ({
                url: item.url || item,
                duration: item.duration || 'Unknown'
            }))
        }

        if (result.videos) {
            return result.videos.map(item => ({
                url: item.url || item,
                duration: item.duration || 'Unknown'
            }))
        }

        if (result.url) {
            return [{
                url: result.url,
                duration: result.duration || 'Unknown'
            }]
        }

        return []
    }

    const extractRegenerationData = (mediaItem) => {
        return {
            originalPrompt: mediaItem.originalPrompt || mediaItem.prompt || '',
            generatedPrompt: mediaItem.prompt || '',
            originalPostContent: mediaItem.originalPostContent || '',
            hasTemplate: !!(mediaItem.originalPostContent)
        }
    }

    const openRegenerationModal = (messageId, messageMedia, mediaType, originalPrompt, generatedPrompt = '', originalPostContent = '') => {
        const currentMedia = getCompletedMediaByType(messageMedia, mediaType)
        if (currentMedia.length === 0) {
            console.warn('No completed media found for type:', mediaType)
            return
        }

        console.log('Opening regeneration modal with media:', currentMedia)

        const firstMediaItem = currentMedia[0]
        let regenerationData = { originalPrompt, generatedPrompt, originalPostContent }

        if (firstMediaItem && (firstMediaItem.originalPrompt || firstMediaItem.originalPostContent)) {
            const extracted = extractRegenerationData(firstMediaItem)
            regenerationData = {
                originalPrompt: extracted.originalPrompt || originalPrompt,
                generatedPrompt: extracted.generatedPrompt || generatedPrompt || originalPrompt,
                originalPostContent: extracted.originalPostContent || originalPostContent
            }
        }

        regenerationModal.value = {
            isOpen: true,
            mediaType,
            currentMedia,
            originalPrompt: regenerationData.originalPrompt,
            generatedPrompt: regenerationData.generatedPrompt,
            originalPostContent: regenerationData.originalPostContent,
            messageId,
            useTemplate: !!(regenerationData.originalPostContent)
        }
    }

    const openRegenerationModalFromMediaItem = (messageId, mediaItem, mediaType) => {
        const regenerationData = extractRegenerationData(mediaItem)

        regenerationModal.value = {
            isOpen: true,
            mediaType,
            currentMedia: [mediaItem],
            originalPrompt: regenerationData.originalPrompt,
            generatedPrompt: regenerationData.generatedPrompt,
            originalPostContent: regenerationData.originalPostContent,
            messageId,
            useTemplate: regenerationData.hasTemplate
        }
    }

    const closeRegenerationModal = () => {
        regenerationModal.value.isOpen = false
    }

    const regenerateMedia = async ({ prompt, options, useTemplate = false, postContent = '' }, replaceMediaCallback, pollStatusCallback) => {
        if (isGenerating.value) return

        clearError()

        try {
            let mediaItem
            const selectedModel = options.model

            const generationOptions = {
                aspect_ratio: options.aspect_ratio || '1:1',
                model: selectedModel || (regenerationModal.value.mediaType === 'image' ? 'midjourney' : 'kling'),
                ...options
            }

            if (useTemplate && postContent) {
                generationOptions.post_content = postContent

                if (options.selected_social_platform) generationOptions.selected_social_platform = options.selected_social_platform
                if (options.audience_gender) generationOptions.audience_gender = options.audience_gender
                if (options.audience_age) generationOptions.audience_age = options.audience_age
                if (options.audience_income) generationOptions.audience_income = options.audience_income
                if (options.spelling_variant) generationOptions.spelling_variant = options.spelling_variant
            }

            if (regenerationModal.value.mediaType === 'image') {
                mediaItem = await generateImage(prompt, generationOptions)
            } else {
                generationOptions.duration = options.duration || 5
                mediaItem = await generateVideo(prompt, generationOptions)
            }

            console.log('Generated media item:', mediaItem)
            replaceMediaCallback(regenerationModal.value.messageId, mediaItem)
            pollStatusCallback(mediaItem.taskId)

        } catch (error) {
            console.error('Error regenerating media:', error)
            throw error
        }
    }

    const toggleTemplateMode = () => {
        regenerationModal.value.useTemplate = !regenerationModal.value.useTemplate
    }

    return {
        regenerationModal,
        isGenerating,
        hasCompletedMedia,
        getCompletedMediaByType,
        extractRegenerationData,
        openRegenerationModal,
        openRegenerationModalFromMediaItem,
        closeRegenerationModal,
        regenerateMedia,
        toggleTemplateMode
    }
}
