import { ref, reactive, computed } from 'vue'
import { useStreamingChat } from './useStreamingChat'
import Showdown from 'showdown'

// Content separator delimiters used to split AI-generated content into multiple posts
// These are literal text patterns that the AI includes in responses to separate content
const CONTENT_SEPARATORS = [
    '<<------->>', // Long double-bracket arrow separator (10 dashes)
    '<<----->>',   // Medium double-bracket arrow separator (7 dashes)
    '<------->'    // Standard single-bracket arrow separator (7 dashes)
]

export function useTabChat() {
    const tabChats = reactive({})
    const activeTabId = ref(null)
    const converter = new Showdown.Converter()

    const {
        isStreaming,
        streamedContent,
        error: streamError,
        startStream
    } = useStreamingChat()

    const initializeTabChat = (tabId) => {
        if (!tabChats[tabId]) {
            tabChats[tabId] = {
                messages: [],
                isInitialized: false,
                hasGeneratedContent: false,
                generatedContent: '',
                generatedHtmlContent: ''
            }
        }
    }

    const getTabMessages = (tabId) => {
        initializeTabChat(tabId)
        return tabChats[tabId].messages
    }

    const addMessageToTab = (tabId, message) => {
        initializeTabChat(tabId)
        const timestamp = getCurrentTimestamp()

        const messageWithTimestamp = {
            ...message,
            timestamp,
            id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        }

        tabChats[tabId].messages.push(messageWithTimestamp)
        return messageWithTimestamp
    }

    const setTabGeneratedContent = (tabId, content, htmlContent) => {
        initializeTabChat(tabId)
        tabChats[tabId].generatedContent = content
        tabChats[tabId].generatedHtmlContent = htmlContent
        tabChats[tabId].hasGeneratedContent = true
    }

    const getTabGeneratedContent = (tabId) => {
        initializeTabChat(tabId)
        return {
            content: tabChats[tabId].generatedContent,
            htmlContent: tabChats[tabId].generatedHtmlContent,
            hasContent: tabChats[tabId].hasGeneratedContent
        }
    }

    const sendTabMessage = async (tabId, userMessage, additionalPayload = {}) => {
        initializeTabChat(tabId)
        activeTabId.value = tabId

        const userMsg = addMessageToTab(tabId, {
            role: 'user',
            content: userMessage
        })

        try {

           let modifiedMessage = `${userMessage}. IMPORTANT: If you decide to generate multiple separate posts or content pieces, please separate each one with exactly <------->. Format: Content 1 <-------> Content 2 <-------> Content 3 etc.`

            const messages = tabChats[tabId].messages.map(msg => ({
                role: msg.role,
                content: msg.content
            }))

            messages[messages.length - 1].content = modifiedMessage

            const payload = {
                messages,
                ...additionalPayload
            }

            const result = await startStream(payload)

            // Check for multiple delimiters to handle different formats
            let foundDelimiter = null

            for (const delimiter of CONTENT_SEPARATORS) {
                if (result.content.includes(delimiter)) {
                    foundDelimiter = delimiter
                    break
                }
            }

            if (foundDelimiter) {
                const posts = splitContentIntoPosts(result.content, foundDelimiter)

                if (posts.length > 1) {
                    const addedMessages = addMultipleMessagesToTab(tabId, posts)

                    return {
                        userMessage: userMsg,
                        assistantMessages: addedMessages,
                        result
                    }
                }
            }

            const assistantMsg = addMessageToTab(tabId, {
                role: 'assistant',
                content: result.content,
                htmlContent: converter.makeHtml(result.content),
                isPost: true,  // Ensure all assistant content in post generation uses PostCard rendering
                postIndex: 1,
                totalPosts: 1
            })

            return {
                userMessage: userMsg,
                assistantMessage: assistantMsg,
                result
            }
        } catch (error) {
            addMessageToTab(tabId, {
                role: 'assistant',
                content: `Sorry, there was an error processing your message: ${error.message}`,
                htmlContent: `<p class="text-red-600">Sorry, there was an error processing your message: ${error.message}</p>`,
                isError: true
            })
            throw error
        }
    }

    const splitContentIntoPosts = (content, delimiter = '<------->' ) => {
        const posts = content.split(delimiter).filter(post => post.trim().length > 0)

        return posts.map(post => post.trim())
    }

    const addMultipleMessagesToTab = (tabId, messages) => {
        initializeTabChat(tabId)
        const addedMessages = []

        messages.forEach((messageContent, index) => {
            const timestamp = getCurrentTimestamp()

            const messageWithTimestamp = {
                role: 'assistant',
                content: messageContent,
                htmlContent: converter.makeHtml(messageContent),
                timestamp,
                id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}_${index}`,
                isPost: true,
                postIndex: index + 1,
                totalPosts: messages.length
            }

            tabChats[tabId].messages.push(messageWithTimestamp)
            addedMessages.push(messageWithTimestamp)
        })

        return addedMessages
    }


    const generateTabContent = async (tabId, channel, campaignData, audienceData) => {
        initializeTabChat(tabId)
        activeTabId.value = tabId

        try {
            // Extract post count and context from campaignData if available
            const postCount = campaignData?.post_count || 5
            const context = campaignData?.context || ''

            const payload = {
                feature: 'social_media_content',
                audience_gender: audienceData.gender || 'not specified',
                audience_age: audienceData.ageRange?.[0] || 'not specified',
                audience_income: 'not specified',
                selected_social_platform: channel.charAt(0).toUpperCase() + channel.slice(1),
                campaign_type: campaignData?.campaignType || 'brand awareness campaign',
                fundraising_target: parseInt(campaignData?.targetAmount) || null,
                call_to_action: campaignData?.campaignDescription || '',
                spelling_variant: 'British English',
                post_count: postCount,
                platform: channel,
                context: context
            }

            // If context is provided, add it as a user message
            if (context) {
                payload.messages = [
                    {
                        role: 'user',
                        content: context
                    }
                ]
            }

            const result = await startStream(payload)
            handleStreamingCompletion(tabId, result.content)

            tabChats[tabId].isInitialized = true
            return result
        } catch (error) {
            addMessageToTab(tabId, {
                role: 'assistant',
                content: `Error generating content for ${channel}: ${error.message}`,
                htmlContent: `<p class="text-red-600">Error generating content for ${channel}: ${error.message}</p>`,
                isError: true
            })
            throw error
        }
    }

    const clearTabChat = (tabId) => {
        if (tabChats[tabId]) {
            tabChats[tabId].messages = []
            tabChats[tabId].isInitialized = false
            tabChats[tabId].hasGeneratedContent = false
            tabChats[tabId].generatedContent = ''
            tabChats[tabId].generatedHtmlContent = ''
        }
    }

    const clearAllTabChats = () => {
        Object.keys(tabChats).forEach(tabId => {
            delete tabChats[tabId]
        })
        activeTabId.value = null
    }

    const editTabMessage = async (tabId, messageId, editPrompt, additionalPayload = {}) => {
        initializeTabChat(tabId)
        activeTabId.value = tabId

        const messageIndex = tabChats[tabId].messages.findIndex(msg => msg.id === messageId)
        if (messageIndex === -1) {
            throw new Error('Message not found')
        }

        const originalMessage = tabChats[tabId].messages[messageIndex]

        try {
            let originalContent = originalMessage.content || ''
            if (originalMessage.htmlContent) {
                const tempDiv = document.createElement('div')
                tempDiv.innerHTML = originalMessage.htmlContent
                originalContent = tempDiv.textContent || tempDiv.innerText || originalContent
            }

            const editInstructions = `Please improve the following content based on this request: "${editPrompt}"

Original content:
${originalContent}

Please provide only the improved content without any additional explanation or formatting.`

            const payload = {
                messages: [
                    {
                        role: 'user',
                        content: editInstructions
                    }
                ],
                ...additionalPayload
            }

            const result = await startStream(payload)

            return {
                originalMessage,
                improvedContent: result.content,
                result
            }
        } catch (error) {
            throw new Error(`Failed to edit message: ${error.message}`)
        }
    }

    const updateTabMessage = (tabId, messageId, newContent, newHtmlContent) => {
        initializeTabChat(tabId)

        const messageIndex = tabChats[tabId].messages.findIndex(msg => msg.id === messageId)
        if (messageIndex === -1) {
            throw new Error('Message not found')
        }

        tabChats[tabId].messages[messageIndex] = {
            ...tabChats[tabId].messages[messageIndex],
            content: newContent,
            htmlContent: newHtmlContent || converter.makeHtml(newContent),
            isEdited: true,
            editedAt: getCurrentTimestamp()
        }

        return tabChats[tabId].messages[messageIndex]
    }

    const isTabStreaming = computed(() => {
        return isStreaming.value && activeTabId.value
    })

    const currentStreamingContent = computed(() => {
        if (isStreaming.value && activeTabId.value) {
            const content = streamedContent.value
            // Check for any delimiter format in streaming content
            const hasDelimiter = CONTENT_SEPARATORS.some(delimiter => content.includes(delimiter))

            if (hasDelimiter) {
                return converter.makeHtml(content)
            }
            return converter.makeHtml(content)
        }
        return ''
    })

    const handleStreamingCompletion = (tabId, finalContent) => {
        initializeTabChat(tabId)

        const htmlContent = converter.makeHtml(finalContent)
        setTabGeneratedContent(tabId, finalContent, htmlContent)

        // Check for multiple delimiters to handle different formats
        let foundDelimiter = null
        let posts = [finalContent] // Default to single post

        console.log('🚀 Processing streaming completion for tab:', tabId)
        console.log('📄 Content length:', finalContent.length)

        for (const delimiter of CONTENT_SEPARATORS) {
            if (finalContent.includes(delimiter)) {
                foundDelimiter = delimiter
                posts = splitContentIntoPosts(finalContent, delimiter)
                console.log(`✅ Found delimiter "${delimiter}" - Split into ${posts.length} posts`)
                break
            }
        }

        if (posts.length > 1) {
            // Multiple posts - use existing logic
            console.log(`📝 Adding ${posts.length} posts to tab ${tabId}`)
            addMultipleMessagesToTab(tabId, posts)
        } else {
            // Single post - ensure it gets isPost: true for consistent PostCard rendering
            console.log(`📝 Adding single post to tab ${tabId}`)
            addMessageToTab(tabId, {
                role: 'assistant',
                content: finalContent,
                htmlContent: htmlContent,
                isInitialContent: true,
                isPost: true,  // Add this flag to ensure PostCard rendering
                postIndex: 1,
                totalPosts: 1
            })
        }

        return posts
    }

    const getCurrentTimestamp = () => {
        return new Date().toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        })
    }

    return {
        // State
        tabChats,
        activeTabId,
        isStreaming: isTabStreaming,
        streamedContent,
        currentStreamingContent,
        streamError,

        // Methods
        initializeTabChat,
        getTabMessages,
        addMessageToTab,
        addMultipleMessagesToTab,
        splitContentIntoPosts,
        setTabGeneratedContent,
        getTabGeneratedContent,
        sendTabMessage,
        generateTabContent,
        clearTabChat,
        clearAllTabChats,
        handleStreamingCompletion,
        editTabMessage,
        updateTabMessage
    }
}
