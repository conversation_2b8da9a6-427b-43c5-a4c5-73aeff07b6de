<template>
    <table class="fs-xs w-full text-left text-text-headings">
        <tbody>
            <tr
                v-for="(item, index) in tableRows"
                :key="index"
                class="bg-surface-page"
                :class="[
                    item.isHighlighted
                        ? 'rounded border-t-4 border-white'
                        : 'border-b border-b-border-primary-light',
                ]"
            >
                <td
                    :class="[
                        'w-[212px] px-16 py-12 font-header font-bold',
                        item.isHighlighted
                            ? 'rounded bg-surface-action-3x-light'
                            : 'bg-surface-grey-2x-light',
                    ]"
                >
                    <Tooltip
                        v-if="item.tooltip"
                        :label="item.tooltip.label"
                        :tooltip="item.tooltip.tooltip"
                    />
                    <template v-else>
                        {{ item.label }}
                    </template>
                </td>
                <td
                    class="fs-md w-[212px] px-16 py-12"
                    :class="
                        item.isHighlighted
                            ? 'relative border-b border-border-primary-light font-header font-bold after:absolute after:-top-3 after:left-0 after:h-1 after:w-full after:bg-border-primary-light'
                            : 'font-normal'
                    "
                >
                    <span class="flex items-center justify-between">
                        {{ tableData[item.key] }}
                    </span>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script setup>
import Tooltip from "../Tooltip/Tooltip.vue";
const props = defineProps({
    tableData: { type: Object, required: true },
});

// Define the table rows with labels, keys, and a flag for highlighted rows
const tableRows = [
    { label: "Age", key: "age" },
    { label: "Gender", key: "gender" },
    { label: "Location", key: "location" },
    { label: "Salary", key: "salary" },
    // {
    //     label: "Total num. of donors",
    //     key: "predicted_num_donors",
    //     isHighlighted: true,
    // },
    // {
    //     label: "Data Confidence",
    //     key: "affinity",
    //     isHighlighted: true,
    //     tooltip: {
    //         label: "Data Confidence",
    //         tooltip:
    //             "This measures how much data Pravi currently has to support these predictions.",
    //     },
    // },
    // {
    //     label: "Predicted donation",
    //     key: "predicted_donation",
    //     isHighlighted: true,
    // },
    // {
    //     label: "Average num. donations per year",
    //     key: "likely_to_give_regularly",
    //     isHighlighted: true,
    // },
];
</script>
