import TablePersona from "./TablePersona.vue";

export default {
    component: TablePersona,
    tags: ["autodocs"],
    args: {},
};

export const Persona1 = {
    args: {
        tableData: {
            gender: "Male/Female",
            age: "25-40",
            location: "London",
            interests: "Rainforest, Arts",
            salary: "£50,000 - £70,000",
            predicted_num_donors: "11,500",
            affinity: "Very high",
            predicted_donation: "£140",
            likely_to_give_regularly: "Medium",
        },
    },
    // Adding methods to handle the emitted events
    argTypes: {
        action: { action: "clicked" }, // This will allow Storybook to log actions
    },
};
