<template>
    <table class="table-container">
        <tbody>
            <!-- First Row -->
            <tr class="table-row-normal table-row">
                <td class="table-cell-header table-cell-grey table-cell">
                    Gender
                </td>
                <td class="table-cell-body">
                    <span class="table-icon">
                        <VueSelect
                            :options="optionsGender"
                            v-model="tableForm.GENDER"
                            :clearable="false"
                            :components="{ OpenIndicator }"
                            class="w-full"
                        />
                    </span>
                </td>
            </tr>

            <!-- Second Row -->
            <tr class="table-row-normal table-row">
                <td class="table-cell-header table-cell-grey table-cell">
                    Age
                </td>
                <td class="table-cell-body">
                    <span class="table-icon">
                        <VueSelect
                            :options="optionsAge"
                            v-model="tableForm.AGE"
                            :clearable="false"
                            :components="{ OpenIndicator }"
                            class="w-full"
                        />
                    </span>
                </td>
            </tr>

            <!-- Third Row -->
            <tr class="table-row-normal table-row">
                <td class="table-cell-header table-cell-grey table-cell">
                    Location
                </td>
                <td class="table-cell-body">
                    <VueSelect
                        :options="optionsLocation"
                        v-model="tableForm.LOCATION"
                        :clearable="false"
                        :components="{ OpenIndicator }"
                        class="w-full"
                    />
                </td>
            </tr>

            <!-- Fourth Row -->
            <!-- <tr class="table-row-normal table-row">
                <td class="table-cell-header table-cell-grey table-cell">
                    Area of Focus
                </td>
                <td class="table-cell-body">
                    <span class="table-icon">
                        <VueSelect
                            label="name"
                            :options="findDonorsStore.areaOfFocusOptions"
                            v-model="tableForm.AREA_OF_FOCUS"
                            :reduce="(item) => item.name"
                            :clearable="false"
                            :components="{ OpenIndicator }"
                            class="w-full"
                        />
                    </span>
                </td>
            </tr> -->

            <!-- Fifth Row -->
            <tr class="table-row-normal table-row">
                <td class="table-cell-header table-cell-grey table-cell">
                    Salary
                </td>
                <td class="table-cell-body">
                    <span class="table-icon">
                        <VueSelect
                            :options="optionsSalary"
                            v-model="tableForm.SALARY"
                            :clearable="false"
                            :components="{ OpenIndicator }"
                            class="w-full"
                        />
                    </span>
                </td>
            </tr>

            <!-- Sixth Row (Highlighted) -->
            <!-- <tr class="table-row-highlighted table-row">
                <td class="table-cell-header table-cell-highlighted table-cell">
                    Total num. of donors
                </td>
                <td class="table-cell-body-highlighted table-cell">
                    <span class="table-icon">
                        {{ tableData.predicted_num_donors }}
                    </span>
                </td>
            </tr> -->

            <!-- Seventh Row (Highlighted) -->
            <tr class="table-row-highlighted table-row">
                <td class="table-cell-header table-cell-highlighted table-cell">
                    <Tooltip
                        label="Data Confidence"
                        tooltip="This measures how much data Pravi currently has to support these predictions."
                    />
                </td>
                <td class="table-cell-body-highlighted table-cell">
                    <span class="table-icon">
                        {{ tableData.affinity }}
                    </span>
                </td>
            </tr>

            <!-- Eighth Row (Highlighted) -->
            <tr class="table-row-highlighted table-row">
                <td class="table-cell-header table-cell-highlighted table-cell">
                    Predicted average donation
                </td>
                <td class="table-cell-body-highlighted table-cell">
                    <span class="table-icon">
                        {{ tableData.predicted_donation }}
                    </span>
                </td>
            </tr>

            <!-- Ninth Row (Highlighted) -->
            <tr class="table-row-highlighted table-row">
                <td class="table-cell-header table-cell-highlighted table-cell">
                    Average no. donations per year
                </td>
                <td class="table-cell-body-highlighted table-cell">
                    <span class="table-icon">
                        {{ tableData.likely_to_give_regularly }}
                    </span>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script setup>
import { ref, onMounted, h, watch } from "vue";
import IconEdit from "../Icons/IconEdit.vue";
import VueSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { useFindDonorsStore } from "@/stores/findDonors";
import Tooltip from "../Tooltip/Tooltip.vue";

const findDonorsStore = useFindDonorsStore();

const { updateForm } = findDonorsStore;

const props = defineProps({
    tableData: { type: Object, required: true },
    optionsGender: Array,
    optionsAge: Array,
    optionsLocation: Array,
    optionsSalary: Array,
});

// Create a render function for the OpenIndicator component
const OpenIndicator = {
    render() {
        return h(IconEdit, { class: { toggle: false } });
    },
};

const tableForm = ref({
    GENDER: "",
    AGE: "",
    LOCATION: "",
    SALARY: "",
    AREA_OF_FOCUS: "",
});

// Function to sync the dropdowns with tableData
const syncWithTableData = () => {
    tableForm.value.GENDER = props.tableData.gender;
    tableForm.value.AGE = props.tableData.age;
    tableForm.value.LOCATION = props.tableData.location;
    tableForm.value.SALARY = props.tableData.salary;
    tableForm.value.AREA_OF_FOCUS = props.tableData.areaOfFocus;
};

// Run on component mount
onMounted(() => {
    syncWithTableData();
});

// Watch for changes in tableData and update the corresponding selections
watch(
    () => props.tableData,
    (newTableData) => {
        syncWithTableData();
    },
    { deep: true },
);

// Watch for changes in tableForm dropdowns and update the form in the store
watch(
    () => tableForm,
    () => {
        updateForm(tableForm.value);
    },
    { deep: true },
);
</script>

<style>
:root,
:host {
    --vs-controls-color: #fff;
    --vs-selected-bg: rgba(241, 57, 151, 1);
    --vs-dropdown-option--active-bg: rgba(241, 57, 151, 1);
    --vs-dropdown-option--active-color: #fff;

    /* --vs-border-color: rgba(220, 221, 221, 1); */
    --vs-border-color: transparent;
    --vs-border-radius: 8px;
    --vs-actions-padding: 8px 12px;
}

.vs--open .vs__open-indicator {
    transform: none;
}
</style>
