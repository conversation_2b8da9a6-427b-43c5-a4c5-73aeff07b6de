<template>
    <table class="fs-sm w-full text-left text-text-headings">
        <tbody>
            <tr
                v-for="(item, index) in tableRows"
                :key="index"
                class="bg-surface-page"
                :class="[
                    item.isHighlighted
                        ? 'rounded border-t-4 border-white'
                        : 'border-b border-b-border-primary-light',
                ]"
            >
                <td
                    :class="[
                        'w-[212px] px-16 py-12 font-header font-bold',
                        item.isHighlighted
                            ? 'rounded bg-surface-action-3x-light'
                            : 'bg-surface-grey-2x-light',
                    ]"
                >
                    {{ item.label }}
                </td>
                <td
                    class="w-[212px] px-16 py-12"
                    :class="
                        item.isHighlighted
                            ? 'relative border-b border-border-primary-light font-header font-bold after:absolute after:-top-3 after:left-0 after:h-1 after:w-full after:bg-border-primary-light'
                            : 'font-normal'
                    "
                >
                    <span class="flex items-center justify-between">
                        <Tag
                            :color="
                                item.key === 'spend' ? 'information' : 'action'
                            "
                            class="fs-lg mx-auto font-bold"
                            >{{ tableData[item.key] }}</Tag
                        >
                    </span>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script setup>
import Tag from "../Tag/Tag.vue";

const props = defineProps({
    tableData: { type: Object, required: true },
});

// Define the table rows with labels, keys, and a flag for highlighted rows
const tableRows = [
    { label: "Spend", key: "spend" },
    { label: "Receive", key: "receive" },
];
</script>
