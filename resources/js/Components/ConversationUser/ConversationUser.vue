<template>
    <div
        class="fs-sm ml-auto inline-block rounded-[48px] rounded-br-none bg-surface-action-2x-light px-32 py-12 text-right text-text-body"
    >
        {{ text }}
        <div
            v-if="timeDate"
            class="fs-xxs flex items-center justify-end gap-4 font-light"
        >
            {{ timeDate }}

            <IconDotsVertical />
        </div>
    </div>
</template>

<script setup>
import IconDotsVertical from "../Icons/IconDotsVertical.vue";

defineProps({
    text: String,
    timeDate: String,
});
</script>
