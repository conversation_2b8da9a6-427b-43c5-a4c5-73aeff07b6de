<template>
    <span class="rounded-2xl border px-16 py-2" :class="colorClass">
        <slot></slot>
    </span>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
    color: {
        type: String,
        default: "default",
        validator: (value) => {
            return [
                "default",
                "black",
                "information",
                "success",
                "warning",
                "error",
                "action",
            ].includes(value);
        },
    },
});

const colorClass = computed(() => {
    return {
        "tag--default": props.color === "default",
        "tag--black": props.color === "black",
        "tag--information": props.color === "information",
        "tag--success": props.color === "success",
        "tag--warning": props.color === "warning",
        "tag--error": props.color === "error",
        "tag--action": props.color === "action",
    };
});
</script>
