<template>
    <div
        class="relative inline-flex items-center justify-center gap-8 rounded-[48px] border border-border-primary bg-surface-page px-24 py-8 text-center"
        :class="{ 'mb-48': arrowDown }"
    >
        <slot></slot>
        <IconArrowDown
            v-if="arrowDown"
            class="absolute bottom-0 left-1/2 z-10 -translate-x-1/2 translate-y-full"
        />
    </div>
</template>

<script setup>
import IconArrowDown from "@/Components/Icons/IconArrowDown.vue";

defineProps({ arrowDown: Boolean });
</script>
