<template>
    <div
        class="rounded-[20px] border border-border-action bg-surface-action-3x-light p-24 text-center"
    >
        <h3 class="mb-24 font-bold text-text-grey">
            <Tooltip
                v-if="tooltip"
                :label="tooltip.label"
                :tooltip="tooltip.tooltip"
            />
            <template v-else>
                {{ title }}
            </template>
        </h3>
        <p class="fs-md lg:fs-3xl">
            {{ data }}
        </p>
    </div>
</template>

<script setup>
import Tooltip from "../Tooltip/Tooltip.vue";

defineProps({
    title: {
        type: String,
        required: true,
    },
    data: {
        type: [String, Number],
        required: true,
    },
    tooltip: {
        type: String,
        default: "",
    },
});
</script>
