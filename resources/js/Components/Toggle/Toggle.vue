<template>
    <label :for="id" class="inline-flex cursor-pointer items-center">
        <input
            v-model="proxyChecked"
            type="checkbox"
            :value="value"
            :id="id"
            class="peer sr-only"
            :disabled="disabled"
        />
        <div
            class="peer relative h-20 w-[34px] rounded-full border border-border-black after:absolute after:start-[2px] after:top-[2px] after:h-[14px] after:w-[14px] after:rounded-full after:border after:border-border-black after:bg-surface-page-inv after:transition-all after:content-[''] peer-checked:bg-surface-action-light peer-checked:after:translate-x-full peer-checked:after:border-border-black peer-focus:outline-none peer-focus:ring-1 peer-focus:ring-border-action"
            :class="{
                'border-border-disabled after:border-surface-grey after:bg-surface-grey':
                    disabled,
            }"
        ></div>
        <span class="ms-8" :class="{ 'text-text-disabled': disabled }"
            >{{ label }}
        </span>
    </label>
</template>

<script setup>
import { computed } from "vue";

const emit = defineEmits(["update:checked"]);

const props = defineProps({
    checked: {
        type: [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>],
        default: false,
    },
    value: {
        type: String,
        default: null,
    },
    id: String,
    label: String,
    disabled: Boolean,
});

const proxyChecked = computed({
    get() {
        return props.checked;
    },

    set(val) {
        emit("update:checked", val);
    },
});
</script>
