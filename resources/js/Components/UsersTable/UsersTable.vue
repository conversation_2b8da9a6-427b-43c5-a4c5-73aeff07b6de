<!-- UsersTable.vue -->
<template>
    <div>
        <Table
            v-if="hasUsers"
            :tableData="tableData"
            @action="handleTableAction"
        />
        <p v-else>You have no added users.</p>
    </div>
</template>

<script setup>
import Table from "@/Components/Table/Table.vue";

const props = defineProps({
    tableData: Array,
    hasUsers: Boolean,
});

const emit = defineEmits(["deleteUsers"]);

const handleTableAction = ({ action, row, rowIndex }) => {
    if (action.toLowerCase() === "delete") {
        const confirmed = window.confirm(
            "Are you sure you want to delete this user? This action cannot be undone.",
        );
        if (confirmed) {
            emit("deleteUsers", row.id);
        }
    } else {
        console.log(`Action: ${action} on row ${rowIndex}`, row);
    }
};
</script>
