import UsersTable from "./UsersTable.vue";

export default {
    component: UsersTable,
    tags: ["autodocs"],
    args: {},
};

export const Default = {
    args: {
        hasUsers: true,
        tableData: {
            headings: ["First name", "Last name", "Email", "Role", "Action"],
            rows: [
                {
                    firstName: "Lorem",
                    lastName: "Ipsum",
                    email: "<EMAIL>",
                    role: "Admin",
                    action: "Delete",
                },
                {
                    firstName: "Lorem",
                    lastName: "Ipsum",
                    email: "<EMAIL>",
                    role: "Admin",
                    action: "Delete",
                },
                {
                    firstName: "Lorem",
                    lastName: "Ipsum",
                    email: "<EMAIL>",
                    role: "Admin",
                    action: "Delete",
                },
            ],
        },
    },
};
