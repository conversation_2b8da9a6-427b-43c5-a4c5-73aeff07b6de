<template>
    <footer
        class="mt-auto border-t border-border-primary-light pb-56 pt-32 lg:pb-104"
    >
        <div
            class="container mb-32 flex flex-col items-center justify-center gap-20 md:flex-row md:gap-48 lg:gap-80"
        >
            <Link href="/">
                <IconLogoPink />
            </Link>

            <div
                class="flex flex-col items-center justify-center gap-12 md:flex-row xl:gap-24"
            >
                <Link
                    v-for="(link, index) of navLinks"
                    :key="index"
                    class="px-4 py-4 lg:px-16"
                    :href="link.url"
                >
                    {{ link.text }}
                </Link>
            </div>

            <div class="flex items-center justify-center gap-12 md:gap-24">
                <Link href="/contact-us" class="p-4">
                    <span class="sr-only">Email</span>
                    <IconMail></IconMail>
                </Link>

                <a
                    href="https://uk.linkedin.com/company/pravi-ai"
                    target="_blank"
                    class="p-4"
                >
                    <span class="sr-only">LinkedIn</span>
                    <IconBrandLinkedin></IconBrandLinkedin>
                </a>

                <a
                    href="https://www.instagram.com/pravi.ai/"
                    target="_blank"
                    class="p-4"
                >
                    <span class="sr-only">Instagram</span>
                    <IconBrandInstagram></IconBrandInstagram>
                </a>
            </div>
        </div>

        <div class="mx-auto max-w-2xl text-center">
            <p class="mb-8">© {{ currentYear }} Pravi. All rights reserved</p>
            <p>Pravi Ltd is registered in England and Wales (No. 15897581).</p>
            <p>
                Registered Office: 54, Manor Park, Barnstaple, Devon EX31 2DH.
            </p>
        </div>
    </footer>
</template>

<script setup>
import { ref } from "vue";
import { Link } from "@inertiajs/vue3";
import IconLogoPink from "@/Components/Icons/IconLogoPink.vue";
import IconBrandInstagram from "@/Components/Icons/IconBrandInstagram.vue";
import IconBrandLinkedin from "@/Components/Icons/IconBrandLinkedin.vue";
import IconMail from "@/Components/Icons/IconMail.vue";

defineProps({
    navLinks: Array,
});

const currentYear = ref(new Date().getFullYear());
</script>
