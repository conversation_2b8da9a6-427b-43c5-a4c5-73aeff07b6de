<template>
    <div class="flex items-end gap-16">
        <IconLogoBlack class="hidden md:inline" />
        <div
            class="rounded-[48px] rounded-bl-none bg-surface-grey-2x-light p-24"
            :class="{ 'w-full py-40': fullWidth }"
        >
            <template v-if="text">{{ text }}</template>

            <slot></slot>

            <div
                v-if="timeDate"
                class="fs-sm flex items-center justify-end gap-4"
            >
                {{ timeDate }}

                <IconDotsVertical />
            </div>
        </div>
    </div>
</template>

<script setup>
import IconDotsVertical from "../Icons/IconDotsVertical.vue";
import IconLogoBlack from "../Icons/IconLogoBlack.vue";

defineProps({
    text: String,
    timeDate: String,
    fullWidth: { type: Boolean, default: false },
});
</script>
