<template>
    <div
        :class="{
            'pb-12': helperText || displayedErrorMessage,
            'pb-28 md:pb-32': !helperText && !displayedErrorMessage,
        }"
    >
        <div class="relative">
            <textarea
                ref="input"
                :id="id"
                :name="name"
                class="peer w-full rounded-lg border-border-primary bg-surface-page p-8 text-text-body placeholder-transparent outline-none transition-all hover:border-border-action-hover focus:border-border-focus focus:ring-transparent disabled:border-border-disabled disabled:bg-surface-disabled"
                :class="{
                    'border-border-error hover:border-border-error focus:border-border-error':
                        hasError,
                }"
                v-model="internalValue"
                @blur="validateInput"
                :placeholder="label"
                :disabled="disabled"
                :required="required"
                :rows="rows"
                :maxlength="maxlength"
            />

            <label
                :for="id"
                class="absolute -top-2 left-8 -translate-y-1/2 scale-[0.875] bg-surface-page px-4 font-bold text-text-highlight transition-all peer-placeholder-shown:top-24 peer-placeholder-shown:scale-[1] peer-placeholder-shown:font-normal peer-placeholder-shown:text-text-grey peer-focus:-top-2 peer-focus:scale-[0.875] peer-focus:font-bold peer-focus:text-text-highlight peer-disabled:bg-surface-disabled"
                :class="{
                    'text-text-error peer-focus:text-text-error': hasError,
                }"
            >
                {{ label }}
            </label>
        </div>
        <p
            v-if="helperText || displayedErrorMessage"
            class="fs-xs mt-4"
            :class="{
                'text-text-error': hasError,
                'text-text-grey': !hasError,
            }"
        >
            {{ hasError ? displayedErrorMessage : helperText }}
        </p>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";

// Define props
const props = defineProps({
    modelValue: String,
    name: String,
    id: String,
    label: String,
    disabled: Boolean,
    required: Boolean,
    helperText: String,
    serverError: String,
    rules: {
        type: Array,
        default: () => [],
    },
    rows: String,
    maxlength: [String, Number],
});

// State to manage the current input type
const currentType = ref(props.type);

// V-Model handling
// ========================
// Define emits
const emit = defineEmits(["update:modelValue"]);

// Internal state for input value
const internalValue = ref(props.modelValue);

// Watch internalValue and emit the update when it changes
watch(internalValue, (newValue) => {
    emit("update:modelValue", newValue);
});

// Watch the prop to sync internal state if modelValue changes externally
watch(
    () => props.modelValue,
    (newValue) => {
        internalValue.value = newValue;
    },
);

// Validation handling
// ========================
// Validate input on change
const errorMessage = ref("");
const hasError = computed(() => !!props.serverError || !!errorMessage.value);

// Client side validation
const validateInput = () => {
    errorMessage.value = "";
    for (const rule of props.rules) {
        const validationResult = rule(internalValue.value);
        if (validationResult !== true) {
            errorMessage.value = validationResult;
            break;
        }
    }
};

// This computed property decides which error message to display
// (server error overrides client)
const displayedErrorMessage = computed(() => {
    return props.serverError || errorMessage.value;
});

// Focus handling
// ========================
// Focus input if needed
const input = ref(null);
onMounted(() => {
    if (input.value && input.value.hasAttribute("autofocus")) {
        input.value.focus();
    }
});

// Expose focus method
defineExpose({ focus: () => input.value.focus() });

// Password inputs visibility toggling
// ========================
// State to track password visibility
const isPasswordVisible = ref(false);

// Method to toggle the password visibility
const togglePasswordVisibility = () => {
    isPasswordVisible.value = !isPasswordVisible.value;
    currentType.value = isPasswordVisible.value ? "text" : "password";
};
</script>
