import TextArea from "./TextArea.vue";
import rules from "../../utilities/validation-rules";

export default {
    component: TextArea,
    tags: ["autodocs"],
    argTypes: {},
    args: {
        label: "Your message",
    },
};

export const Default = {
    args: {
        helperText: "Some helper text.",
    },
};

export const Error = {
    args: {
        modelValue: "<EMAIL>",
        type: "email",
        label: "Email",
        serverError: "The email address has already been used.",
        rules: [rules.required, rules.email],
    },
};

export const Disabled = {
    args: {
        type: "email",
        label: "Email",
        disabled: true,
    },
};
