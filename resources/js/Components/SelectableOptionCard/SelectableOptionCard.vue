<template>
    <button
        class="flex w-full max-w-[330px] flex-col rounded-2xl border p-12 text-left shadow transition lg:px-24 lg:pb-24"
        :class="[
            disabled
                ? 'cursor-not-allowed opacity-50'
                : 'hover:border-pink-light hover:bg-surface-action-2x-light',
            isSelected
                ? 'border-pink-light bg-surface-action-2x-light'
                : 'border-surface-grey-light bg-transparent',
        ]"
        :disabled="disabled"
        @click="select"
    >
        <slot></slot>

        <h2 v-if="title" class="my-8 font-bold text-text-headings">
            {{ title }}
        </h2>
        <p v-if="text" class="fs-xs font-light">{{ text }}</p>
    </button>
</template>

<script setup>
import { ref, computed } from "vue";

const props = defineProps({
    title: String,
    text: String,
    disabled: Boolean,
    modelValue: String,
    value: String,
});

const emit = defineEmits(["update:modelValue"]);

const isSelected = computed(() => props.modelValue === props.value);

const select = () => {
    if (props.disabled) return;
    emit("update:modelValue", props.value);
};
</script>
