<template>
    <button
        class="rounded-2xl border border-border-primary p-16 text-left lg:p-40"
    >
        <img
            :src="avatarPath"
            alt=""
            class="mb-24 w-[100px]"
            width="100"
            height="100"
        />

        <h2 class="fs-xl mb-4 flex items-center justify-between font-bold">
            {{ campaign.gender }} {{ campaign.age }}
            <IconChevronRight />
        </h2>

        <p class="mb-24 font-bold text-text-grey">{{ formattedDate }}</p>

        <p class="fs-md font-bold">
            <span v-if="campaign.category"> {{ campaign.category }}</span>
            <span v-if="campaign.subcategory">, </span>
            <span v-if="campaign.subcategory">
                {{ campaign.subcategory }}
            </span>
        </p>
    </button>
</template>

<script setup>
import { computed } from "vue";
import { format } from "date-fns"; // Import date formatting function
import IconChevronRight from "../Icons/IconChevronRight.vue";
import { getDonorAvatarPath } from "@/utilities/helpers";

const { campaign } = defineProps({
    campaign: Object,
});

const avatarPath = computed(() => {
    return getDonorAvatarPath({ GENDER: campaign.gender, AGE: campaign.age });
});

// Format the timestamp to date
const formattedDate = computed(() => {
    return campaign.updated_at
        ? format(new Date(campaign.updated_at), "dd/MM/yy")
        : "";
});
</script>
