<template>
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="49"
        height="48"
        viewBox="0 0 49 48"
        fill="none"
    >
        <g clip-path="url(#clip0_2479_7458)">
            <path
                d="M8.5 12C8.5 10.9391 8.92143 9.92172 9.67157 9.17157C10.4217 8.42143 11.4391 8 12.5 8H36.5C37.5609 8 38.5783 8.42143 39.3284 9.17157C40.0786 9.92172 40.5 10.9391 40.5 12V36C40.5 37.0609 40.0786 38.0783 39.3284 38.8284C38.5783 39.5786 37.5609 40 36.5 40H12.5C11.4391 40 10.4217 39.5786 9.67157 38.8284C8.92143 38.0783 8.5 37.0609 8.5 36V12Z"
                stroke="inherit"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
            <path
                d="M16.5 22V32"
                stroke="inherit"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
            <path
                d="M16.5 16V16.02"
                stroke="inherit"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
            <path
                d="M24.5 32V22"
                stroke="inherit"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
            <path
                d="M32.5 32V26C32.5 24.9391 32.0786 23.9217 31.3284 23.1716C30.5783 22.4214 29.5609 22 28.5 22C27.4391 22 26.4217 22.4214 25.6716 23.1716C24.9214 23.9217 24.5 24.9391 24.5 26"
                stroke="inherit"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
        </g>
        <defs>
            <clipPath id="clip0_2479_7458">
                <rect
                    width="48"
                    height="48"
                    fill="white"
                    transform="translate(0.5)"
                />
            </clipPath>
        </defs>
    </svg>
</template>
