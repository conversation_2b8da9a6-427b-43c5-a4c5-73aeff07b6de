<template>
    <div class="bouncing-loader">
        <div></div>
        <div></div>
        <div></div>
    </div>
</template>

<style>
.bouncing-loader {
    display: flex;
    justify-content: start;
}

.bouncing-loader > div {
    opacity: 0.9;
    width: 8px;
    height: 8px;
    margin: 0 3px 4px;
    background: #202224;
    border-radius: 50%;
    animation: bouncing-loader 0.5s infinite alternate;
}

.bouncing-loader > div:nth-child(2) {
    animation-delay: 0.1666666667s;
}

.bouncing-loader > div:nth-child(3) {
    animation-delay: 0.3333333334s;
}

@keyframes bouncing-loader {
    to {
        opacity: 0.5;
        transform: translate3d(0, -10px, 0);
    }
}
</style>
