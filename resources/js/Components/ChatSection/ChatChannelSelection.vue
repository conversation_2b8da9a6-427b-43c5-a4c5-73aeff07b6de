<template>
    <div class="flex flex-col gap-24">
        <!-- Assistant Response with Channel Selection -->
        <div class="flex flex-col gap-12">
            <div class="flex items-stretch gap-12">
                <div class="bg-[#FAFAFA] rounded-[48px_48px_48px_0px] px-24 py-40 flex-1">
                    <div class="flex flex-col gap-32">
                        <p class="font-body text-16 text-black">Great! Based on your audience and campaign, I recommend these channels. Select the ones you want to use:</p>
                        
                        <!-- Channel Selection -->
                        <div class="bg-white rounded-16 px-24 py-16 flex flex-col gap-8">
                            <p class="font-body text-16 font-bold text-[#202224]">Recommended Channels</p>
                            
                            <div class="flex flex-col gap-12">
                                <!-- Dynamic Channel Rendering -->
                                <div 
                                    v-for="channel in availableChannels" 
                                    :key="channel"
                                    class="flex items-center gap-8 py-7"
                                >
                                    <div 
                                        class="w-24 h-24 border-2 border-black rounded-md flex items-center justify-center cursor-pointer"
                                        :class="{ 'bg-black': selectedChannels.includes(channel) }"
                                        @click="toggleChannel(channel)"
                                    >
                                        <svg v-if="selectedChannels.includes(channel)" width="18" height="18" viewBox="0 0 18 18" fill="none">
                                            <path d="M6 10L8 12L12 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <component :is="getChannelIcon(channel)" class="w-24 h-24" />
                                    <span class="font-body text-16 text-[#21272A]">{{ getChannelDisplayName(channel) }}</span>
                                </div>
                            </div>
                        </div>

                        <p class="font-body text-16 text-black">Would you like to create content for these channels?</p>
                        
                        <!-- Timestamp -->
                        <div class="flex justify-between items-center">
                            <IconRefresh class="w-16 h-16" />
                            <span class="font-body text-10 font-light leading-[1.6] text-[#202224] opacity-80">{{ getCurrentTimestamp() }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, defineEmits, defineProps } from 'vue'
import IconRefresh from '@/Components/Icons/IconRefresh.vue'

// Import Icons directory components for main channels
import IconInstagram from '@/Components/Icons/IconInstagram.vue'
import IconFacebook from '@/Components/Icons/IconFacebook.vue'
import IconTikTok from '@/Components/Icons/IconTikTok.vue'
import IconPinterest from '@/Components/Icons/IconPinterest.vue'
import IconTwitter from '@/Components/Icons/IconTwitter.vue'
import IconYouTube from '@/Components/Icons/IconYouTube.vue'
import IconBrandLinkedin from '@/Components/Icons/IconBrandLinkedin.vue'

// Import all possible channel icons from SocialButtons
import InstagramIcon from '@/Components/SocialButtons/InstagramIcon.vue'
import FacebookIcon from '@/Components/SocialButtons/FacebookIcon.vue'
import TikTokIcon from '@/Components/SocialButtons/TikTokIcon.vue'
import YouTubeIcon from '@/Components/SocialButtons/YouTubeIcon.vue'
import LinkedInIcon from '@/Components/SocialButtons/LinkedInIcon.vue'
import SnapchatIcon from '@/Components/SocialButtons/SnapchatIcon.vue'
import PinterestIcon from '@/Components/SocialButtons/PinterestIcon.vue'
import TwitterIcon from '@/Components/SocialButtons/TwitterIcon.vue'

const props = defineProps({
    availableChannels: {
        type: Array,
        default: () => ['instagram', 'facebook', 'tiktok']
    }
})

const emit = defineEmits(['channels-selected'])

const selectedChannels = ref([]) // Start with no channels selected

const getCurrentTimestamp = () => {
    return new Date().toLocaleString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
    })
}

const toggleChannel = (channel) => {
    const index = selectedChannels.value.indexOf(channel)
    if (index > -1) {
        selectedChannels.value.splice(index, 1)
    } else {
        selectedChannels.value.push(channel)
    }
    
    // Emit the selected channels to parent
    emit('channels-selected', selectedChannels.value)
}

const getChannelIcon = (channel) => {
    // First try Icons directory components (these work better for UI)
    const iconMap = {
        'instagram': IconInstagram,
        'facebook': IconFacebook,
        'tiktok': IconTikTok,
        'pinterest': IconPinterest,
        'twitter': IconTwitter,
        'x': IconTwitter,
        'youtube': IconYouTube,
        'linkedin': IconBrandLinkedin,
    }
    
    // Fallback to SocialButtons components for other channels
    const socialIconMap = {
        'linkedin': LinkedInIcon,
        'snapchat': SnapchatIcon,
        'pinterest': PinterestIcon,
        'twitter': TwitterIcon,
        'x': TwitterIcon
    }
    
    const channelLower = channel.toLowerCase()
    return iconMap[channelLower] || socialIconMap[channelLower] || IconInstagram
}

const getChannelDisplayName = (channel) => {
    const nameMap = {
        'instagram': 'Instagram',
        'facebook': 'Facebook',
        'tiktok': 'TikTok',
        'youtube': 'YouTube',
        'linkedin': 'LinkedIn',
        'snapchat': 'Snapchat',
        'pinterest': 'Pinterest',
        'twitter': 'X (Twitter)',
        'x': 'X (Twitter)'
    }
    
    return nameMap[channel.toLowerCase()] || channel.charAt(0).toUpperCase() + channel.slice(1)
}
</script> 