<template>
    <div class="px-24 pb-24">
        <div v-if="showQuickActions" class="flex gap-8 mb-16">
            <button 
                @click="handlePromptClick('Recommend social media channels')" 
                class="w-[197px] py-12 px-12 border border-[#DCDDDD] rounded-lg text-lg text-black font-body font-normal text-center bg-[#F3F3F4] hover:bg-[#EAEAEB] transition-colors"
            >
                Recommend social media channels
            </button>
            <button 
                @click="handlePromptClick('Create content for my campaign')" 
                class="w-[197px] py-12 px-12 border border-[#DCDDDD] rounded-lg text-lg text-black font-body font-normal text-center bg-[#F3F3F4] hover:bg-[#EAEAEB] transition-colors"
            >
                Create content for my campaign
            </button>
        </div>

        <div class="relative w-full h-48">
            <input 
                v-model="userInput" 
                @keyup.enter="handleSendMessage"
                type="text" 
                :placeholder="placeholder" 
                class="w-full h-full px-20 py-12 border border-[#DCDDDD] rounded-xl focus:outline-none focus:border-text-action focus:ring-1 focus:ring-text-action text-text-grey bg-white font-header text-lg pr-56"
            >
            <button 
                @click="handleSendMessage"
                class="absolute right-12 top-1/2 transform -translate-y-1/2 bg-pink-500 text-white rounded-full w-32 h-32 flex items-center justify-center hover:bg-pink-dark transition-colors shadow-sm"
                :disabled="!userInput.trim() || isLoading"
            >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-16 h-16">
                    <path d="M12 5V19M12 5L6 11M12 5L18 11" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
    isLoading: {
        type: Boolean,
        default: false
    },
    placeholder: {
        type: String,
        default: 'What would you like to create?'
    },
    showQuickActions: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['send-message', 'insert-prompt'])

const userInput = ref('')

const handleSendMessage = () => {
    if (!userInput.value.trim() || props.isLoading) return
    
    emit('send-message', userInput.value)
    userInput.value = ''
}

const handlePromptClick = (prompt) => {
    userInput.value = prompt
    emit('insert-prompt', prompt)
}
</script> 