<template>
    <div class="flex items-center justify-between pl-24 pt-24 pr-24">
        <button
            @click.prevent="router.get('/build/audience-result')"
            class="flex items-center gap-4 py-8 font-header text-lg font-bold text-text-body"
        >
            <IconArrowLeftBold class="w-16 h-16" />
            Back to Audience
        </button>
        
        <div class="flex items-center gap-12">
            <IconAssistant class="w-24 h-24 text-black" />
            <h3 class="font-header font-extrabold text-lg text-black">Assistant</h3>
        </div>
    </div>
</template>

<script setup>
import IconAssistant from '@/Components/Icons/IconAssistant.vue'
import IconArrowLeftBold from '@/Components/Icons/IconArrowLeftBold.vue'
import { router } from '@inertiajs/vue3'
</script> 