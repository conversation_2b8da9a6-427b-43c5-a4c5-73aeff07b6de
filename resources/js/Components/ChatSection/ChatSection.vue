<template>
    <div class="w-[466px] h-[679px] flex flex-col border border-[#DCDDDD] rounded-xl overflow-hidden bg-white shadow-[0px_4px_4px_0px_rgba(0,0,0,0.25)]">
        <div class="flex flex-col gap-64">
            <ChatHeader />
            <ChatGreeting />
        </div>
        <div class="flex-grow"></div>
        <ChatInput 
            :isLoading="isLoading"
            @send-message="handleSendMessage"
            @insert-prompt="handleInsertPrompt"
        />
    </div>
</template>

<script setup>
import ChatHeader from './ChatHeader.vue'
import ChatGreeting from './ChatGreeting.vue'
import ChatInput from './ChatInput.vue'

defineProps({
    isLoading: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['send-message', 'insert-prompt'])

const handleSendMessage = (message) => {
    emit('send-message', message)
}

const handleInsertPrompt = (prompt) => {
    emit('insert-prompt', prompt)
}
</script> 