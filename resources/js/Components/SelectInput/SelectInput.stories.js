import SelectInput from "./SelectInput.vue";
// import rules from "../../utilities/validation-rules";

export default {
    component: SelectInput,
    tags: ["autodocs"],

    args: {
        label: "Your role",
        modelValue: "",
    },
};

export const Default = {
    args: {
        options: [
            { id: 0, name: "Fundraiser" },
            { id: 1, name: "Marketer" },
            { id: 2, name: "SEO / Executive" },
            { id: 3, name: "Consultant" },
            { id: 4, name: "Other" },
        ],
    },
};

export const Disabled = {
    args: {
        disabled: true,
    },
};
