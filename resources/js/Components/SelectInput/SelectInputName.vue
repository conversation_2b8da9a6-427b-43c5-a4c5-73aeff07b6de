<template>
    <div
        :class="{
            'pb-12': helperText || displayedErrorMessage,
            'pb-28 md:pb-32': !helperText && !displayedErrorMessage,
        }"
    >
        <div class="relative">
            <label
                v-if="labelPosition === 'outside'"
                :for="id"
                class="mb-8 inline-block font-bold"
            >
                {{ label }}
            </label>
            <select
                ref="select"
                class="peer h-48 w-full rounded-lg border-border-primary bg-surface-page p-8 text-text-body placeholder-transparent outline-none transition-all hover:border-border-action-hover focus:border-border-focus focus:ring-transparent disabled:border-border-disabled disabled:bg-surface-disabled"
                :value="modelValue"
                @input="$emit('update:modelValue', $event.target.value)"
                :disabled="disabled"
                @blur="validateInput"
            >
                <option
                    v-for="option in options"
                    :value="option.name"
                    :key="option.key ?? option.id"
                >
                    {{ option.name }}
                </option>
            </select>
            <label
                v-if="labelPosition === 'inside'"
                :for="id"
                class="absolute left-8 -translate-y-1/2 bg-surface-page px-4 text-text-grey transition-all peer-focus:-top-2 peer-focus:scale-[0.875] peer-focus:font-bold peer-focus:text-text-highlight peer-disabled:bg-surface-disabled"
                :class="{
                    'text-text-error peer-focus:text-text-error': hasError,
                    '-top-2 scale-[0.875] font-bold text-text-highlight':
                        modelValue !== null,
                    'top-1/2 scale-[1] font-normal': modelValue === null,
                }"
            >
                {{ label }}
            </label>
        </div>
        <p
            v-if="helperText || displayedErrorMessage"
            class="fs-xs mt-4"
            :class="{
                'text-text-error': hasError,
                'text-text-grey': !hasError,
            }"
        >
            {{ hasError ? displayedErrorMessage : helperText }}
        </p>
    </div>
</template>

<script setup>
import { onMounted, ref, computed } from "vue";

const props = defineProps({
    modelValue: [Number, String],
    options: {
        type: Array,
        required: true,
    },
    label: {
        type: String,
        required: true,
    },
    id: {
        type: String,
        required: true,
    },
    serverError: String,
    rules: {
        type: Array,
        default: () => [],
    },
    labelPosition: {
        type: String,
        default: "inside",
    },
    disabled: {
        type: Boolean,
        required: false,
    },
});

defineEmits(["update:modelValue"]);

const select = ref(null);

onMounted(() => {
    if (select.value.hasAttribute("autofocus")) {
        select.value.focus();
    }
});

defineExpose({ focus: () => select.value.focus() });

// Validation handling
// ========================
// Validate input on change
const errorMessage = ref("");
const hasError = computed(() => !!props.serverError || !!errorMessage.value);

// Client side validation
const validateInput = () => {
    errorMessage.value = "";
    for (const rule of props.rules) {
        const validationResult = rule(internalValue.value);
        if (validationResult !== true) {
            errorMessage.value = validationResult;
            break;
        }
    }
};

// This computed property decides which error message to display
// (server error overrides client)
const displayedErrorMessage = computed(() => {
    return props.serverError || errorMessage.value;
});
</script>
