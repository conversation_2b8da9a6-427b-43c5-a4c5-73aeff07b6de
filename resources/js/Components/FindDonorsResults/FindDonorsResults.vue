<template>
    <div v-if="!hasResults" class="p-24">
        <p class="mb-12">
            No audience result found. Try adjusting your search parameters and
            refreshing the prediction.
        </p>
    </div>

    <div v-else class="p-24">
        <TabsRadio
            :tabs="tabs"
            v-model="findDonorsStore.shownResultIndex"
            @tab-click="onTabClick"
            :showDot="false"
            fullWidth
        />

        <div
            v-if="findDonorsStore.shownResultIndex === 0"
            class="min-h-[500px] bg-surface-page p-16"
        >
            <h2
                v-if="findDonorsStore.isCustomPrediction"
                class="mb-8 mt-8 font-bold"
            >
                Your selection:
            </h2>
            <div
                class="flex flex-col items-center gap-24 lg:flex-row xl:gap-40"
            >
                <div class="">
                    <img
                        :src="avatarPath"
                        alt=""
                        class="mx-auto mb-24 w-[100px] rounded-full ring-2 ring-icon-action lg:w-[150px]"
                        width="100"
                        height="100"
                    />
                    <TablePersona
                        v-if="tableDataArray[0]"
                        :tableData="tableDataArray[0]"
                        class="max-w-[550px] flex-1"
                    />
                </div>

                <div>
                    <TableAffinity
                        v-if="findDonorsStore.resultsAffinity"
                        :tableData="findDonorsStore.resultsAffinity"
                        class="max-w-[688px]"
                    />

                    <div class="mt-12 flex gap-12">
                        <DataCard
                            v-if="tableDataArray[0]"
                            class="flex-1"
                            title="Data Confidence"
                            :data="tableDataArray[0].affinity"
                            :tooltip="{
                                label: 'Data Confidence',
                                tooltip:
                                    'This measures how much data Pravi currently has to support these predictions.',
                                IconClass: 'stroke-text-grey',
                            }"
                        />
                        <DataCard
                            v-if="tableDataArray[0]"
                            class="flex-1"
                            title="Prediction donation"
                            :data="tableDataArray[0].predicted_donation"
                        />
                        <DataCard
                            v-if="tableDataArray[0]"
                            class="flex-1"
                            title="Average annual donations"
                            :data="tableDataArray[0].likely_to_give_regularly"
                        />
                    </div>
                </div>
            </div>

            <p class="fs-xxs mt-12 leading-5">
                Pravi uses anonymous UK donation data to make predictions. Your
                best match is based on how many people donate as well as how
                much, how recently and how often they give.
            </p>

            <Button
                v-if="findDonorsStore.isCustomPrediction"
                @click="showBestMatch"
                color="action"
                :disabled="findDonorsStore.isLoading"
                class="mt-16"
            >
                Show me Pravi's Best Match
            </Button>
        </div>

        <div v-else class="min-h-[500px] bg-surface-page p-16">
            <DonutChartAffinity
                v-if="findDonorsStore.shownResultIndex === 1"
                :chartData="genderChartData"
                width="60%"
                @barClicked="handleBarClicked"
                chartType="GENDER"
                :hasResults="hasResults"
                @refreshPredictions="refreshPredictions"
            />
            <ColumnChartAffinity
                v-if="findDonorsStore.shownResultIndex === 2"
                :chartData="ageChartData"
                width="100%"
                @barClicked="handleBarClicked"
                chartType="AGE"
                :hasResults="hasResults"
                @refreshPredictions="refreshPredictions"
            />
            <ColumnChartAffinity
                v-if="findDonorsStore.shownResultIndex === 3"
                :chartData="salaryChartData"
                width="100%"
                @barClicked="handleBarClicked"
                chartType="SALARY"
                :hasResults="hasResults"
                @refreshPredictions="refreshPredictions"
            />

            <p class="fs-xxs mt-12 leading-5">
                Pravi uses anonymous UK donation data to make predictions. Your
                best match is based on how many people donate as well as how
                much, how recently and how often they give.
            </p>
        </div>

        <div
            v-if="findDonorsStore.shownResultIndex === 0"
            class="flex flex-col items-center gap-16 sm:flex-row"
        >
            <ButtonLink href="/campaigns" class="h-40">
                Use saved audience
            </ButtonLink>

            <!-- <Button
                    v-if="hasResults && findDonorsStore.shownResultIndex === 0"
                    @click="handleSelectDonors"
                    color="action"
                    :disabled="findDonorsStore.isLoading"
                    class="h-40"
                >
                    Create Campaign
                </Button> -->

            <Button
                v-if="hasResults && hasSaveButton"
                @click="handleSave"
                color="action"
                :disabled="findDonorsStore.isLoading || isSaved"
                class="ml-auto h-40"
            >
                {{ isSaved ? "Saved" : "Save Audience" }}
            </Button>
        </div>
    </div>

    <!-- <Feedback v-if="hasResults" class="mt-16 lg:float-right" /> -->

    <Toast
        v-if="toast.message"
        :text="toast.message"
        :type="toast.type"
        @handleClose="toast.message = ''"
    ></Toast>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from "vue";
import { useFindDonorsStore } from "@/stores/findDonors";
import Button from "../Button/Button.vue";
import ConversationPravi from "../ConversationPravi/ConversationPravi.vue";
import TablePersona from "../TablePersona/TablePersona.vue";
import TabsRadio from "../Tabs/TabsRadio.vue";
import Feedback from "../Feedback/Feedback.vue";
import Spinner from "../Spinner/Spinner.vue";
import TableAffinity from "../TableAffinity/TableAffinity.vue";
import ColumnChartAffinity from "../ColumnChartAffinity/ColumnChartAffinity.vue";
import { submitFindDonors, saveCampaign } from "@/utilities/api";
import { getDonorAvatarPath } from "@/utilities/helpers";
import Toast from "../Toast/Toast.vue";
import DataCard from "../DataCard/DataCard.vue";
import DonutChartAffinity from "../DonutChartAffinity/DonutChartAffinity.vue";
import ButtonLink from "../ButtonLink/ButtonLink.vue";

const emit = defineEmits(["handlePreviousStep", "handleNextStep"]);

const findDonorsStore = useFindDonorsStore();
const {
    setIsLoading,
    setShownResultIndex,
    setSelectedDonor,
    setResultsAffinity,
} = findDonorsStore;

const props = defineProps({
    socialMediaChannels: Object,
    communicationChannels: Object,
    fundraisingCampaigns: Object,
    hasSaveButton: Boolean,
});

const tabs = ref([]);
const tableDataArray = ref([]);

function onTabClick(tab) {
    setShownResultIndex(tab.value);
    findDonorsStore.clearFormPersonaData();
}

const hasResults = computed(
    () =>
        findDonorsStore.donorsResults &&
        findDonorsStore.donorsResults.length > 0,
);

const mapResultsToTableDataArray = (results) => {
    if (!hasResults.value) return;
    tableDataArray.value.length = 0;
    results.forEach((result) => {
        const tableDataItem = {
            gender: result.GENDER,
            age: result.AGE,
            location: result.LOCATION,
            areaOfFocus: findDonorsStore.form.AREA_OF_FOCUS,
            salary: result.SALARY,
            affinity: result.AFFINITY,
            predicted_donation: result.PREDICTED_AVERAGE_DONATION,
            likely_to_give_regularly: result.LIKELIHOOD_TO_GIVE_REGULARLY,
        };
        tableDataArray.value.push(tableDataItem);
    });
};

const generateTabsFromResults = () => {
    if (!hasResults.value) return;
    tabs.value.length = 0;
    if (findDonorsStore.donorsResults[0]) {
        tabs.value.push({
            name: "Best match",
            value: 0,
            disabled: false,
        });
    }
    if (findDonorsStore.resultsGender) {
        tabs.value.push({
            name: "Gender",
            value: 1,
            disabled: false,
        });
    }
    if (findDonorsStore.resultsAge) {
        tabs.value.push({
            name: "Age",
            value: 2,
            disabled: false,
        });
    }
    if (findDonorsStore.resultsSalary) {
        tabs.value.push({
            name: "Salary",
            value: 3,
            disabled: false,
        });
    }
};

/**
 * updateAffinityData() computes the affinity data from the first donor in donorsResults,
 * then calls the store setter to save it as resultsAffinity.
 */
function updateAffinityData() {
    const donor = findDonorsStore.donorsResults
        ? findDonorsStore.donorsResults[0]
        : null;
    if (!donor || !donor.AGE || !donor.GENDER) {
        setResultsAffinity({
            social_media: "",
            communication: "",
            fundraising: "",
        });
        return;
    }
    const age = donor.AGE;
    const genderKey = donor.GENDER.toLowerCase();

    const socialMediaArray =
        props.socialMediaChannels[age] &&
        props.socialMediaChannels[age][genderKey]
            ? props.socialMediaChannels[age][genderKey]
            : [];
    const communicationArray =
        props.communicationChannels[age] &&
        props.communicationChannels[age][genderKey]
            ? props.communicationChannels[age][genderKey]
            : [];
    const fundraisingArray =
        props.fundraisingCampaigns[age] &&
        props.fundraisingCampaigns[age][genderKey]
            ? props.fundraisingCampaigns[age][genderKey]
            : [];

    setResultsAffinity({
        social_media: socialMediaArray.join(", "),
        communication: communicationArray.join(", "),
        other_causes: findDonorsStore.donorsResults[0]?.CROSS_INTEREST,
        brands: findDonorsStore.donorsResults[0]?.BRAND_INTEREST,
        fundraising: fundraisingArray.join(", "),
    });
}

onMounted(() => {
    mapResultsToTableDataArray(findDonorsStore.donorsResults);
    generateTabsFromResults();
    updateAffinityData();
});

watch(
    () => findDonorsStore.donorsResults,
    (newValue, oldValue) => {
        if (newValue !== oldValue) {
            mapResultsToTableDataArray(newValue);
            generateTabsFromResults();
            updateAffinityData();
        }
    },
    { deep: true },
);

//
// Affinity avatars
// ==========================================================================

const avatarPath = computed(() => {
    const donor = findDonorsStore.donorsResults
        ? findDonorsStore.donorsResults[0]
        : null;

    return getDonorAvatarPath(donor);
});

// --------------------------
// Chart Data Computed Props
// --------------------------
const ageChartData = computed(() => {
    const results = findDonorsStore.resultsAge;
    if (!results || !results.length) {
        return {
            title: "Donor distribution by age",
            data: [],
            categories: [],
            selected: null,
        };
    }
    const firstDonorAge = findDonorsStore.donorsResults[0]?.AGE || null;
    return {
        title: "Donor distribution by age",
        data: results.map((r) => r.AGE_BAND),
        categories: results.map((r) => r.COUNT),
        selected: firstDonorAge,
    };
});

const genderChartData = computed(() => {
    const results = findDonorsStore.resultsGender;
    if (!results || !results.length) {
        return {
            title: "Donor distribution by gender",
            data: [],
            categories: [],
            selected: null,
        };
    }
    const firstDonorGender = findDonorsStore.donorsResults[0]?.GENDER || null;
    return {
        title: "Donor distribution by gender",
        data: results.map((r) => r.DERIVED_GENDER),
        categories: results.map((r) => r.COUNT),
        selected: firstDonorGender,
    };
});

const salaryChartData = computed(() => {
    const results = findDonorsStore.resultsSalary;
    if (!results || !results.length) {
        return {
            title: "Donor distribution by salary",
            data: [],
            categories: [],
            selected: null,
        };
    }
    const firstDonorSalary = findDonorsStore.donorsResults[0]?.SALARY || null;
    return {
        title: "Donor distribution by salary",
        data: results.map((r) => r.SALARY_BAND),
        categories: results.map((r) => r.COUNT),
        selected: firstDonorSalary,
    };
});

const handleBarClicked = (clickedValue, chartType) => {
    findDonorsStore.updateForm({ [chartType]: clickedValue });
};

// --------------------------
// Final action submit handlers
// --------------------------
const errors = ref({});

const handleSuccess = () => {
    mapResultsToTableDataArray(findDonorsStore.donorsResults);
    generateTabsFromResults();
    updateAffinityData();
    setIsLoading(false);
};

const handleError = (errorData) => {
    errors.value = errorData;
    console.error("Error refreshing predictions:", errorData);
    setIsLoading(false);
};

const refreshPredictions = async () => {
    setIsLoading(true);
    await submitFindDonors(findDonorsStore.form, handleSuccess, handleError);
    findDonorsStore.setIsCustomPrediction(true);
    onTabClick({ value: 0 });
};

const showBestMatch = async () => {
    findDonorsStore.clearFormPersonaData();
    setIsLoading(true);
    await submitFindDonors(findDonorsStore.form, handleSuccess, handleError);
    findDonorsStore.setIsCustomPrediction(false);
    onTabClick({ value: 0 });
};

const handleSelectDonors = () => {
    // Always use the first donor in the list.
    setSelectedDonor(findDonorsStore.donorsResults[0]);
    emit("handleNextStep");
};

// --------------------------
// Save Campaign handlers
// --------------------------

const isSaved = ref(false);

const toast = ref({
    message: "",
    type: "",
});

const handleSave = async () => {
    await saveCampaign();
    isSaved.value = true;
    toast.value = {
        message: "Audience successfully saved",
        type: "success",
    };
};
</script>
