<template>
    <main
        class="w-full overflow-auto bg-surface-grey-2x-light p-16 pt-80 md:px-32 md:py-24"
    >
        <div
            class="min-h-full w-full rounded-2xl bg-surface-page px-24 py-24 md:px-40"
        >
            <TabsNav :tabs="tabs" />

            <slot></slot>
        </div>
    </main>
</template>

<script setup>
import TabsNav from "@/Components/Tabs/TabsNav.vue";

const tabs = [
    {
        name: "Profile",
        href: "/settings/profile",
        disabled: false,
    },
    {
        name: "Notifications",
        href: "/settings/notifications",
        disabled: false,
    },
    {
        name: "Subscription",
        href: "/settings/subscription",
        disabled: false,
    },
    // {
    //     name: "Integrations",
    //     href: "/settings/integrations",
    //     disabled: false,
    // },
    {
        name: "Help",
        href: "/settings/help",
        disabled: false,
    },
];
</script>
