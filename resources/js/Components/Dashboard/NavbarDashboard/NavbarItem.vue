<template>
    <li v-if="isListItem">
        <component
            :is="isLink ? Link : 'button'"
            :href="href"
            :method="method"
            @click="onClick"
            class="flex items-center gap-8 rounded-[80px] p-12 transition hover:bg-surface-action-hover-extra-light md:p-16"
            :class="{
                'bg-surface-action-hover-extra-light font-bold': active,
            }"
        >
            <slot name="icon"></slot>
            <span v-show="showFull"><slot></slot></span>
        </component>
    </li>

    <component
        v-else
        :is="isLink ? Link : 'button'"
        :href="href"
        :method="method"
        @click="onClick"
        class="flex items-center gap-8 rounded-[80px] p-12 transition hover:bg-surface-action-hover-extra-light md:p-16"
        :class="{ 'bg-surface-action-hover-extra-light font-bold': active }"
    >
        <slot name="icon"></slot>
        <span v-show="showFull"><slot></slot></span>
    </component>
</template>

<script setup>
import { Link } from "@inertiajs/vue3";

defineProps({
    showFull: Boolean,
    isLink: { type: Boolean, default: true },
    href: { type: String, default: "" },
    method: { type: String, default: "get" },
    onClick: { type: Function, default: null },
    isListItem: { type: Boolean, default: true },
    active: Boolean,
});
</script>
