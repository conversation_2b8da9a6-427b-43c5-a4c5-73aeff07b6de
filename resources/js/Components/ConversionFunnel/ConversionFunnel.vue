<template>
    <div class="relative mx-auto flex max-w-[700px] flex-col items-center">
        <!-- Step 1 -->
        <div
            class="relative mt-12 flex w-full flex-wrap justify-center gap-16 rounded-2xl border border-border-action-hover bg-surface-action-2x-light py-12 text-center"
        >
            <Button
                v-for="(btnLabel, index) in awarenessButtons"
                :key="index"
                @click="handleButton('awareness')"
                class="group h-56"
            >
                <component
                    :is="getIconComponent(btnLabel)"
                    class="mr-12 w-24 stroke-black transition group-hover:stroke-white"
                />
                {{ btnLabel }}</Button
            >
        </div>
        <IconArrowBadgeDown />

        <!-- Step 2 -->
        <div
            class="relative w-10/12 rounded-2xl border border-border-action-hover bg-surface-action-2x-light py-12 text-center"
        >
            <Button @click="handleButton('leadCapture')" class="group h-56">
                <UsersIcon
                    class="mr-12 stroke-black transition group-hover:stroke-white"
                />
                Lead capture</Button
            >
        </div>
        <IconArrowBadgeDown />

        <!-- Step 3 -->
        <div
            class="relative flex w-8/12 flex-wrap justify-center gap-16 rounded-2xl border border-border-action-hover bg-surface-action-2x-light py-12 text-center"
        >
            <Button
                v-for="(btnLabel, index) in stewardshipButtons"
                :key="index"
                @click="handleButton('stewardship')"
                class="group h-56"
            >
                <component
                    :is="getIconComponent(btnLabel)"
                    class="mr-12 w-24 stroke-black transition group-hover:stroke-white"
                />
                {{ btnLabel }}</Button
            >
        </div>
        <IconArrowBadgeDown />

        <!-- Step 4 -->
        <div
            class="relative w-6/12 rounded-2xl border border-border-action-hover bg-surface-action-2x-light py-12 text-center"
        >
            <Button @click="handleButton('campaigns')" class="group h-56">
                <HeartIcon
                    class="mr-12 stroke-black transition group-hover:stroke-white"
                />
                Campaigns</Button
            >
        </div>
    </div>
</template>

<script setup>
import Button from "../Button/Button.vue";
import IconArrowBadgeDown from "../Icons/IconArrowBadgeDown.vue";
import DirectMailIcon from "../SocialButtons/DirectMailIcon.vue";
import EmailIcon from "../SocialButtons/EmailIcon.vue";
import FacebookIcon from "../SocialButtons/FacebookIcon.vue";
import HeartIcon from "../SocialButtons/HeartIcon.vue";
import InstagramIcon from "../SocialButtons/InstagramIcon.vue";
import LinkedInIcon from "../SocialButtons/LinkedInIcon.vue";
import PhoneCallIcon from "../SocialButtons/PhoneCallIcon.vue";
import PinterestIcon from "../SocialButtons/PinterestIcon.vue";
import SMSIcon from "../SocialButtons/SMSIcon.vue";
import SnapchatIcon from "../SocialButtons/SnapchatIcon.vue";
import SocialDMsIcon from "../SocialButtons/SocialDMsIcon.vue";
import TikTokIcon from "../SocialButtons/TikTokIcon.vue";
import UsersIcon from "../SocialButtons/UsersIcon.vue";
import WhatsAppIcon from "../SocialButtons/WhatsAppIcon.vue";
import YouTubeIcon from "../SocialButtons/YouTubeIcon.vue";

const props = defineProps({
    awarenessButtons: Array,
    stewardshipButtons: Array,
});

const emit = defineEmits(["openModal"]);

const handleButton = (name) => {
    emit("openModal", name);
};

const iconMapping = {
    Facebook: FacebookIcon,
    Instagram: InstagramIcon,
    YouTube: YouTubeIcon,
    TikTok: TikTokIcon,
    Snapchat: SnapchatIcon,
    Pinterest: PinterestIcon,
    LinkedIn: LinkedInIcon,
    "Social Media DMs": SocialDMsIcon,
    SMS: SMSIcon,
    Email: EmailIcon,
    WhatsApp: WhatsAppIcon,
    "Direct Mail": DirectMailIcon,
    "Phone Call": PhoneCallIcon,
};

const getIconComponent = (label) => {
    return iconMapping[label] || null;
};
</script>
