<template>
    <nav
        class="max-w-screen-2xl bg-surface-page px-24 py-16 md:flex md:items-center md:gap-16 xl:gap-24"
    >
        <div class="flex items-center justify-between">
            <Link href="/">
                <IconLogoText class="w-88 lg:w-[129px]" />
            </Link>

            <button
                @click.prevent="isOpen = !isOpen"
                type="button"
                class="inline-flex h-32 w-32 items-center justify-center rounded-lg p-2 focus:outline-none focus:ring-2 md:hidden"
                aria-controls="main-menu"
                :aria-expanded="isOpen"
            >
                <span class="sr-only">Open main menu</span>
                <IconHamburger />
            </button>
        </div>

        <div
            class="mt-16 w-full flex-col items-center gap-16 rounded-2xl bg-pink-3x-extra-light p-16 md:mt-0 md:flex md:flex-row md:bg-transparent md:p-0 lg:gap-24"
            :class="{ flex: isOpen, hidden: !isOpen }"
            id="main-menu"
        >
            <Link
                v-for="(link, index) of links"
                :key="index"
                :href="link.url"
                class="px-16 py-8 text-lg md:px-4 md:py-4 lg:px-16"
            >
                {{ link.text }}
            </Link>

            <template v-if="$page.props.auth.user">
                <Link
                    href="/build/index"
                    class="btn btn--md btn--default mb-8 md:mb-0 md:ml-auto"
                >
                    My Dashboard
                </Link>

                <Button @click.prevent="logout" color="action">Logout</Button>
            </template>

            <template v-else>
                <ButtonLink href="/login" class="mb-8 md:mb-0 md:ml-auto">
                    Login
                </ButtonLink>

                <Button @click="$emit('handleJoinWaitlist')" class="">
                    Join Newsletter
                </Button>

                <ButtonLink href="/#plans" color="action"
                    >Try For Free</ButtonLink
                >
            </template>
        </div>
    </nav>
</template>

<script setup>
import { ref, computed } from "vue";
import IconLogoText from "../Icons/IconLogoText.vue";
import IconHamburger from "../Icons/IconHamburger.vue";
import { Link, router } from "@inertiajs/vue3";
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";
import Button from "@/Components/Button/Button.vue";

defineEmits(["handleJoinWaitlist"]);

const isOpen = ref(false);

const links = [
    { text: "Home", url: "/" },
    { text: "Price", url: "/#plans" },
    { text: "FAQ", url: "/faq" },
    { text: "Blog", url: "/blog" },
    { text: "Contact us", url: "/contact-us" },
];

const stripeCustomerPortalLink = computed(
    () => import.meta.env.VITE_STRIPE_CUSTOMER_PORTAL_LINK,
);

const logout = () => {
    router.post(route("logout"));
};
</script>
