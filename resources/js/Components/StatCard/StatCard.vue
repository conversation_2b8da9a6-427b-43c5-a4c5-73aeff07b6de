<template>
    <div class="rounded-3xl bg-surface-page p-12 px-16 shadow md:px-24">
        <h2 class="fs-md mb-8 font-normal text-text-grey">{{ title }}</h2>

        <div class="flex items-end justify-between gap-16">
            <span class="fs-3xl font-header font-bold text-text-headings">
                {{ value }}
            </span>

            <Tag :color="tagColor">{{ changePercentage }}</Tag>
        </div>
    </div>
</template>

<script setup>
import Tag from "../Tag/Tag.vue";

defineProps({
    title: String,
    value: String,
    changePercentage: String,
    tagColor: String,
});
</script>
