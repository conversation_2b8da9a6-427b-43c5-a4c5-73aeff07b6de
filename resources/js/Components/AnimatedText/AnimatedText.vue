<template>
    <div v-html="displayedText"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";

const props = defineProps({
    text: {
        type: String,
        required: true,
    },
    speed: {
        type: Number,
        default: 10,
    },
    htmlMode: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(["animation-complete"]);

const displayedText = ref("");
let animationInterval = null;

const animateText = () => {
    const textToAnimate = props.text || "";

    // Split the text into words while preserving HTML tags
    const words = [];
    let currentWord = "";
    let inTag = false;

    for (let i = 0; i < textToAnimate.length; i++) {
        const char = textToAnimate[i];

        // Check if we're inside an HTML tag
        if (char === "<") {
            inTag = true;

            // If we have accumulated a word before this tag, push it
            if (currentWord) {
                words.push(currentWord);
                currentWord = "";
            }

            currentWord += char;
        } else if (char === ">") {
            inTag = false;
            currentWord += char;

            // Push complete HTML tag as a single "word"
            words.push(currentWord);
            currentWord = "";
        } else if (inTag) {
            // Inside tag, just keep accumulating
            currentWord += char;
        } else if (char === " " || char === "\n") {
            // Space or newline outside tag marks word boundary
            if (currentWord) {
                words.push(currentWord);
            }

            // Keep the space/newline as a separate "word" to maintain formatting
            words.push(char);
            currentWord = "";
        } else {
            // Regular character outside tag
            currentWord += char;
        }
    }

    // Add the last word if any
    if (currentWord) {
        words.push(currentWord);
    }

    // Now animate word by word
    let currentIndex = 0;
    displayedText.value = "";

    animationInterval = setInterval(() => {
        if (currentIndex < words.length) {
            displayedText.value += words[currentIndex];
            currentIndex++;
        } else {
            clearInterval(animationInterval);
            emit("animation-complete");
        }
    }, props.speed * 5); // Slightly longer delay for words vs characters
};

// Start animation when component is mounted
onMounted(() => {
    animateText();
});

// Clean up interval when component is unmounted
onBeforeUnmount(() => {
    if (animationInterval) {
        clearInterval(animationInterval);
    }
});
</script>
