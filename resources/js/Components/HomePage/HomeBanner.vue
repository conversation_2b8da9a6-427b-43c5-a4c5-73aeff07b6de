<template>
    <header
        class="grid min-h-[570px] place-items-center bg-home-banner bg-cover bg-center bg-no-repeat"
    >
        <div
            class="container flex flex-col items-center justify-between md:gap-40 lg:flex-row"
        >
            <div class="max-w-[610px] space-y-24 py-40">
                <h1 class="fs-5xl grow text-center font-extrabold md:text-left">
                    Find new donors<br />
                    to give to your cause
                </h1>

                <p>
                    Pravi helps you find and convert new donors without needing
                    an existing donor database.
                </p>

                <div class="flex gap-24">
                    <ButtonLink href="/#plans" class="min-h-48" color="action">
                        Try For Free
                    </ButtonLink>
                </div>
            </div>

            <img
                src="../../../images/home-banner-1.png"
                alt="The Pravi dashboard user interface."
                class="w-full max-w-[600px] flex-1"
            />
        </div>
    </header>
</template>

<script setup>
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";
import But<PERSON> from "@/Components/Button/Button.vue";

defineEmits(["handleJoinWaitlist"]);
</script>
