<template>
    <section
        class="container flex flex-col items-center justify-center gap-40 lg:flex-row lg:gap-[112px]"
    >
        <div class="w-full max-w-[415px] space-y-16 text-left lg:text-right">
            <h2 class="fs-3xl mb-8 text-center font-extrabold lg:text-right">
                Turn Interest Into Action
            </h2>

            <p>
                Turn clicks into donations with your own custom-built marketing
                funnel.
            </p>
            <p>
                <PERSON>ravi analyses billions of data points to recommend the right
                steps to reach your ideal donors.
            </p>
            <p>
                No guesswork, no jargon just a smart, data-backed plan to grow
                your donorbase.
            </p>

            <div class="flex justify-center gap-24 pt-8 lg:justify-end">
                <ButtonLink href="/#plans" class="h-48" color="action">
                    Try For Free
                </ButtonLink>
            </div>
        </div>

        <img
            src="../../../images/home-conversion-demo.png"
            alt=""
            class="w-full max-w-[580px] flex-shrink"
        />
    </section>
</template>

<script setup>
import Button from "@/Components/Button/Button.vue";
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";
import Pill from "@/Components/Pill/Pill.vue";
import IconBrandInstagram from "../Icons/IconBrandInstagram.vue";
import IconBrandFacebook from "../Icons/IconBrandFacebook.vue";
import IconBrandGoogle from "../Icons/IconBrandGoogle.vue";
import IconMail from "../Icons/IconMail.vue";
import IconPigMoney from "../Icons/IconPigMoney.vue";

defineEmits(["handleJoinWaitlist"]);
</script>
