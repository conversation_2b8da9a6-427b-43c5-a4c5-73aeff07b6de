<template>
    <section class="relative">
        <div class="container">
            <div
                class="mx-auto max-w-[848px] rounded-2xl bg-pink-3x-extra-light px-40 py-24"
            >
                <div
                    class="absolute bottom-0 left-0 right-0 top-0 bg-lines-1 bg-cover bg-center bg-no-repeat md:bg-left-top"
                ></div>

                <h2 class="fs-3xl relative mb-48 text-center font-extrabold">
                    Meet the team
                </h2>

                <div
                    class="flex flex-col items-center justify-center gap-24 md:flex-row lg:gap-64"
                >
                    <div
                        v-for="(profile, index) in teamProfiles"
                        :key="index"
                        class="relative rounded-2xl bg-surface-page px-24 py-40 text-center shadow"
                    >
                        <img
                            :src="profile.imgUrl"
                            :alt="`${profile.name} smiling and working on their laptop.`"
                            class="w-full max-w-[200px] rounded-2xl"
                            width="200"
                            height="200"
                            loading="lazy"
                        />

                        <h3 class="fs-lg mb-8 mt-40 font-bold">
                            {{ profile.name }}
                        </h3>

                        <p>{{ profile.title }}</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script setup>
import jeremyImage from "../../../images/profile-jeremy.jpg";
import robynImage from "../../../images/profile-robyn.jpg";

const teamProfiles = [
    {
        name: "Jeremy Healsmith",
        title: "Co-Founder",
        imgUrl: jeremyImage,
    },
    {
        name: "Robyn Greaves",
        title: "Co-Founder",
        imgUrl: robynImage,
    },
];
</script>
