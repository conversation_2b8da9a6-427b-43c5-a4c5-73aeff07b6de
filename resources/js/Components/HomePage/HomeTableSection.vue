<template>
    <section
        class="container flex flex-col items-center justify-start gap-40 lg:flex-row xl:gap-80"
    >
        <div class="lg:order-1 lg:w-1/2">
            <div class="max-w-md space-y-16 lg:max-w-lg">
                <h2 class="fs-3xl text-center font-extrabold md:text-left">
                    Discover your ideal donors
                </h2>

                <p>
                    <PERSON><PERSON><PERSON> builds data-driven donor personas based on billions of
                    data points including gender, age, giving habits and the
                    social media channels they use.
                </p>

                <p>
                    Use these insights to create content and fundraising
                    campaigns that truly connect, without needing a big
                    database, budget or team.
                </p>

                <div
                    class="flex items-center justify-center gap-24 pt-8 md:justify-start"
                >
                    <ButtonLink href="#plans" class="h-48" color="action">
                        Try For Free
                    </ButtonLink>
                </div>
            </div>
        </div>

        <div class="lg:w-1/2">
            <div class="relative max-w-[528px]">
                <div
                    class="relative overflow-x-auto rounded-lg border border-border-primary-light p-4 md:p-16"
                >
                    <TablePersona :tableData="tableData" />
                </div>

                <img
                    src="../../../images/triangles-1.png"
                    alt=""
                    class="absolute -right-32 bottom-0 hidden -rotate-90 md:inline-block"
                    width="84"
                    height="82"
                />
            </div>
        </div>
    </section>
</template>

<script setup>
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";
import TablePersona from "@/Components/TablePersona/TablePersona.vue";
import Button from "@/Components/Button/Button.vue";

defineEmits(["handleJoinWaitlist"]);

const tableData = {
    gender: "Female",
    age: "25-29",
    location: "London",
    salary: "£20k - £30k",
    predicted_num_donors: "11,500",
    affinity: "Very High",
    predicted_donation: "£4.32 - £8.20",
    likely_to_give_regularly: "15",
};
</script>
