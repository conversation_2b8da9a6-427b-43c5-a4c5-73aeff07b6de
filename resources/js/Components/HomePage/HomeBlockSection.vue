<template>
    <section class="container text-center">
        <h2 class="fs-3xl mb-8 font-extrabold">
            <PERSON><PERSON><PERSON> creates tailored donor personas and acquisition strategies
        </h2>
        <p class="mb-24">
            Find out who is passionate about your cause, where to find them, and
            what to say to encourage them to give.
        </p>

        <picture>
            <source
                srcset="../../../images/home-block.jpg"
                media="(min-width: 768px)"
            />
            <img
                src="../../../images/home-block-mobile.jpg"
                alt=""
                class="mb-24 rounded-2xl"
            />
        </picture>

        <ul
            class="relative z-10 flex flex-col items-center justify-center gap-40 rounded-2xl bg-[#fafafa] p-24 lg:flex-row lg:items-end lg:gap-24"
        >
            <li
                v-for="item of list"
                :key="item.number"
                class="max-w-[556px] space-y-12"
            >
                <div
                    class="fs-md relative mx-auto grid size-56 place-items-center rounded-full bg-surface-action-hover-extra-light font-bold"
                    :class="{
                        'lg:before:border- lg:before:absolute lg:before:-right-4 lg:before:top-1/2 lg:before:z-[-1] lg:before:block lg:before:h-1 lg:before:w-[730px] lg:before:border-dashed lg:before:border-icon-grey lg:before:bg-line-dotted':
                            item.number == 2,
                    }"
                >
                    {{ item.number }}
                </div>
                <h3 class="fs-md font-bold">{{ item.title }}</h3>
                <p>{{ item.text }}</p>
            </li>
        </ul>
    </section>
</template>

<script setup>
const list = [
    {
        number: 1,
        title: "Sign Up for Pravi",
        text: "Instantly find out who will love your cause even if you don't have any data of your own!",
    },
    {
        number: 2,
        title: "Unlock the Future of Fundraising",
        text: "Pravi tells you who your ideal donors are, how to reach them and how much to ask for.",
    },
];
</script>
