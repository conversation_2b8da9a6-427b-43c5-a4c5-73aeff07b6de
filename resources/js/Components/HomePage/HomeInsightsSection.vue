<template>
    <section class="container">
        <div
            class="container grid items-center gap-24 rounded-2xl bg-surface-action-3x-light bg-shapes-1 bg-contain bg-top bg-no-repeat px-24 py-40 md:px-40 lg:grid-cols-2 xl:px-80"
        >
            <div class="mx-auto max-w-[472px] text-center md:text-left">
                <h2 class="fs-3xl mb-8 font-extrabold">Stay Ahead on Social</h2>

                <p class="mb-8">
                    Schedule and track your posts using our built-in social
                    media scheduler.
                </p>
                <p>
                    Simply connect your accounts and you can stay organised,
                    post consistently, and see what's working - all in one
                    place.
                </p>

                <div
                    class="mx-auto my-40 flex max-w-sm flex-col gap-24 sm:flex-row md:mx-0"
                >
                    <ButtonLink href="/#plans" class="min-h-48" color="action">
                        Try For Free
                    </ButtonLink>
                </div>
            </div>

            <img
                src="../../../images/graph-profile.webp"
                alt=""
                width="462"
                height="485"
                class="mx-auto w-full max-w-[462px] lg:mx-0"
                loading="lazy"
            />
        </div>
    </section>
</template>

<script setup>
import Button from "@/Components/Button/Button.vue";
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";
defineEmits(["handleJoinWaitlist"]);
</script>
