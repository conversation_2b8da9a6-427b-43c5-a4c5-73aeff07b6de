<template>
    <section id="plans" class="container text-center">
        <div class="flex items-center justify-center gap-24 lg:gap-40">
            <PlanCard
                v-for="plan of plans"
                :key="plan.id"
                :plan="plan"
                buttonText="Try For Free"
                class="max-w-[593px]"
                href="/select-subscription"
            />
        </div>

        <p class="mx-auto my-40 max-w-5xl">
            Pravi is powered by anonymous UK-based data.<br />
            If you're outside the UK, you can still sign up and explore, or
            <span
                class="cursor-pointer font-semibold text-text-action-hover underline"
                @click="emits('showWaitlistModal')"
                >join our newsletter</span
            >
            to hear when we launch in your country.
        </p>
    </section>
</template>

<script setup>
import PlanCard from "@/Components/PlanCard/PlanCard.vue";
const emits = defineEmits("showWaitlistModal");

defineProps({ plans: Array });

// const freeTrialPlan = {
//     title: "Get started with a 28 day free trial",
//     subtitle: "£20/month",
//     is_discounted: false,
//     description_formatted: [
//         "🔍 Build data-driven donor personas tailored to your cause -  see who’s most likely to give, how much, and how often, without needing a donor list",
//         "➡️ Follow a step-by-step donor growth funnel, that tells you which platforms to focus on and how to turn awareness into action",
//         "💬 Create content fast using a smart chatbot that understands your cause and your donors",
//         "📅 Post and plan using a built-in social media scheduler (connect 3 accounts including Facebook, Instagram, TikTok, or LinkedIn)",
//         "🚀 Join the Pravi Launchpad: a guided 4-week free trial with live webinars and 1:1 support to help you get started",
//     ],
// };
</script>
