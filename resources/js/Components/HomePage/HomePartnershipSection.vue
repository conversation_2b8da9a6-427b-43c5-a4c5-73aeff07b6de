<template>
    <section class="container">
        <h2 class="fs-3xl text-center font-extrabold">In partnership with</h2>

        <div
            class="mx-auto flex max-w-[1100px] flex-wrap items-center justify-around gap-8 py-32 md:gap-24 lg:gap-64"
        >
            <Pill class="h-[112px] w-full shadow md:w-auto">
                <img
                    src="../../../images/orbital-logo.png"
                    alt="Orbital Global"
                    width="350"
                    height="97"
                    class="w-full max-w-[280px] lg:max-w-[350px]"
                    loading="lazy"
                />
            </Pill>
            <Pill class="h-[112px] w-full shadow md:w-auto">
                <img
                    src="../../../images/nkmt-logo.png"
                    alt="Natalie Kate Moss Trust"
                    width="198"
                    height="43"
                    class="w-full max-w-[198px] lg:max-w-[198px]"
                    loading="lazy"
                />
            </Pill>
            <Pill class="h-[112px] w-full shadow md:w-auto">
                <img
                    src="../../../images/rainforest-logo.png"
                    alt="Rainforest Foundation UK. Securing lands, sustaining lives."
                    width="234"
                    height="61"
                    class="w-full max-w-[234px] lg:max-w-[196px]"
                    loading="lazy"
                />
            </Pill>
            <Pill class="h-[112px] w-full shadow md:w-auto">
                <img
                    src="../../../images/innovate-uk-logo.png"
                    alt="Co-Funded by UKRI Innovate UK."
                    width="426"
                    height="80"
                    class="w-full max-w-[218px] lg:max-w-[426px]"
                    loading="lazy"
                />
            </Pill>
        </div>
    </section>
</template>

<script setup>
import Pill from "@/Components/Pill/Pill.vue";
</script>
