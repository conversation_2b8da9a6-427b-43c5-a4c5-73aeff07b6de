import { fn } from "@storybook/test";
import TextInput from "./TextInput.vue";
import rules from "../../utilities/validation-rules";

export default {
    component: TextInput,
    tags: ["autodocs"],
    argTypes: {
        type: {
            control: { type: "select" },
            options: ["text", "number", "email", "password"],
            default: "text",
        },
    },
    args: {
        label: "Email",
    },
};

export const Text = {
    args: {
        type: "text",
        label: "Name",
        helperText: "Some helper text.",
    },
};

export const Number = {
    args: {
        type: "number",
        label: "Number",
        helperText: "Some helper text.",
    },
};

export const Email = {
    args: {
        type: "email",
        label: "Email",
        rules: [rules.required, rules.email],
    },
};

export const Password = {
    args: {
        type: "password",
        label: "Password",
        rules: [rules.required, rules.password],
        helperText:
            "Must be at least 8 characters, contain at least one uppercase letter, one lowercase letter, one special character or number.",
    },
};

export const Error = {
    args: {
        modelValue: "<EMAIL>",
        type: "email",
        label: "Email",
        serverError: "The email address has already been used.",
        rules: [rules.required, rules.email],
    },
};

export const Disabled = {
    args: {
        type: "email",
        label: "Email",
        disabled: true,
    },
};
