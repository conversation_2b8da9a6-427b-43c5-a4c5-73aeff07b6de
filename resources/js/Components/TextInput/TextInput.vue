<template>
    <div
        :class="{
            [paddingWithText]: helperText || displayedErrorMessage,
            [paddingWithoutText]: !helperText && !displayedErrorMessage,
        }"
    >
        <div class="relative">
            <span
                v-if="isPrice"
                class="absolute left-12 top-1/2 -translate-y-1/2 text-text-grey"
            >
                {{ priceSymbol }}
            </span>

            <input
                ref="input"
                :id="id"
                :name="name"
                :type="currentType"
                class="peer h-48 w-full rounded-lg border-border-primary bg-surface-page p-8 text-text-body placeholder-transparent outline-none transition-all hover:border-border-action-hover focus:border-border-focus focus:ring-transparent disabled:border-border-disabled disabled:bg-surface-disabled"
                :class="{
                    'border-border-error hover:border-border-error focus:border-border-error':
                        hasError,

                    'pl-24': isPrice,
                }"
                v-model="internalValue"
                @blur="validateInput"
                :placeholder="label"
                :disabled="disabled"
                :required="required"
            />
            <button
                v-if="type === 'password'"
                class="absolute right-4 top-1/2 -translate-y-1/2 p-4"
                type="button"
                @click="togglePasswordVisibility"
            >
                <IconEye v-if="!isPasswordVisible"></IconEye>
                <IconEyeClosed v-else></IconEyeClosed>
            </button>

            <label
                :for="id"
                class="absolute -top-2 left-8 -translate-y-1/2 scale-[0.875] bg-surface-page px-4 font-bold text-text-highlight transition-all peer-placeholder-shown:top-1/2 peer-placeholder-shown:scale-[1] peer-placeholder-shown:font-normal peer-placeholder-shown:text-text-grey peer-focus:-top-2 peer-focus:scale-[0.875] peer-focus:font-bold peer-focus:text-text-highlight peer-disabled:bg-surface-disabled"
                :class="{
                    'text-text-error peer-focus:text-text-error': hasError,
                }"
            >
                {{ label }}
            </label>
        </div>
        <p
            v-if="helperText || displayedErrorMessage"
            class="fs-xs mt-4"
            :class="{
                'text-text-error': hasError,
                'text-text-grey': !hasError,
            }"
        >
            {{ hasError ? displayedErrorMessage : helperText }}
        </p>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import IconEye from "../Icons/IconEye.vue";
import IconEyeClosed from "../Icons/IconEyeClosed.vue";

// Define props
const props = defineProps({
    modelValue: String,
    name: String,
    id: String,
    label: String,
    disabled: Boolean,
    required: Boolean,
    helperText: String,
    serverError: String,
    paddingWithoutText: {
        type: String,
        default: "pb-28 md:pb-32",
    },
    paddingWithText: {
        type: String,
        default: "pb-12",
    },
    type: {
        type: String,
        default: "text",
        validator: (value) =>
            ["text", "email", "password", "number"].includes(value),
    },
    rules: {
        type: Array,
        default: () => [],
    },
    isPrice: {
        type: Boolean,
        default: false,
    },
    priceSymbol: {
        type: String,
        default: "£",
    },
});

// State to manage the current input type
const currentType = ref(props.type);

// V-Model handling
// ========================
// Define emits
const emit = defineEmits(["update:modelValue"]);

// Internal state for input value
const internalValue = ref(props.modelValue);

// Watch internalValue and emit the update when it changes
// For price input force numbers only
// ========================
watch(internalValue, (newValue) => {
    if (props.isPrice) {
        const numericOnly = String(newValue).replace(/\D/g, "");
        internalValue.value = numericOnly;
        emit("update:modelValue", numericOnly);
    } else {
        emit("update:modelValue", newValue);
    }
});

const safeString = (val) => (val == null ? "" : String(val));

// Watch the prop to sync internal state if modelValue changes externally
watch(
    () => props.modelValue,
    (newValue) => {
        internalValue.value = props.isPrice
            ? safeString(newValue).replace(/\D/g, "")
            : newValue;
    },
);

// Validation handling
// ========================
// Validate input on change
const errorMessage = ref("");
const hasError = computed(() => !!props.serverError || !!errorMessage.value);

// Client side validation
const validateInput = () => {
    errorMessage.value = "";
    for (const rule of props.rules) {
        const validationResult = rule(internalValue.value);
        if (validationResult !== true) {
            errorMessage.value = validationResult;
            break;
        }
    }
};

// This computed property decides which error message to display
// (server error overrides client)
const displayedErrorMessage = computed(() => {
    return props.serverError || errorMessage.value;
});

// Focus handling
// ========================
// Focus input if needed
const input = ref(null);
onMounted(() => {
    if (input.value && input.value.hasAttribute("autofocus")) {
        input.value.focus();
    }
});

// Expose focus method
defineExpose({ focus: () => input.value.focus() });

// Password inputs visibility toggling
// ========================
// State to track password visibility
const isPasswordVisible = ref(false);

// Method to toggle the password visibility
const togglePasswordVisibility = () => {
    isPasswordVisible.value = !isPasswordVisible.value;
    currentType.value = isPasswordVisible.value ? "text" : "password";
};
</script>
