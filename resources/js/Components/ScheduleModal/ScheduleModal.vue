<template>
    <div v-if="isVisible" class="fixed inset-0 bg-surface-modal flex items-center justify-center z-10">
        <div class="bg-white rounded-xl p-24 max-w-md w-full">
            <h2 class="text-2xl font-header font-bold mb-16 text-black">Schedule Campaign</h2>
            <div class="mb-16">
                <label class="block mb-8 font-body text-black">Select Date and Time</label>
                <input 
                    type="datetime-local" 
                    v-model="localScheduleDate" 
                    class="w-full p-12 border border-[#DCDDDD] rounded focus:border-text-action focus:ring-1 focus:ring-text-action"
                >
            </div>
            <div class="flex justify-end gap-12">
                <button 
                    @click="handleCancel" 
                    class="px-16 py-12 bg-surface-disabled rounded-lg font-body text-black hover:bg-neutral-light transition-colors"
                >
                    Cancel
                </button>
                <button 
                    @click="handleSchedule" 
                    class="px-16 py-12 bg-pink-500 text-white rounded-lg font-body hover:bg-pink-dark transition-colors"
                >
                    Schedule
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    isVisible: {
        type: Boolean,
        default: false
    },
    scheduleDate: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['close', 'schedule'])

const localScheduleDate = ref(props.scheduleDate)

watch(() => props.scheduleDate, (newValue) => {
    localScheduleDate.value = newValue
})

const handleCancel = () => {
    emit('close')
}

const handleSchedule = () => {
    if (!localScheduleDate.value) return
    emit('schedule', localScheduleDate.value)
}
</script> 