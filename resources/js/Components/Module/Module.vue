<template>
    <button
        class="w-full max-w-[330px] rounded-2xl border border-surface-grey-light bg-transparent p-12 text-left shadow transition lg:px-24"
        :class="{
            'hover:border-pink-light hover:bg-surface-action-2x-light':
                !disabled,
        }"
        :disabled="disabled"
        @click="handleClick"
    >
        <slot> </slot>

        <h2 class="mt-8 font-bold text-text-headings">{{ title }}</h2>

        <p class="fs-xs mb-8 font-light">{{ text }}</p>
    </button>
</template>

<script setup>
const props = defineProps({
    title: String,
    text: String,
    disabled: <PERSON><PERSON><PERSON>,
});

const emit = defineEmits(["handleClick"]);

const handleClick = () => {
    if (props.disabled) return;

    emit("handleClick");
};
</script>
