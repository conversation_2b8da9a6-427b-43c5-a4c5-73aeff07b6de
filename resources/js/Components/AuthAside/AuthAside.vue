<template>
    <div class="relative hidden lg:inline-block">
        <img
            class="absolute -right-16 -top-16"
            src="/images/shapes-border.svg"
            alt=""
            width="86"
            height="84"
        />

        <aside
            v-if="activePage"
            class="grid min-w-[372px] place-items-center rounded-2xl bg-surface-information-light p-56"
        >
            <ol class="space-y-32">
                <li
                    v-for="item of pageList"
                    :key="item.id"
                    class="flex items-center"
                    :class="{ 'font-bold': isActive(item.id) }"
                >
                    <span
                        class="fs-2xl mr-24 grid size-48 place-items-center rounded-full font-bold"
                        :class="{
                            'bg-surface-action text-text-body-white': isActive(
                                item.id,
                            ),
                            'bg-white': !isActive(item.id),
                        }"
                        >{{ item.id }}</span
                    >
                    {{ item.name }}
                </li>
            </ol>
        </aside>

        <img
            v-else
            class="rounded-2xl"
            src="/images/register-graphic.jpg"
            alt=""
            width="372"
            height="544"
        />
    </div>
</template>

<script setup>
const props = defineProps({
    activePage: Number,
});

const pageList = [
    { id: 1, name: "Organisation details", active: false },
    { id: 2, name: "Subscription", active: false },
    { id: 3, name: "About you", active: false },
    { id: 4, name: "Add users", active: false },
    // { id: 5, name: "Connect accounts", active: false },
];

const isActive = (id) => {
    return id === props.activePage;
};
</script>
