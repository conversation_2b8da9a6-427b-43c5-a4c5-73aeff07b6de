<template>
    <VTooltip theme="light" class="inline-flex cursor-pointer items-center">
        <span class="pr-8">
            {{ label }}
        </span>
        <IconInfo :class="iconClass" />

        <template #popper>
            {{ tooltip }}
        </template>
    </VTooltip>
</template>

<script setup>
import IconInfo from "../Icons/IconInfo.vue";

defineProps({
    label: String,
    tooltip: String,
    iconClass: {
        type: String,
        default: "stroke-text-body",
    },
});
</script>
