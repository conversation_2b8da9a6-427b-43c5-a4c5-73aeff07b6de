<template>
    <section class="container space-y-24">
        <div
            v-for="(faq, index) in faqs"
            :key="faq.id"
            id="faqs-accordion-collapse"
            class="mx-auto max-w-5xl rounded-lg border border-border-primary bg-surface-page"
        >
            <h2 :id="`accordion-collapse-heading-${faq.id}`">
                <button
                    type="button"
                    class="fs-md flex w-full items-center justify-between gap-8 p-16 text-left font-bold md:px-24"
                    :aria-controls="`accordion-collapse-body-${faq.id}`"
                    @click="toggle(index)"
                >
                    <span>{{ faq.title }}</span>

                    <IconChevron
                        class="shrink-0 rotate-180"
                        :class="{ 'rotate-0': isActive(index) }"
                    />
                </button>
            </h2>
            <div
                :id="`accordion-collapse-body-${faq.id}`"
                class="px-16 pb-16 transition-all md:px-24"
                :class="{
                    hidden: !isActive(index),
                }"
                :aria-labelledby="`accordion-collapse-heading-${faq.id}`"
            >
                <div class="space-y-16">
                    <p v-html="faq.description"></p>

                    <iframe
                        v-if="faq.video_url"
                        :src="faq.video_url"
                        width="560"
                        height="315"
                        frameborder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        referrerpolicy="strict-origin-when-cross-origin"
                        allowfullscreen
                    ></iframe>

                    <div class="flex items-center justify-between gap-16">
                        <button
                            @click="handleBack"
                            class="min-h-48 px-16 py-8 text-text-action"
                        >
                            Back
                        </button>
                        <Button
                            @click="handleNext"
                            color="action"
                            class="min-h-48"
                            >Next</Button
                        >
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script setup>
import { ref } from "vue";
import IconChevron from "../Icons/IconChevron.vue";
import Button from "../Button/Button.vue";

// Props for the component
const props = defineProps({ faqs: Array });

// State to track the currently active accordion item
const activeIndex = ref(null);

const handleNext = () => {
    activeIndex.value++;
};
const handleBack = () => {
    activeIndex.value--;
};

// Method to toggle the active state of an accordion item
const toggle = (index) => {
    if (activeIndex.value === index) {
        activeIndex.value = null; // Collapse if already active
    } else {
        activeIndex.value = index; // Set active index
    }
};

// Method to check if an accordion item is active
const isActive = (index) => {
    return activeIndex.value === index;
};
</script>
