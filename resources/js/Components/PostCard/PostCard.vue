<template>
    <Link :href="`/blog/${post.slug}`">
        <img
            :src="getImageUrl(post.image)"
            alt=""
            class="mb-12 w-full object-cover md:mb-32"
        />

        <div class="mb-8 flex flex-wrap gap-2">
            <Pill v-for="category in post.categories" :key="category.id">
                {{ category.name }}
            </Pill>
        </div>

        <h3 class="fs-xl mb-8 font-bold">{{ post.title }}</h3>

        <p class="fs-md">
            {{ trimContent(post.content, 25) }}
        </p>
    </Link>
</template>

<script setup>
import Pill from "@/Components/Pill/Pill.vue";
import { Link } from "@inertiajs/vue3";
import { trimContent, getImageUrl } from "@/utilities/helpers";

const props = defineProps({
    post: Object,
});
</script>
