<template>
    <ConversationPravi>
        <p class="mb-4 font-bold">Coming soon:</p>

        <p>
            <PERSON><PERSON><PERSON> will create tailored conversion funnels, draft campaign
            materials and monitor your campaign. If you can't wait, contact our
            expert digital marketers.
        </p>

        <div class="my-40 flex flex-col gap-16 lg:flex-row">
            <div class="lg:w-1/2">
                <div class="mx-auto w-full max-w-[300px]">
                    <Pill arrow-down class="w-full">
                        <IconBrandFacebook class="size-32 lg:size-56" />
                        <IconBrandInstagram class="size-32 lg:size-56" />
                    </Pill>
                    <Pill arrow-down class="w-full">
                        <IconBrandGoogle class="size-32 lg:size-56" />
                    </Pill>
                    <Pill arrow-down class="w-full">
                        <IconMail class="size-32 lg:size-56" />
                    </Pill>
                    <Pill class="w-full">
                        <IconPigMoney class="size-32 lg:size-56" />
                    </Pill>
                </div>
            </div>

            <div
                class="grid place-items-center rounded-2xl bg-surface-page px-40 py-16 lg:w-1/2"
            >
                <div>
                    <p class="fs-lg mb-20 text-center">
                        We predict
                        <span class="font-bold text-text-action-hover">3X</span>
                        return for a campaign targeting these donors
                    </p>

                    <TableReturns :tableData="tableData" />
                </div>
            </div>
        </div>

        <p class="mb-12 mt-24">
            Would you like one of our expert fundraisers to create and run your
            campaign for you?
        </p>
        <div class="flex gap-16">
            <Button @click="openModal" color="default">
                Click here for a quote from one of our experts
            </Button>
        </div>

        <HubspotModal
            :showHubspotModal="showHubspotModal"
            @close="showHubspotModal = false"
        />
    </ConversationPravi>
</template>

<script setup>
import { ref } from "vue";
import Button from "../Button/Button.vue";
import ConversationPravi from "../ConversationPravi/ConversationPravi.vue";
import IconBrandFacebook from "../Icons/IconBrandFacebook.vue";
import IconBrandGoogle from "../Icons/IconBrandGoogle.vue";
import IconBrandInstagram from "../Icons/IconBrandInstagram.vue";
import IconMail from "../Icons/IconMail.vue";
import IconPigMoney from "../Icons/IconPigMoney.vue";
import Pill from "../Pill/Pill.vue";
import TableReturns from "../TablePersona/TableReturns.vue";
import HubspotModal from "../HubspotModal/HubspotModal.vue";

const showHubspotModal = ref(false);

const openModal = () => {
    showHubspotModal.value = true;
};

const tableData = {
    spend: "£1000",
    receive: "£3000",
};
</script>
