<template>
    <div
        @click.self="handleClose"
        :id="id"
        tabindex="-1"
        aria-hidden="true"
        class="fixed left-0 right-0 top-0 z-50 h-full max-h-full w-full items-center justify-center overflow-y-auto overflow-x-hidden bg-surface-modal md:inset-0"
        :class="{ flex: isVisible, hidden: !isVisible }"
    >
        <div class="relative max-h-full w-full p-4" :class="[widthClass]">
            <div
                class="relative bg-surface-page p-24 text-text-headings"
                :class="{ 'rounded-3xl': rounded }"
            >
                <div class="mb-24 flex items-center justify-between">
                    <h2 v-if="title" class="fs-2xl font-bold">{{ title }}</h2>
                    <button
                        type="button"
                        class="ml-auto inline-flex items-center justify-center bg-transparent p-4 text-black"
                        @click="handleClose"
                    >
                        <IconCross></IconCross>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <div>
                    <slot></slot>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import IconCross from "@/Components/Icons/IconCross.vue";
import { computed } from "vue";

const props = defineProps({
    isVisible: Boolean,
    title: String,
    width: {
        type: String,
        default: "5xl",
    },
    rounded: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(["close"]);

const handleClose = () => {
    emit("close");
};

const widthClass = computed(() => {
    return {
        "max-w-xl": props.width === "xl",
        "max-w-2xl": props.width === "2xl",
        "max-w-3xl": props.width === "3xl",
        "max-w-4xl": props.width === "4xl",
        "max-w-5xl": props.width === "5xl",
    };
});
</script>
