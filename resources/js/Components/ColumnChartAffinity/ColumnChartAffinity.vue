<template>
    <div class="max-w-7xl bg-surface-page p-20 md:px-24">
        <div class="mb-16 flex w-full justify-between px-16">
            <div>
                <h2 class="fs-2xl mb-8 font-body">{{ chartData.title }}</h2>

                <p class="">
                    Select a donor distribution bar and click the button to
                    refresh the prediction.
                </p>
            </div>

            <Button
                v-if="hasResults"
                @click="() => emit('refreshPredictions')"
                color="action"
                :disabled="findDonorsStore.isLoading"
                class="h-40"
            >
                Refresh Prediction
                <Spinner
                    v-if="findDonorsStore.isLoading"
                    isDark
                    class="ml-8 w-20"
                />
            </Button>
        </div>

        <VueApexCharts
            type="bar"
            :options="chartOptions"
            :series="series"
            height="420px"
            :width="width"
        />
    </div>
</template>

<script setup>
import { ref } from "vue";
import VueApexCharts from "vue3-apexcharts";
import Button from "../Button/Button.vue";
import { useFindDonorsStore } from "@/stores/findDonors";
import Spinner from "../Spinner/Spinner.vue";

const findDonorsStore = useFindDonorsStore();

const { chartData, chartType } = defineProps({
    chartData: Object,
    width: { type: String, default: "600px" },
    chartType: String,
    hasResults: Boolean,
});

const emit = defineEmits(["barClicked", "refreshPredictions"]);

// Define your default and selected colors.
const defaultColor = "#5FAC5E";
const selectedColor = "#FFC85C";

// Determine which bar should be highlighted.
const selectedBar = chartData.selected;

// Build an array of colors for each bar based on the label match.
const colorsArray = chartData.data.map((label) =>
    label === selectedBar ? selectedColor : defaultColor,
);

// Create a series array using the numeric values.
const series = ref([
    {
        name: chartData.title,
        data: chartData.categories,
    },
]);

// Function to handle the bar click event
const handleBarClick = (event, chartContext, config) => {
    // Get the index of the clicked bar
    const clickedBarIndex = config.dataPointIndex;

    // Get the value of the clicked bar
    const clickedValue = chartData.data[clickedBarIndex];

    emit("barClicked", clickedValue, chartType);
};

// Build the chart options with the click event handler
const chartOptions = ref({
    xaxis: {
        categories: chartData.data,
        labels: {
            show: true,
        },
    },
    yaxis: {
        labels: {
            show: true,
        },
    },

    colors: colorsArray,
    plotOptions: {
        bar: {
            borderRadius: 16,
            distributed: true,
        },
    },
    grid: {
        show: true,
        strokeDashArray: 6,
    },
    tooltip: {
        enabled: true,
    },
    dataLabels: {
        enabled: false,
    },
    legend: {
        show: false,
    },
    states: {
        hover: {
            filter: {
                type: "none",
            },
        },
    },
    chart: {
        toolbar: {
            show: false,
        },
        events: {
            dataPointSelection: handleBarClick,
        },
    },
});
</script>

<style>
.apexcharts-bar-area {
    cursor: pointer;
}
</style>
