import ColumnChartAffinity from "./ColumnChartAffinity.vue";

export default {
    component: ColumnChartAffinity,
    tags: ["autodocs"],
    args: {},
};

export const Age = {
    args: {
        chartData: {
            title: "Donor distribution by age",
            data: [
                "18-24",
                "25-39",
                "40-49",
                "50-55", // This will be highlighted.
                "56-60",
                "61-68",
                "69-72",
                "73-76",
                "77+",
            ],
            categories: [
                80000, 100000, 110000, 150000, 140000, 160000, 140000, 70000,
                40000,
            ],
            selected: "50-55", // Specify the selected bar here.
        },
    },
};

export const Gender = {
    args: {
        chartData: {
            title: "Donor distribution by gender",
            data: ["Female", "Male"],
            categories: [80000, 100000],
            selected: "Male", // Specify the selected bar here.
        },
    },
};
