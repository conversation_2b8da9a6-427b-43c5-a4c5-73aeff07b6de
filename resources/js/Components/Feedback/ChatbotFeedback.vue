<template>
    <div>
        <div v-if="!hasProvided">
            <button class="p-4" @click="openModal" title="Bad response">
                <IconThumbDown
                    class="transition hover:scale-110 hover:fill-icon-action-light hover:stroke-icon-focus active:scale-125"
                    :class="{
                        'fill-icon-action-light stroke-icon-focus':
                            hasProvidedNegative,
                    }"
                />
            </button>
            <button class="p-4" @click="handleSubmit(1)" title="Good response">
                <IconThumbUp
                    class="transition hover:scale-110 hover:fill-icon-action-light hover:stroke-icon-focus active:scale-125"
                    :class="{
                        'fill-icon-action-light stroke-icon-focus':
                            hasProvidedPositive,
                    }"
                />
            </button>
        </div>

        <Teleport to="body">
            <Modal
                :isVisible="showModal"
                title="Help us improve"
                width="3xl"
                @close="closeModal"
            >
                <div class="mb-16">
                    Any feedback you have will help us improve our results.
                </div>
                <TextArea v-model="message" rows="6"></TextArea>

                <div class="text-center">
                    <Button
                        @click="handleSubmit(0)"
                        color="action"
                        class="h-48"
                    >
                        Submit
                    </Button>
                </div>
            </Modal>
        </Teleport>
    </div>
</template>

<script setup>
import IconThumbUp from "../Icons/IconThumbUp.vue";
import IconThumbDown from "../Icons/IconThumbDown.vue";
import Modal from "../Modal/Modal.vue";
import { ref } from "vue";
import { submitFeedback } from "@/utilities/api";
import TextArea from "../TextArea/TextArea.vue";
import Button from "../Button/Button.vue";

const hasProvided = ref(false);
const hasProvidedPositive = ref(false);
const hasProvidedNegative = ref(false);

// Modal handlers
const showModal = ref(false);
const openModal = () => {
    showModal.value = true;
};
const closeModal = () => {
    showModal.value = false;
};

// Feedback form
const message = ref(null);

const handleSubmit = async (isPositive) => {
    await submitFeedback(isPositive, "chatbot_response", message.value);

    message.value = null;
    showModal.value = false;

    if (isPositive) {
        hasProvidedPositive.value = true;
        hasProvidedNegative.value = false;
    } else {
        hasProvidedNegative.value = true;
        hasProvidedPositive.value = false;
    }
    setTimeout(() => {
        hasProvided.value = true;
    }, 1000);
};
</script>
