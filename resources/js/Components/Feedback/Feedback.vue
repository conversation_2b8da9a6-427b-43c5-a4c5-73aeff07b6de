<template>
    <div>
        <div v-if="hasProvided" class="pe-16">Thank you for your feedback!</div>

        <div v-else class="flex gap-4">
            Help us improve. Do these results look right?

            <button class="p-4" @click="openModal">
                <IconThumbDown
                    class="transition hover:scale-125 hover:fill-icon-action-light hover:stroke-icon-focus"
                />
            </button>
            <button class="p-4" @click="handleSubmit(1)">
                <IconThumbUp
                    class="hover:stroke- transition hover:scale-125 hover:fill-icon-action-light hover:stroke-icon-focus"
                />
            </button>
        </div>

        <Teleport to="body">
            <Modal
                :isVisible="showModal"
                title="Help us improve"
                width="3xl"
                @close="closeModal"
            >
                <div class="mb-16">
                    Any feedback you have will help us improve our results.
                </div>
                <TextArea v-model="message" rows="6"></TextArea>

                <div class="text-center">
                    <Button
                        @click="handleSubmit(0)"
                        color="action"
                        class="h-48"
                    >
                        Submit
                    </Button>
                </div>
            </Modal>
        </Teleport>
    </div>
</template>

<script setup>
import IconThumbUp from "../Icons/IconThumbUp.vue";
import IconThumbDown from "../Icons/IconThumbDown.vue";
import Modal from "../Modal/Modal.vue";
import { ref } from "vue";
import { submitFeedback } from "@/utilities/api";
import TextArea from "../TextArea/TextArea.vue";
import Button from "../Button/Button.vue";

const hasProvided = ref(false);

// Modal handlers
const showModal = ref(false);
const openModal = () => {
    showModal.value = true;
};
const closeModal = () => {
    showModal.value = false;
};

// Feedback form
const message = ref(null);

const handleSubmit = async (isPositive) => {
    await submitFeedback(isPositive, "find_new_donors_results", message.value);

    message.value = null;
    showModal.value = false;
    hasProvided.value = true;
};
</script>
