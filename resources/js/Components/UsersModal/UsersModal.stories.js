import UsersModal from "./UsersModal.vue";

export default {
    component: UsersModal,
    tags: ["autodocs"],
    args: {},
};

export const Default = {
    args: {
        showProfilesModal: true,
        hasUsers: true,
        tableData: {
            headings: ["First name", "Last name", "Email", "Role", "Action"],
            rows: [
                {
                    firstName: "Lorem",
                    lastName: "Ipsum",
                    email: "<EMAIL>",
                    role: "Admin",
                    action: "Delete",
                },
                {
                    firstName: "Lorem",
                    lastName: "Ipsum",
                    email: "<EMAIL>",
                    role: "Admin",
                    action: "Delete",
                },
                {
                    firstName: "Lorem",
                    lastName: "Ipsum",
                    email: "<EMAIL>",
                    role: "Admin",
                    action: "Delete",
                },
            ],
        },
    },
};
