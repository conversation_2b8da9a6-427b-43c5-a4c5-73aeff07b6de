<!-- UsersModal.vue -->
<template>
    <Modal
        :isVisible="showProfilesModal"
        title="Profile list"
        @close="handleClose"
    >
        <UsersTable
            :tableData="tableData"
            :hasUsers="hasUsers"
            @deleteUsers="handleDeleteUsers"
        />
    </Modal>
</template>

<script setup>
import Modal from "@/Components/Modal/Modal.vue";
import UsersTable from "@/Components/UsersTable/UsersTable.vue";

const props = defineProps({
    showProfilesModal: Boolean,
    tableData: Array,
    hasUsers: Boolean,
});

const emit = defineEmits(["close", "deleteUsers"]);

const handleClose = () => {
    emit("close");
};

const handleDeleteUsers = (id) => {
    emit("deleteUsers", id);
};
</script>
