<template>
    <div class="flex flex-col gap-16 xl:flex-row">
        <Button
            @click="handleSave"
            color="action"
            :disabled="isLoading || isSaveLoading"
            class="h-56"
        >
            Save Campaign
        </Button>

        <Button
            color="default"
            class="h-56"
            :blank="true"
            :disabled="isLoading || isSaveLoading"
            @click="redirectToSocial"
        >
            Post To Social
        </Button>

        <form class="relative flex-1" @submit.prevent="submitPrompt">
            <input
                ref="chatInput"
                id="chatInput"
                type="text"
                class="peer w-full rounded-xl border-border-primary bg-surface-page p-16 text-text-body outline-none transition-all hover:border-border-action-hover focus:border-border-focus focus:ring-transparent disabled:border-border-disabled disabled:bg-surface-disabled"
                v-model="internalValue"
                :placeholder="placeholder"
                :disabled="isLoading || isSaveLoading"
            />
            <button
                class="absolute right-16 top-1/2 -translate-y-1/2 rounded-full bg-surface-action p-4 disabled:cursor-default"
                type="submit"
                :disabled="disabled"
            >
                <Spinner v-if="isLoading || isSaveLoading" class="size-24" />
                <IconArrowUp v-else></IconArrowUp>
            </button>
        </form>

        <Toast
            v-if="toast.message"
            :text="toast.message"
            :type="toast.type"
            @handleClose="toast.message = ''"
        ></Toast>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from "vue";
import IconArrowUp from "../Icons/IconArrowUp.vue";
import Spinner from "../Spinner/Spinner.vue";
import Button from "../Button/Button.vue";
import Toast from "../Toast/Toast.vue";
import { saveCampaign } from "@/utilities/api";
import { useFindDonorsStore } from "@/stores/findDonors";
import { debounce } from "lodash";

const findDonorsStore = useFindDonorsStore();

// Define props
const props = defineProps({
    modelValue: String,
    placeholder: String,
    disabled: Boolean,
    isLoading: Boolean,
    socialUrl: {
        type: String,
        required: false,
        default: null
    }
});

const chatInput = ref(null);

// V-Model handling
// ========================
// Define emits
const emit = defineEmits(["update:modelValue", "submitPrompt", "handleSave"]);

// Internal state for input value
const internalValue = ref(props.modelValue);

// Watch internalValue and emit the update when it changes
watch(internalValue, (newValue) => {
    emit("update:modelValue", newValue);
});

// Watch the prop to sync internal state if modelValue changes externally
watch(
    () => props.modelValue,
    (newValue) => {
        internalValue.value = newValue;
    },
);

// Watch isLoading to refocus when loading completes
watch(
    () => props.isLoading,
    (newValue, oldValue) => {
        // If loading just completed (was loading, now it's not)
        if (oldValue === true && newValue === false) {
            // Wait for the DOM to update
            nextTick(() => {
                // Focus the input
                if (chatInput.value) {
                    chatInput.value.focus();
                }
            });
        }
    },
);

// Submit button handling
// ========================
const submitPrompt = () => {
    emit("submitPrompt");
};

//
// Social Pravi handlers
// ==========================================================================

const redirectToSocial = () => {
    window.open(props.socialUrl, "_blank");
};

onMounted(() => {
    // Focus the input when component is mounted
    if (chatInput.value) {
        chatInput.value.focus();
    }
});

// --------------------------
// Save Campaign handlers
// --------------------------

const isSaved = ref(false);
const isSaveLoading = ref(false);

const toast = ref({
    message: "",
    type: "",
});

// Wrap the saveCampaign function with debounce (1 second delay, for example)
const handleSave = debounce(async () => {
    isSaveLoading.value = true;
    try {
        await saveCampaign();
        isSaved.value = true;
        toast.value = {
            message: "Campaign successfully saved",
            type: "success",
        };
    } catch (error) {
        console.error("Error saving campaign:", error);
    } finally {
        isSaveLoading.value = false;
    }
}, 1000);
</script>
