<template>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 4H20V20H4V4Z" fill="transparent"/>
        <path d="M8 20L12 11" stroke="#242424" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M10.7 14C11.137 15.263 12.13 16 13.25 16C15.321 16 17 14.446 17 12C17.0009 11.2699 16.8418 10.5483 16.534 9.8862C16.2261 9.2241 15.777 8.6375 15.2182 8.1675C14.6593 7.6975 14.0043 7.3556 13.2993 7.1659C12.5942 6.9762 11.8561 6.9432 11.1368 7.0692C10.4176 7.1952 9.7348 7.4773 9.1363 7.8955C8.5377 8.3137 8.038 8.8579 7.6723 9.49C7.3066 10.1219 7.0838 10.8264 7.0195 11.5537C6.9551 12.281 7.0507 13.0137 7.3 13.7" stroke="#242424" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <circle cx="12" cy="12" r="9" stroke="#242424" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
</template>

<script setup>
// No props or logic needed for this icon component
</script> 