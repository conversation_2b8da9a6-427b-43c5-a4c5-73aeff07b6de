<template>
    <svg 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        :width="width"
        :height="height"
        :class="colorClass"
    >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
    </svg>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    width: {
        type: [String, Number],
        default: '24'
    },
    height: {
        type: [String, Number],
        default: '24'
    },
    color: {
        type: String,
        default: 'currentColor'
    }
})

const colorClass = computed(() => {
    if (props.color.startsWith('text-') || props.color.startsWith('stroke-')) {
        return props.color
    }
    return ''
})
</script> 