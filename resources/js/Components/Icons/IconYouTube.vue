<template>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 4H20V20H4V4Z" fill="transparent"/>
        <path d="M2.5 7C2.5 5.61929 3.61929 4.5 5 4.5H19C20.3807 4.5 21.5 5.61929 21.5 7V17C21.5 18.3807 20.3807 19.5 19 19.5H5C3.61929 19.5 2.5 18.3807 2.5 17V7Z" stroke="#242424" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M10 8.5L16 12L10 15.5V8.5Z" stroke="#242424" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
</template>

<script setup>
// No props or logic needed for this icon component
</script> 