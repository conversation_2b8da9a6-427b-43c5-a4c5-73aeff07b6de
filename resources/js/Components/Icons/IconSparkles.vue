<template>
  <svg 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg" 
    :class="classes"
  >
    <path d="M3 4C3 3.44772 3.44772 3 4 3H20C20.5523 3 21 3.44772 21 4V20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V4Z" fill="white"/>
    <path d="M7 12L9 14M9 14L11 12M9 14V7M15.7239 10.375V7.10938M14.2135 8.75H17.25" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M12.5 17.2109C14.3133 17.2109 15.7812 15.743 15.7812 13.9297C15.7812 12.1164 14.3133 10.6484 12.5 10.6484C10.6867 10.6484 9.21875 12.1164 9.21875 13.9297C9.21875 15.743 10.6867 17.2109 12.5 17.2109Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
</template>

<script setup>
defineProps({
  classes: {
    type: String,
    default: ''
  }
});
</script> 