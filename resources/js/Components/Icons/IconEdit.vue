<template>
    <svg
        :width="width"
        :height="height"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <g clip-path="url(#clip0_1497_10548)">
            <path
                d="M4.66699 4.66669H4.00033C3.6467 4.66669 3.30756 4.80716 3.05752 5.05721C2.80747 5.30726 2.66699 5.6464 2.66699 6.00002V12C2.66699 12.3536 2.80747 12.6928 3.05752 12.9428C3.30756 13.1929 3.6467 13.3334 4.00033 13.3334H10.0003C10.3539 13.3334 10.6931 13.1929 10.9431 12.9428C11.1932 12.6928 11.3337 12.3536 11.3337 12V11.3334"
                stroke="#242424"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
            <path
                d="M13.59 4.39001C13.8526 4.12745 14.0001 3.77133 14.0001 3.40001C14.0001 3.02869 13.8526 2.67257 13.59 2.41001C13.3274 2.14745 12.9713 1.99994 12.6 1.99994C12.2287 1.99994 11.8726 2.14745 11.61 2.41001L6 8.00001V10H8L13.59 4.39001Z"
                stroke="#242424"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
            <path
                d="M10.667 3.33331L12.667 5.33331"
                stroke="#242424"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
        </g>
        <defs>
            <clipPath id="clip0_1497_10548">
                <rect width="16" height="16" fill="white" />
            </clipPath>
        </defs>
    </svg>
</template>

<script setup>
const props = defineProps({
    width: {
        type: Number,
        default: 16
    },
    height: {
        type: Number,
        default: 16
    }
})
</script>
