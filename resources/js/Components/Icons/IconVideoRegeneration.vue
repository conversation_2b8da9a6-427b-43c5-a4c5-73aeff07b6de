<template>
    <svg 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        :width="width"
        :height="height"
        :class="colorClass"
    >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
    </svg>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    width: {
        type: [String, Number],
        default: '24'
    },
    height: {
        type: [String, Number],
        default: '24'
    },
    color: {
        type: String,
        default: 'currentColor'
    }
})

const colorClass = computed(() => {
    if (props.color.startsWith('text-') || props.color.startsWith('stroke-')) {
        return props.color
    }
    return ''
})
</script> 