<template>
    <svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M14.5 15C15.0304 15 15.5391 15.2107 15.9142 15.5858C16.2893 15.9609 16.5 16.4696 16.5 17C16.5 16.4696 16.7107 15.9609 17.0858 15.5858C17.4609 15.2107 17.9696 15 18.5 15C17.9696 15 17.4609 14.7893 17.0858 14.4142C16.7107 14.0391 16.5 13.5304 16.5 13C16.5 13.5304 16.2893 14.0391 15.9142 14.4142C15.5391 14.7893 15.0304 15 14.5 15ZM14.5 3C15.0304 3 15.5391 3.21071 15.9142 3.58579C16.2893 3.96086 16.5 4.46957 16.5 5C16.5 4.46957 16.7107 3.96086 17.0858 3.58579C17.4609 3.21071 17.9696 3 18.5 3C17.9696 3 17.4609 2.78929 17.0858 2.41421C16.7107 2.03914 16.5 1.53043 16.5 1C16.5 1.53043 16.2893 2.03914 15.9142 2.41421C15.5391 2.78929 15.0304 3 14.5 3ZM7.5 15C7.5 13.4087 8.13214 11.8826 9.25736 10.7574C10.3826 9.63214 11.9087 9 13.5 9C11.9087 9 10.3826 8.36786 9.25736 7.24264C8.13214 6.11742 7.5 4.5913 7.5 3C7.5 4.5913 6.86786 6.11742 5.74264 7.24264C4.61742 8.36786 3.0913 9 1.5 9C3.0913 9 4.61742 9.63214 5.74264 10.7574C6.86786 11.8826 7.5 13.4087 7.5 15Z" stroke="#242424" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
</template>

<script setup>
// No props or logic needed for this icon component
</script> 