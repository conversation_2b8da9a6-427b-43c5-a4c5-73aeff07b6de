<template>
    <teleport to="body">
        <div
            id="toast-success"
            class="fixed bottom-0 right-16 z-10 mb-16 flex items-center rounded-lg border border-border-action bg-surface-page p-16 shadow md:bottom-16 md:right-24"
            role="alert"
        >
            <div
                v-if="type === 'danger'"
                class="inline-flex size-32 flex-shrink-0 items-center justify-center rounded-lg text-icon-error"
            >
                <svg
                    class="size-20"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                >
                    <path
                        d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 11.793a1 1 0 1 1-1.414 1.414L10 11.414l-2.293 2.293a1 1 0 0 1-1.414-1.414L8.586 10 6.293 7.707a1 1 0 0 1 1.414-1.414L10 8.586l2.293-2.293a1 1 0 0 1 1.414 1.414L11.414 10l2.293 2.293Z"
                    />
                </svg>
            </div>

            <div
                v-else
                class="bg-stone inline-flex h-32 w-32 flex-shrink-0 items-center justify-center rounded-lg text-icon-action"
            >
                <svg
                    class="size-20"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                >
                    <path
                        d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"
                    />
                </svg>
            </div>
            <div class="mx-20 font-medium">
                {{ text }}
            </div>
            <button
                @click.prevent="$emit('handleClose')"
                type="button"
                class="focus:ring-gray-300 hover:bg-stone -mx-4 -my-4 ms-auto inline-flex items-center justify-center rounded-lg bg-surface-page p-8 text-icon-black focus:ring-2"
                data-dismiss-target="#toast-success"
                aria-label="Close"
            >
                <span class="sr-only">Close</span>
                <svg
                    class="size-[14px]"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 14 14"
                >
                    <path
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                    />
                </svg>
            </button>
        </div>
    </teleport>
</template>

<script setup>
import { onMounted } from "vue";
const emit = defineEmits(["handleClose"]);
defineProps({ text: String, type: String });

// Automatically close the toast after 7 seconds
onMounted(() => {
    setTimeout(() => {
        emit("handleClose");
    }, 7500);
});
</script>
