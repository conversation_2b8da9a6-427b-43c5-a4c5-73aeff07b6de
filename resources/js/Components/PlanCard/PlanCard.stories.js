import PlanCard from "./PlanCard.vue";

export default {
    component: PlanCard,
    tags: ["autodocs"],

    args: {},
};

export const Default = {
    args: {
        plan: {
            id: 1,
            stripe_price_id: "dummy",
            title: "Monthly Price",
            subtitle: "£40 / per month",
            description:
                "Build unlimited campaigns---Live benchmarking---Make data driven decisions---Save time and money building your fundraising campaigns",
            checkout_url: "https://buy.stripe.com/test_3cs1656b96rp5jO6op",
            created_at: "2024-08-27T11:35:41.000000Z",
            updated_at: "2024-08-27T11:35:41.000000Z",
            description_formatted: [
                "Build unlimited campaigns",
                "Live benchmarking",
                "Make data driven decisions",
                "Save time and money building your fundraising campaigns",
            ],
        },
    },
};
