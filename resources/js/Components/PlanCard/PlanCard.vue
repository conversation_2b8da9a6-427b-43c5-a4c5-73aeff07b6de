<template>
    <a
        :href="href ? href : plan.checkout_link_for_user"
        class="group flex cursor-pointer flex-col items-center justify-start gap-24 rounded-2xl border-2 border-surface-page bg-surface-page p-24 text-center shadow transition hover:border-border-action xl:p-40"
    >
        <h2 class="fs-xl font-bold">{{ plan.title }}</h2>

        <h3 class="fs-3xl relative font-extrabold">
            {{ plan.subtitle }}
            <IconRedX
                v-if="plan.is_discounted"
                class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
            />
        </h3>

        <p class="fs-3xl font-header font-extrabold">
            {{ plan.placeholder }}
        </p>

        <ul class="space-y-12 pl-28 text-left">
            <li
                class="relative"
                v-for="(item, id) of formattedDescriptions"
                :key="id"
                v-html="item"
            ></li>
        </ul>

        <Button v-if="buttonText" class="mt-auto h-48" color="action">
            {{ buttonText }}
        </Button>

        <Button
            v-else
            class="mt-auto h-48 transition focus:border-border-focus focus:bg-surface-focus focus:ring-border-focus group-hover:border-border-action group-hover:bg-surface-action group-hover:text-white"
        >
            Select
        </Button>
    </a>
</template>

<script setup>
import Button from "@/Components/Button/Button.vue";
import IconRedX from "../Icons/IconRedX.vue";
import { computed } from "vue";

const props = defineProps({
    plan: Object,
    buttonText: String,
    href: String,
});

// Emoji mapping object
const emojiMap = {
    ":mag:": "🔍",
    ":arrow_right:": "➡️",
    ":speech_balloon:": "💬",
    ":date:": "📅",
    ":rocket:": "🚀",
};

// Function to convert emoji shortcodes to actual emojis
const convertEmojis = (text) => {
    let result = text;
    for (const [shortcode, emoji] of Object.entries(emojiMap)) {
        result = result.replace(shortcode, emoji);
    }
    return result;
};

// Process all description items
const formattedDescriptions = computed(() => {
    if (!props.plan || !props.plan.description_formatted) return [];
    return props.plan.description_formatted.map((item) => convertEmojis(item));
});
</script>
