<template>
    <div>
        <label :for="id" class="mb-16 font-bold">
            <Tooltip v-if="tooltip" :label="label" :tooltip="tooltip" />

            <template v-else>
                {{ label }}
            </template>
        </label>

        <div v-if="showMinMax" class="fs-md mb-12 flex justify-between">
            <button @click="handleMin">Min</button>
            <button @click="handleMax">Max</button>
        </div>

        <input
            :id="id"
            type="range"
            :value="modelValue"
            @input="updateValue($event.target.value)"
            class="w-full cursor-pointer bg-surface-grey-light accent-surface-action-hover transition-all hover:accent-surface-focus focus:accent-surface-focus"
            :min="min"
            :max="max"
            :step="step"
        />
    </div>
</template>

<script setup>
import { defineEmits } from "vue";
import Tooltip from "../Tooltip/Tooltip.vue";

const props = defineProps({
    id: String,
    label: String,
    showMinMax: Boolean,
    modelValue: Number,
    min: {
        type: Number,
        default: 0,
    },
    max: {
        type: Number,
        default: 1,
    },
    step: {
        type: Number,
        default: 0.1,
    },
    tooltip: {
        type: String,
    },
});

// Define the event emitter for v-model support
const emit = defineEmits(["update:modelValue"]);

// Emit the new value whenever the input changes
const updateValue = (newValue) => {
    emit("update:modelValue", Number(newValue));
};

// Optional: Handle min and max button clicks (if necessary)
const handleMin = () => {
    updateValue(0); // Set to minimum value (or any custom logic)
};

const handleMax = () => {
    updateValue(1); // Set to maximum value (or any custom logic)
};
</script>
