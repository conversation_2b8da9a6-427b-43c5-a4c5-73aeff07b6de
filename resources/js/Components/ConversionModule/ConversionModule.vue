<template>
    <ConversationPravi fullWidth>
        <p class="mb-20">
            We've analysed the data to map out the key steps to build your
            audience and turn them into donors. Click any stage to learn more.
        </p>

        <div class="bg-white px-16 pb-88 pt-40">
            <ConversionFunnel
                @openModal="openModal"
                :awarenessButtons="awarenessButtons"
                :stewardshipButtons="stewardshipButtons"
                :buttonMapping="buttonMapping"
            />
        </div>
    </ConversationPravi>

    <Teleport to="body">
        <Modal
            :isVisible="showModal"
            @close="showModal = false"
            width="3xl"
            :rounded="true"
        >
            <div class="-mt-48 px-16 text-center lg:px-24">
                <h2 class="fs-md text-center font-bold">
                    {{ modalValues[activeModal].title }}
                </h2>
                <p class="fs-md my-24">
                    {{ modalValues[activeModal].text }}
                </p>

                <div
                    v-if="
                        activeModal === 'awareness' && awarenessButtons.length
                    "
                    class="mb-20 flex flex-wrap items-center justify-center gap-8"
                >
                    <component
                        v-for="(btnLabel, index) in awarenessButtons"
                        :is="getButtonComponent(btnLabel)"
                        :key="index"
                        @click="
                            () => modalAction(btnLabel, activeModal, btnLabel)
                        "
                    />
                </div>

                <div
                    v-if="
                        activeModal === 'stewardship' &&
                        stewardshipButtons.length
                    "
                    class="mb-20 flex flex-wrap items-center justify-center gap-8"
                >
                    <component
                        v-for="(btnLabel, index) in stewardshipButtons"
                        :is="getButtonComponent(btnLabel)"
                        :key="index"
                        @click="
                            () => modalAction(btnLabel, activeModal, btnLabel)
                        "
                    />
                </div>

                <Button
                    v-if="modalValues[activeModal].button"
                    @click="modalValues[activeModal].button.action"
                    color="action"
                    class="h-40"
                    >{{ modalValues[activeModal].button.text }}
                </Button>
            </div>
        </Modal>
    </Teleport>
</template>

<script setup>
import ConversionFunnel from "../ConversionFunnel/ConversionFunnel.vue";
import ConversationPravi from "../ConversationPravi/ConversationPravi.vue";
import Modal from "../Modal/Modal.vue";
import { ref, computed } from "vue";
import Button from "../Button/Button.vue";
import FacebookButton from "../SocialButtons/FacebookButton.vue";
import InstagramButton from "../SocialButtons/InstagramButton.vue";
import YouTubeButton from "../SocialButtons/YouTubeButton.vue";
import TikTokButton from "../SocialButtons/TikTokButton.vue";
import SnapchatButton from "../SocialButtons/SnapchatButton.vue";
import PinterestButton from "../SocialButtons/PinterestButton.vue";
import LinkedInButton from "../SocialButtons/LinkedInButton.vue";
import SocialDMsButton from "../SocialButtons/SocialDMsButton.vue";
import SMSButton from "../SocialButtons/SMSButton.vue";
import EmailButton from "../SocialButtons/EmailButton.vue";
import WhatsAppButton from "../SocialButtons/WhatsAppButton.vue";
import DirectMailButton from "../SocialButtons/DirectMailButton.vue";
import { useFindDonorsStore } from "@/stores/findDonors";
import PhoneCallButton from "../SocialButtons/PhoneCallButton.vue";

const emit = defineEmits(["new-tab"]);

const findDonorsStore = useFindDonorsStore();

const buttonMapping = {
    Facebook: FacebookButton,
    Instagram: InstagramButton,
    YouTube: YouTubeButton,
    TikTok: TikTokButton,
    Snapchat: SnapchatButton,
    Pinterest: PinterestButton,
    LinkedIn: LinkedInButton,
    "Social Media DMs": SocialDMsButton,
    SMS: SMSButton,
    Email: EmailButton,
    WhatsApp: WhatsAppButton,
    "Direct Mail": DirectMailButton,
    "Phone Call": PhoneCallButton,
};

const showModal = ref(false);
const activeModal = ref("awareness");

const modalValues = ref({
    awareness: {
        title: "Awareness",
        text: "Our data says your audience is on these social media channels. Click any logo below to start creating content.",
    },
    leadCapture: {
        title: "Lead Capture",
        text: "To grow your audience, capture contact details by offering a free resource, like an eBook or petition.",
        button: {
            text: "Create now",
            action: () => {
                modalAction("Lead Capture", "lead_capture");
            },
        },
    },
    stewardship: {
        title: "Stewardship",
        text: "Our data says your audience prefers these communication channels. Click any logo to create updates.",
    },
    campaigns: {
        title: "Campaigns",
        text: "Plan fundraising activities that resonate with your audience.",
        button: {
            text: "Get started",
            action: () => {
                modalAction("Campaigns", "campaigns");
            },
        },
    },
    // donations: {
    //     title: "Donations",
    //     text: "Turn your engaged supporters into regular donors.",
    //     button: {
    //         text: "Let's go",
    //         action: () => {
    //             modalAction("Donations", "donations");
    //         },
    //     },
    // },
});

const openModal = (name) => {
    activeModal.value = name;
    showModal.value = true;
};

const modalAction = (name, feature, platform_channel) => {
    let chatKey = "";
    if (feature === "awareness" || feature === "stewardship") {
        chatKey = platform_channel;
    } else {
        chatKey = feature;
    }
    chatKey = chatKey.toLowerCase().replace(/\s+/g, "-");

    findDonorsStore.addDashboardTab(name, feature, platform_channel, chatKey);
};

// Social/Communication buttons
// -----------------------------------
const awarenessButtons = computed(() => {
    let str = findDonorsStore.resultsAffinity.social_media || "";
    // Split the string into an array of button labels.
    return str
        .split(",")
        .map((label) => label.trim())
        .filter(Boolean);
});

const stewardshipButtons = computed(() => {
    let str = findDonorsStore.resultsAffinity.communication || "";
    // Split the string into an array of button labels.
    return str
        .split(",")
        .map((label) => label.trim())
        .filter(Boolean);
});

const getButtonComponent = (label) => {
    return buttonMapping[label] || null;
};
</script>
