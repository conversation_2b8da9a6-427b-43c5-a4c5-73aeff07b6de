<template>
    <div class="w-[585px] h-[679px] border border-[#DCDDDD] rounded-xl p-24 bg-white shadow-[0px_4px_4px_0px_rgba(0,0,0,0.25)]">
        <ContentHeader 
            :currentDate="currentDate" 
            @schedule-campaign="$emit('schedule-campaign')" 
        />
        
        <ContentDisplay 
            :isLoading="isLoading"
            :content="content"
            :waitingMessage="waitingMessage"
        />
    </div>
</template>

<script setup>
import ContentHeader from './ContentHeader.vue'
import ContentDisplay from './ContentDisplay.vue'

defineProps({
    currentDate: {
        type: String,
        required: true
    },
    isLoading: {
        type: Boolean,
        default: false
    },
    content: {
        type: String,
        default: ''
    },
    waitingMessage: {
        type: String,
        default: 'Wait for it...'
    }
})

defineEmits(['schedule-campaign'])
</script> 