<template>
    <div class="h-[550px]">
        <div v-if="isLoading" class="flex items-center justify-center h-full">
            <div class="animate-pulse text-text-grey text-lg font-body">Generating content...</div>
        </div>
        <div v-else-if="content" class="prose max-w-none text-black h-full overflow-y-auto" v-html="content"></div>
        <div v-else class="text-text-grey text-lg font-body h-full flex items-center">
            {{ waitingMessage }}
        </div>
    </div>
</template>

<script setup>
defineProps({
    isLoading: {
        type: Boolean,
        default: false
    },
    content: {
        type: String,
        default: ''
    },
    waitingMessage: {
        type: String,
        default: 'Wait for it...'
    }
})
</script> 