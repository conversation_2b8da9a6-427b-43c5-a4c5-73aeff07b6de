<template>
    <div class="w-full h-[calc(100vh-200px)] bg-white rounded-xl border border-[#DCDDDD] shadow-[0px_4px_4px_0px_rgba(0,0,0,0.25)] flex flex-col overflow-hidden">

        <!-- Header with Back Navigation -->
        <div class="flex items-center justify-between p-16 border-b border-[#DCDDDD] bg-white">
            <div class="flex items-center gap-16">
                <Button
                    @click="handleBackNavigation"
                    title="Back to campaign setup"
                    color="default"
                    size="sm"
                >
                    <VsxIcon iconName="ArrowLeft" :size="18" type="linear" />
                    Back
                </Button>
                <h2 class="text-lg font-semibold text-text-body">Campaign Content Creation</h2>
            </div>
            <div class="flex items-center gap-12">
                <Button
                    @click="handleScheduleNavigation"
                    color="action"
                    size="sm"
                    title="Schedule your campaign content"
                >
                    <VsxIcon iconName="Calendar" :size="16" type="linear" />
                    Schedule Campaign
                </Button>
            </div>
        </div>

        <!-- Platform Tabs -->
        <div class="flex gap-8 overflow-x-auto p-16 border-b border-[#DCDDDD] bg-surface-grey-2x-light">
            <div v-for="(count, platformId) in platformsData" :key="platformId"
                class="flex-shrink-0 flex items-center gap-8 px-16 py-8 rounded-lg cursor-pointer transition-all duration-200 relative"
                :class="activeTab === platformId
                    ? 'bg-white border-2 border-[#FF5EAB] shadow-sm'
                    : 'bg-white/50 border border-[#E5E7EB] hover:bg-white hover:border-[#FF5EAB]/30'"
                @click="setActiveTab(platformId)">
                <div class="flex items-center justify-center w-24 h-24 rounded-full relative"
                    :style="{ backgroundColor: getPlatformColor(platformId) + '20' }">
                    <VsxIcon :iconName="getPlatformIcon(platformId)" :size="16" :color="getPlatformColor(platformId)" type="linear" />
                </div>
                <div class="flex flex-col">
                    <span class="text-sm font-medium text-text-body">{{ getPlatformName(platformId) }}</span>
                    <span class="text-xs text-text-secondary">{{ count }} post{{ count > 1 ? 's' : '' }}</span>
                </div>
                <!-- Active indicator -->
                <div v-if="activeTab === platformId"
                    class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-[#FF5EAB] rounded-full">
                </div>
            </div>
        </div>

        <!-- Chat Content Area -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Chat Messages -->
            <div class="flex-1 overflow-y-auto p-16 min-h-0" ref="chatContainer">
                <div v-if="getTabMessages(activeTab).length === 0 && !isTabStreaming"
                    class="flex flex-col items-center justify-center h-full text-center">
                    <div class="mb-16">
                        <VsxIcon :iconName="getPlatformIcon(activeTab)" :size="48" :color="getPlatformColor(activeTab)"
                            type="linear" class="opacity-30" />
                    </div>
                    <h5 class="font-medium text-text-body mb-8">{{ getPlatformName(activeTab) }} Campaign Content</h5>
                    <p class="text-sm text-text-secondary mb-16 max-w-[400px]">
                        {{ getEmptyStateMessage() }}
                    </p>
                    <div v-if="isLoading || isAutoGenerating" class="flex items-center gap-8">
                        <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-[#FF5EAB]"></div>
                        <span class="text-sm text-[#FF5EAB]">Generating campaign content...</span>
                    </div>
                </div>

                <!-- Tab Chat Messages -->
                <TabChatMessages
                    v-if="getTabMessages(activeTab).length > 0 || (isTabStreaming && activeTab === activeTabId)"
                    :messages="getTabMessages(activeTab)"
                    :isStreaming="isTabStreaming && activeTab === activeTabId"
                    :streamingContent="isTabStreaming && activeTab === activeTabId ? currentStreamingContent : ''"
                    :activeTabId="activeTab"
                    :editTabMessage="editTabMessage"
                    :updateTabMessage="updateTabMessage"
                    ref="chatMessagesRef"
                />
            </div>

            <!-- Chat Input -->
            <div class="p-16 border-t border-[#DCDDDD] flex-shrink-0">
                <div class="relative">
                    <textarea
                        ref="chatInputRef"
                        v-model="chatInput"
                        @keydown="handleKeyDown"
                        @input="autoResizeTextarea"
                        :disabled="isLoading || isTabStreaming"
                        placeholder="Ask for specific campaign content adjustments..."
                        title="💡 Tip: Press Enter for new line, Shift+Enter to send"
                        :class="[
                            'w-full px-16 py-12 border border-[#DCDDDD] rounded-lg text-sm outline-none focus:ring-0 focus:border-[#FF5EAB] disabled:opacity-50 resize-none min-h-[48px] max-h-[200px] leading-relaxed transition-all duration-200',
                            chatInput.trim() ? 'pr-48' : 'pr-16'
                        ]"
                        rows="1"
                    ></textarea>

                    <!-- Send Button -->
                    <button
                        v-if="chatInput.trim()"
                        @click="sendMessage"
                        :disabled="isLoading || isTabStreaming || !chatInput.trim()"
                        class="absolute bottom-8 right-8 w-32 h-32 bg-[#FF5EAB] hover:bg-[#E54A96] disabled:opacity-50 disabled:cursor-not-allowed rounded-lg flex items-center justify-center transition-all duration-200"
                        title="Send message (Shift+Enter)"
                    >
                        <VsxIcon iconName="Send2" :size="16" color="white" type="linear" />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { router } from '@inertiajs/vue3'
import { useTabChat } from '@/composables/useTabChat'
import { VsxIcon } from 'vue-iconsax'
import TabChatMessages from './TabChatMessages.vue'
import Button from '../Button/Button.vue'

const props = defineProps({
    platformsData: {
        type: Object,
        required: true
    },
    campaignData: {
        type: Object,
        default: () => ({})
    },
    audienceData: {
        type: Object,
        default: () => ({})
    },
    socialUrl: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['content-generated'])

// Tab chat composable
const {
    activeTabId,
    isStreaming: isTabStreaming,
    streamedContent,
    currentStreamingContent,
    getTabMessages,
    getTabGeneratedContent,
    generateTabContent,
    sendTabMessage,
    editTabMessage,
    updateTabMessage
} = useTabChat()

// Local state
const activeTab = ref('')
const isLoading = ref(false)
const isAutoGenerating = ref(false)
const chatInput = ref('')
const chatInputRef = ref(null)
const chatContainer = ref(null)
const chatMessagesRef = ref(null)

// Platform configuration
const platformConfig = {
    twitter: { name: 'Twitter', icon: 'Hashtag', color: '#1DA1F2' },
    instagram: { name: 'Instagram', icon: 'Instagram', color: '#E4405F' },
    linkedin: { name: 'LinkedIn', icon: 'UserSquare', color: '#0077B5' },
    facebook: { name: 'Facebook', icon: 'Facebook', color: '#1877F2' },
    pinterest: { name: 'Pinterest', icon: 'Heart', color: '#BD081C' },
    tiktok: { name: 'TikTok', icon: 'VideoPlay', color: '#000000' },
    threads: { name: 'Threads', icon: 'MessageText', color: '#000000' },
    bluesky: { name: 'Bluesky', icon: 'Cloud', color: '#00A8E8' },
    youtube: { name: 'YouTube', icon: 'Youtube', color: '#FF0000' },
    blog: { name: 'Blog', icon: 'DocumentText1', color: '#6B7280' }
}

// Computed properties
const hasPlatformsData = computed(() => {
    return props.platformsData && Object.keys(props.platformsData).length > 0
})

// Methods
const getPlatformName = (platformId) => {
    return platformConfig[platformId]?.name || platformId.charAt(0).toUpperCase() + platformId.slice(1)
}

const getPlatformIcon = (platformId) => {
    return platformConfig[platformId]?.icon || 'DocumentText1'
}

const getPlatformColor = (platformId) => {
    return platformConfig[platformId]?.color || '#6B7280'
}

const getPlatformPostCount = (platformId) => {
    return props.platformsData[platformId] || 1
}

const getEmptyStateMessage = () => {
    const postCount = getPlatformPostCount(activeTab.value)
    return `I'll generate ${postCount} campaign post${postCount > 1 ? 's' : ''} for ${getPlatformName(activeTab.value)} based on your campaign data.`
}

const setActiveTab = (platformId) => {
    activeTab.value = platformId

    // Auto-generate content if tab is empty
    if (getTabMessages(platformId).length === 0 && !isTabStreaming.value) {
        generateContentForPlatform(platformId)
    }
}

const generateContentForPlatform = async (platformId) => {
    if (isLoading.value || isTabStreaming.value) return

    isLoading.value = true
    isAutoGenerating.value = true

    try {
        const context = buildContextFromCampaignData()
        const postCount = getPlatformPostCount(platformId)
        const platformName = getPlatformName(platformId)

        // Enhanced context with platform-specific and campaign-specific guidance
        let enhancedContext = `${context} `
        enhancedContext += `Create ${postCount} compelling campaign post${postCount > 1 ? 's' : ''} specifically optimized for ${platformName}. `

        // Add platform-specific guidance
        if (platformId === 'instagram') {
            enhancedContext += `Use visual storytelling, relevant hashtags, and engaging captions that encourage interaction. `
        } else if (platformId === 'facebook') {
            enhancedContext += `Focus on community building, shareable content, and clear calls-to-action. `
        } else if (platformId === 'twitter') {
            enhancedContext += `Keep it concise, use trending hashtags, and encourage retweets and engagement. `
        } else if (platformId === 'linkedin') {
            enhancedContext += `Maintain professional tone while highlighting impact and credibility. `
        } else if (platformId === 'tiktok') {
            enhancedContext += `Create engaging, trend-aware content that encourages participation and sharing. `
        }

        if (postCount > 1) {
            enhancedContext += `Separate multiple posts with <<----->>. Vary the messaging and approach for each post while maintaining campaign consistency. `
        }

        const result = await generateTabContent(
            platformId,
            platformId,
            {
                context: enhancedContext,
                post_count: postCount,
                platform: platformId,
                feature: 'campaign_content',
                audience_gender: props.audienceData?.gender || 'not specified',
                audience_age: props.audienceData?.ageRange?.[0] || 'not specified',
                audience_income: props.audienceData?.salary || 'not specified',
                campaign_type: props.campaignData?.campaignType || 'brand awareness campaign',
                fundraising_target: props.campaignData?.targetAmount || '',
                call_to_action: props.campaignData?.campaignDescription || '',
                selected_social_platform: platformName
            },
            props.audienceData
        )

        emit('content-generated', {
            platform: platformId,
            content: result.content,
            htmlContent: getTabGeneratedContent(platformId).htmlContent,
            postCount: postCount
        })

    } catch (error) {
        console.error('Error generating content:', error)
    } finally {
        isLoading.value = false
        isAutoGenerating.value = false
    }
}

const buildContextFromCampaignData = () => {
    let context = "Create engaging campaign content that drives action and awareness. "

    if (props.campaignData && Object.keys(props.campaignData).length > 0) {
        const { campaignType, targetAmount, campaignDescription } = props.campaignData
        context += `Campaign type: ${campaignType || 'brand awareness campaign'}. `

        if (targetAmount) {
            context += `Fundraising target: ${targetAmount}. `
            context += `Focus on compelling calls-to-action that encourage donations and support. `
        }

        if (campaignDescription) {
            context += `Campaign description: ${campaignDescription}. `
        }

        context += `Ensure content aligns with campaign goals and messaging. `
    }

    if (props.audienceData && Object.keys(props.audienceData).length > 0) {
        const { gender, ageRange, location, salary } = props.audienceData
        context += `Target audience: `

        if (gender) context += `${gender} gender, `
        if (ageRange?.[0]) context += `age range: ${ageRange[0]}, `
        if (location) context += `location: ${location}, `
        if (salary) context += `income level: ${salary}, `

        context += `tailor messaging and tone to resonate with this demographic. `
    }

    context += `Use persuasive language, emotional appeals, and clear value propositions. `
    context += `Include relevant hashtags and engagement-driving elements. `

    return context
}

const sendMessage = async () => {
    if (!chatInput.value.trim() || isLoading.value || isTabStreaming.value) return

    const message = chatInput.value.trim()
    chatInput.value = ''

    // Reset textarea height
    if (chatInputRef.value) {
        chatInputRef.value.style.height = 'auto'
        chatInputRef.value.rows = 1
    }

    try {
        await sendTabMessage(activeTab.value, message, {
            context: buildContextFromCampaignData(),
            feature: 'campaign_content',
            audience_gender: props.audienceData?.gender || 'not specified',
            audience_age: props.audienceData?.ageRange?.[0] || 'not specified',
            audience_income: props.audienceData?.salary || 'not specified',
            campaign_type: props.campaignData?.campaignType || 'brand awareness campaign',
            fundraising_target: props.campaignData?.targetAmount || '',
            call_to_action: props.campaignData?.campaignDescription || '',
            selected_social_platform: getPlatformName(activeTab.value),
            campaignData: props.campaignData,
            audienceData: props.audienceData
        })
    } catch (error) {
        console.error('Error sending message:', error)
    }
}

const handleKeyDown = (event) => {
    if (event.key === 'Enter' && event.shiftKey) {
        event.preventDefault()
        sendMessage()
    } else if (event.key === 'Enter') {
        // Allow normal Enter for new lines
        return
    }
}

const autoResizeTextarea = () => {
    if (chatInputRef.value) {
        chatInputRef.value.style.height = 'auto'
        chatInputRef.value.style.height = Math.min(chatInputRef.value.scrollHeight, 200) + 'px'
    }
}

const handleBackNavigation = () => {
    router.visit('/build/campaign/platforms')
}

const handleScheduleNavigation = () => {
    if (props.socialUrl) {
        window.open(props.socialUrl, '_blank')
    } else {
        router.visit('/build/calendar')
    }
}

// Initialize first tab
onMounted(() => {
    if (hasPlatformsData.value) {
        const firstPlatform = Object.keys(props.platformsData)[0]
        setActiveTab(firstPlatform)
    }
})
</script>
