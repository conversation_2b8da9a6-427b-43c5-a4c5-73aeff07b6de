<template>
    <div class="border-t border-gray-200 bg-gray-50 p-16">
        <div class="flex gap-8">
            <div class="flex-1 relative">
                <input 
                    v-model="userInput" 
                    @keyup.enter="handleSendMessage"
                    type="text" 
                    :placeholder="placeholder"
                    class="w-full h-40 px-16 py-8 border border-gray-300 rounded-lg focus:outline-none focus:border-[#FF5EAB] focus:ring-1 focus:ring-[#FF5EAB] text-gray-700 bg-white font-body text-14 pr-48"
                    :disabled="isLoading || isStreaming"
                >
                <button 
                    @click="handleSendMessage"
                    class="absolute right-8 top-1/2 transform -translate-y-1/2 bg-[#FF5EAB] text-white rounded-full w-24 h-24 flex items-center justify-center hover:bg-[#E54A96] transition-colors shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    :disabled="!userInput.trim() || isLoading || isStreaming"
                >
                    <div v-if="isLoading || isStreaming" class="w-12 h-12 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <svg v-else width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 3L8 13M8 3L5 6M8 3L11 6" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Quick action buttons -->
        <div class="flex gap-8 mt-12" v-if="showQuickActions">
            <button 
                @click="handleQuickAction('Improve this content')"
                class="px-12 py-6 text-12 bg-white border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors font-body"
                :disabled="isLoading || isStreaming"
            >
                Improve this content
            </button>
            <button 
                @click="handleQuickAction('Make it shorter')"
                class="px-12 py-6 text-12 bg-white border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors font-body"
                :disabled="isLoading || isStreaming"
            >
                Make it shorter
            </button>
            <button 
                @click="handleQuickAction('Add more details')"
                class="px-12 py-6 text-12 bg-white border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors font-body"
                :disabled="isLoading || isStreaming"
            >
                Add more details
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
    isLoading: {
        type: Boolean,
        default: false
    },
    isStreaming: {
        type: Boolean,
        default: false
    },
    placeholder: {
        type: String,
        default: 'Ask about this content or request changes...'
    },
    showQuickActions: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['send-message'])

const userInput = ref('')

const handleSendMessage = () => {
    if (!userInput.value.trim() || props.isLoading || props.isStreaming) return
    
    emit('send-message', userInput.value)
    userInput.value = ''
}

const handleQuickAction = (action) => {
    if (props.isLoading || props.isStreaming) return
    
    emit('send-message', action)
}
</script> 