<template>
    <div class="flex justify-between items-center w-full mb-24">
        <p class="font-header font-bold text-lg text-black">Today - {{ currentDate }}</p>
        <button 
            @click="$emit('schedule-campaign')" 
            class="flex items-center gap-8 text-lg font-header font-bold text-black hover:text-text-action transition-colors"
        >
            Schedule campaign
            <IconArrowNarrowRight class="w-24 h-24 text-current" />
        </button>
    </div>
</template>

<script setup>
import IconArrowNarrowRight from '@/Components/Icons/IconArrowNarrowRight.vue'

defineProps({
    currentDate: {
        type: String,
        required: true
    },
    socialUrl: {
        type: String,
        required: false
    }
})

defineEmits(['schedule-campaign'])
</script> 