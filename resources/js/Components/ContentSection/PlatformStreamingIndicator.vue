<template>
    <div class="flex items-start">
        <div class="bg-[#F8F9FA] rounded-[16px_4px_16px_16px] px-16 py-12 max-w-[90%] w-full">
            <!-- Simple Streaming Header -->
            <div class="flex items-center gap-12 mb-16">
                <div class="w-20 h-20 bg-gradient-to-r from-[#FF5EAB] to-[#E54A96] rounded-full flex items-center justify-center animate-pulse">
                    <VsxIcon iconName="Magicpen" :size="12" color="white" type="linear" />
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-700 text-sm">
                        {{ headerTitle }}
                    </h4>
                    <p class="text-xs text-gray-500 mt-1">
                        {{ headerSubtitle }}
                    </p>
                </div>
            </div>

            <!-- Streaming Content Display -->
            <div class="min-h-[100px]">
                <!-- Real-time content preview -->
                <div v-if="streamingContent" class="space-y-12">
                    <!-- Multiple posts detection -->
                    <div v-if="detectMultiplePosts(streamingContent)" class="space-y-12">
                        <div class="text-xs text-gray-500 mb-8">
                            Generating {{ getDetectedPostCount(streamingContent) }} posts...
                        </div>

                        <!-- Post previews -->
                        <div class="space-y-12">
                            <div v-for="(post, index) in getStreamingPostPreviews(streamingContent)" :key="index"
                                class="space-y-6">
                                <div class="text-xs text-gray-500">Post {{ index + 1 }}</div>
                                <div class="prose prose-sm max-w-none text-gray-700 text-sm leading-relaxed"
                                    v-html="convertToHtml(post)"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Single post content -->
                    <div v-else>
                        <div class="prose prose-sm max-w-none text-gray-700 text-sm leading-relaxed"
                            v-html="convertToHtml(streamingContent)"></div>
                    </div>
                </div>

                <!-- Initial loading state -->
                <div v-else class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-[#FF5EAB]"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import { VsxIcon } from 'vue-iconsax'
import Showdown from 'showdown'

const props = defineProps({
    streamingContent: {
        type: String,
        default: ''
    },
    platformId: {
        type: String,
        required: true
    },
    postCount: {
        type: Number,
        default: 1
    },
    isInitialGeneration: {
        type: Boolean,
        default: false
    }
})

const converter = new Showdown.Converter()

// Platform utilities
const getPlatformName = (platformId) => {
    const platforms = {
        'twitter': 'Twitter', 'instagram': 'Instagram', 'linkedin': 'LinkedIn',
        'facebook': 'Facebook', 'pinterest': 'Pinterest', 'tiktok': 'TikTok',
        'threads': 'Threads', 'bluesky': 'Bluesky', 'youtube': 'YouTube', 'blog': 'Blog'
    }
    return platforms[platformId] || platformId
}



// Content processing utilities
const convertToHtml = (content) => {
    return converter.makeHtml(content || '')
}

const detectMultiplePosts = (content) => {
    return content && content.includes('<<----->>') && content.split('<<----->>').length > 1
}

const getDetectedPostCount = (content) => {
    if (!content) return 0
    return content.split('<<----->>').filter(post => post.trim().length > 0).length
}

const getStreamingPostPreviews = (content) => {
    if (!content) return []
    return content.split('<<----->>').filter(post => post.trim().length > 0).map(post => post.trim())
}

// Computed properties
const platformName = computed(() => getPlatformName(props.platformId))

const headerTitle = computed(() => {
    if (props.isInitialGeneration) {
        return `Generating ${platformName.value} Content`
    }
    return 'AI Assistant'
})

const headerSubtitle = computed(() => {
    if (props.isInitialGeneration) {
        return `Creating ${props.postCount} post${props.postCount > 1 ? 's' : ''}`
    }
    return 'Generating response...'
})


</script>
