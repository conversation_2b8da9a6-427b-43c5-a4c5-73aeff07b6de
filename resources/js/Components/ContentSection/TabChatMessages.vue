<template>
    <div 
        class="flex flex-col gap-16 h-full p-16 bg-gray-50 transition-all duration-500 ease-in-out"
        :class="expandedMessageId ? 'expanded-chat-container' : ''"
        ref="chatContainer"
    >
        <div 
            v-for="message in visibleMessages" 
            :key="message.id"
            class="flex flex-col gap-8 transition-all duration-500 ease-in-out"
            :class="message.role === 'user' ? 'items-end' : 'items-start'"
        >
            <!-- User Message -->
            <div v-if="message.role === 'user'" class="flex flex-col items-end gap-4 max-w-[80%]">
                <div class="bg-[#FF5EAB] text-white rounded-[16px_16px_4px_16px] px-16 py-8">
                    <p class="font-body text-14 leading-relaxed">{{ message.content }}</p>
                </div>
            </div>
            
            <!-- Assistant Message -->
            <div v-else class="flex flex-col items-start max-w-[90%]">
                
                <!-- Normal Message Display -->
                <div class="bg-[#FFF5FA] rounded-[10px] px-16 py-8 shadow-sm">
                    <div 
                        v-if="message.htmlContent" 
                        class="prose prose-sm max-w-none prose-p:my-2 prose-ul:my-2 prose-ol:my-2 prose-li:my-0 text-[#000]"
                        v-html="message.htmlContent"
                    ></div>
                    <p v-else class="font-body text-14 leading-relaxed text-[#000]">{{ message.content }}</p>
                    
                    <!-- Edited indicator -->
                    <div v-if="message.isEdited" class="mt-4 text-10 text-gray-500 italic flex items-center gap-2">
                        <span>Edited {{ message.editedAt }}</span>
                    </div>

                    <div v-if="message.isError" class="mt-8 text-12 text-red-600 font-medium">
                        ⚠️ Error occurred
                    </div>
                    
                    <div v-if="getMessageMedia(message.id).length > 0" class="mt-12 pt-12 border-t border-gray-100">
                        <GeneratedMedia 
                            v-for="mediaItem in getMessageMedia(message.id)"
                            :key="mediaItem.id"
                            :mediaItem="mediaItem"
                            @refresh="handleRefreshMedia"
                            @cancel="handleCancelMedia"
                            @media-loaded="scrollToBottom"
                        />
                    </div>

                    <!-- Media Generation Buttons -->
                    <div class="flex items-center justify-between mt-12 pt-8 border-t border-gray-100">
                        <!-- Left side: Edit button -->
                        <div class="flex items-center gap-8">
                            <button 
                                v-if="message.isPost"
                                @click="openExpandedEdit(message)"
                                :disabled="isEditingMessage"
                                class="flex items-center justify-center w-28 h-28 text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                title="Edit post content"
                            >
                                <IconEdit width="36" height="36" />
                            </button>
                        </div>

                        <!-- Right side: Media generation and regeneration buttons -->
                        <div class="flex items-center gap-8">
                            <template v-if="hasCompletedMedia(getMessageMedia(message.id), 'image')">
                                <button 
                                    @click="handleRenewImage(message)"
                                    :disabled="isAnyMediaProcessActive"
                                    class="flex items-center gap-4 px-8 py-4 text-12 font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    title="Regenerate images for this content"
                                >
                                    <IconRefresh class="w-20 h-20" />
                                    <span>Renew Images</span>
                                </button>
                            </template>
                            <template v-else>
                                <button 
                                    @click="handleGenerateImage(message)"
                                    :disabled="isAnyMediaProcessActive"
                                    class="flex items-center gap-4 px-8 py-4 text-12 font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    title="Generate image for this content"
                                >
                                    <IconImage class="w-20 h-20" />
                                    <span>Generate Image</span>
                                </button>
                            </template>

                            <template v-if="hasCompletedMedia(getMessageMedia(message.id), 'video')">
                                <button 
                                    @click="handleRenewVideo(message)"
                                    :disabled="isAnyMediaProcessActive"
                                    class="flex items-center gap-4 px-8 py-4 text-12 font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    title="Regenerate videos for this content"
                                >
                                    <IconRefresh class="w-20 h-20" />
                                    <span>Renew Videos</span>
                                </button>
                            </template>
                            <template v-else>
                                <button 
                                    @click="handleGenerateVideo(message)"
                                    :disabled="isAnyMediaProcessActive"
                                    class="flex items-center gap-4 px-8 py-4 text-12 font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    title="Generate video for this content"
                                >
                                    <IconVideo class="w-20 h-20" />
                                    <span>Generate Video</span>
                                </button>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Streaming indicator -->
        <div v-if="isStreaming && streamingContent" class="flex flex-col items-start gap-4 max-w-[90%]">
            <div class="bg-white border border-gray-200 rounded-[16px_16px_16px_4px] px-16 py-8 shadow-sm">
                <!-- Streaming Header with Split Detection -->
                <div v-if="detectSplitInContent(streamingContent)" class="flex items-center justify-between mb-8 pb-8 border-b border-pink-100">
                    <div class="flex items-center gap-8">
                        <div class="w-20 h-20 bg-gradient-to-r from-[#FF5EAB] to-[#E54A96] rounded-full flex items-center justify-center animate-pulse">
                            <span class="text-white text-10 font-bold">📝</span>
                        </div>
                        <span class="font-body text-12 font-medium text-[#FF5EAB]">
                            Generating {{ getDetectedPostCount(streamingContent) }} posts...
                        </span>
                    </div>
                    <div class="px-8 py-4 bg-pink-50 rounded-lg">
                        <span class="text-10 font-medium text-[#FF5EAB]">✨ Auto-Split Detected</span>
                    </div>
                </div>

                <div 
                    class="prose prose-sm max-w-none prose-p:my-2 prose-ul:my-2 prose-ol:my-2 prose-li:my-0"
                    v-html="streamingContent"
                ></div>
                
                <!-- Split Preview -->
                <div v-if="detectSplitInContent(streamingContent)" class="mt-12 pt-8 border-t border-pink-100">
                    <div class="flex gap-4">
                        <div 
                            v-for="(post, index) in getStreamingPostPreviews(streamingContent)" 
                            :key="index"
                            class="flex-1 min-w-0"
                        >
                            <div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                                <div 
                                    class="h-full bg-gradient-to-r from-[#FF5EAB] to-[#E54A96] rounded-full transition-all duration-500"
                                    :style="`width: ${Math.min(100, (post.length / 200) * 100)}%`"
                                ></div>
                            </div>
                            <div class="text-10 text-gray-600 mt-1 text-center">Post {{ index + 1 }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center gap-8 mt-8">
                    <div class="w-12 h-12 border-2 border-[#FF5EAB] border-t-transparent rounded-full animate-spin"></div>
                    <span class="font-body text-12 text-[#FF5EAB]">Generating response...</span>
                </div>
            </div>
        </div>

        <!-- Media Regeneration Modal -->
        <MediaRegenerationModal
            :is-open="regenerationModal.isOpen"
            :media-type="regenerationModal.mediaType"
            :current-media="regenerationModal.currentMedia"
            :original-prompt="regenerationModal.originalPrompt"
            :is-regenerating="isRegenerating"
            @close="closeRegenerationModal"
            @regenerate="handleMediaRegeneration"
        />

        <!-- Floating regeneration notification -->
        <Transition name="notification">
            <div 
                v-if="isRegenerating && !regenerationModal.isOpen"
                class="fixed bottom-6 right-6 bg-white border border-gray-200 rounded-xl shadow-lg p-4 z-40 max-w-sm"
            >
                <div class="flex items-start gap-3">
                    <div class="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin flex-shrink-0 mt-0.5"></div>
                    <div>
                        <p class="font-medium text-gray-900 text-sm">Media Regenerating</p>
                        <p class="text-gray-600 text-xs mt-1">Your new content is being generated. You can continue using the app.</p>
                    </div>
                    <button 
                        @click="regenerationModal.isOpen = true"
                        class="text-gray-400 hover:text-gray-600 p-1 rounded"
                        title="Show regeneration modal"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </Transition>
    </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, computed } from 'vue'
import { useMediaGeneration } from '@/composables/useMediaGeneration'
import { useMediaRegeneration } from '@/composables/useMediaRegeneration'
import GeneratedMedia from '@/Components/MediaGeneration/GeneratedMedia.vue'
import MediaRegenerationModal from '@/Components/MediaGeneration/MediaRegenerationModal.vue'
import IconImage from '@/Components/Icons/IconImage.vue'
import IconVideo from '@/Components/Icons/IconVideo.vue'
import IconRefresh from '@/Components/Icons/IconRefresh.vue'
import IconEdit from '@/Components/Icons/IconEdit.vue'
import Showdown from 'showdown'

const props = defineProps({
    messages: {
        type: Array,
        default: () => []
    },
    isStreaming: {
        type: Boolean,
        default: false
    },
    streamingContent: {
        type: String,
        default: ''
    },
    activeTabId: {
        type: String,
        default: ''
    },
    editTabMessage: {
        type: Function,
        required: true
    },
    updateTabMessage: {
        type: Function,
        required: true
    },
    expandedMessageId: {
        type: String,
        default: null
    }
})

const emit = defineEmits(['message-expanded', 'message-collapsed'])

const chatContainer = ref(null)
const isRegenerating = ref(false)
const hasActiveMediaGeneration = ref(false)
const converter = new Showdown.Converter()

// Expanding edit functionality
const editPrompt = ref('')
const improvedContent = ref(null)
const isEditingMessage = ref(false)

const isAnyMediaProcessActive = computed(() => {
    if (isGenerating.value || isRegenerating.value || hasActiveMediaGeneration.value) return true
    
    for (const [messageId, mediaArray] of messageMedia.value.entries()) {
        if (mediaArray.some(media => media.status === 'pending' || media.status === 'processing')) {
            return true
        }
    }
    
    return false
})

const { 
    isGenerating, 
    generationError, 
    generatedMedia,
    generateMidjourneyImage,
    generateKlingVideo,
    checkTaskStatus,
    cancelTask,
    clearError
} = useMediaGeneration()

const {
    regenerationModal,
    hasCompletedMedia,
    openRegenerationModal,
    closeRegenerationModal,
    regenerateMedia
} = useMediaRegeneration()

const messageMedia = ref(new Map())

// Computed property to filter messages for display - hide user messages but keep them for LLM history
const visibleMessages = computed(() => {
    return props.messages.filter(message => message.role !== 'user' || message.isVisible === true)
})

// Expanding edit methods
const openExpandedEdit = (message) => {
    if (props.expandedMessageId) return // Prevent multiple expansions
    
    // Reset local state before opening
    editPrompt.value = ''
    improvedContent.value = null
    isEditingMessage.value = false
    
    emit('message-expanded', { messageId: message.id, message: message })
    
    setTimeout(() => {
        scrollToBottom()
    }, 100)
}

const closeExpandedEdit = () => {
    if (isEditingMessage.value) {
        if (!confirm('Content generation is in progress. Are you sure you want to close?')) {
            return
        }
    }
    
    const previousExpandedId = props.expandedMessageId
    emit('message-collapsed', { messageId: previousExpandedId })
}

const handleGenerateImprovement = async () => {
    if (!editPrompt.value.trim() || !props.expandedMessageId) return
    
    const message = props.messages.find(m => m.id === props.expandedMessageId)
    if (!message) return
    
    isEditingMessage.value = true
    improvedContent.value = null
    
    try {
        const result = await props.editTabMessage(
            props.activeTabId,
            message.id,
            editPrompt.value.trim(),
            {
                audience_gender: 'not specified',
                audience_age: 'not specified',
                audience_income: 'not specified',
                feature: 'social_media_content',
                selected_social_platform: props.activeTabId.charAt(0).toUpperCase() + props.activeTabId.slice(1)
            }
        )
        
        improvedContent.value = {
            content: result.improvedContent,
            htmlContent: converter.makeHtml(result.improvedContent)
        }
        
        setTimeout(() => {
            scrollToBottom()
        }, 100)
        
    } catch (error) {
        // Handle error silently
    } finally {
        isEditingMessage.value = false
    }
}

const handleApplyChanges = () => {
    if (!improvedContent.value || !props.expandedMessageId) return
    
    try {
        const messageIndex = props.messages.findIndex(msg => msg.id === props.expandedMessageId)
        if (messageIndex !== -1) {
            const updatedMessage = {
                ...props.messages[messageIndex],
                content: improvedContent.value.content,
                htmlContent: improvedContent.value.htmlContent,
                isEdited: true,
                editedAt: getCurrentTimestamp()
            }
            
            props.updateTabMessage(props.activeTabId, props.expandedMessageId, updatedMessage)
            
            emit('message-collapsed', { messageId: props.expandedMessageId })
        }
    } catch (error) {
        // Handle error silently
    }
}

const getMessageMedia = (messageId) => {
    return messageMedia.value.get(messageId) || []
}

const addMediaToMessage = (messageId, mediaItem) => {
    const currentMedia = messageMedia.value.get(messageId) || []
    currentMedia.push(mediaItem)
    messageMedia.value.set(messageId, currentMedia)
}

const handleGenerateImage = async (message) => {
    if (isGenerating.value || hasActiveMediaGeneration.value) return
    
    clearError()
    
    try {
        const prompt = extractTextFromMessage(message)
        
        if (!prompt) {
            alert('No text content found to generate image from')
            return
        }

        hasActiveMediaGeneration.value = true
        
        const mediaItem = await generateMidjourneyImage(prompt, {
            aspect_ratio: '1:1'
        })
        
        addMediaToMessage(message.id, mediaItem)
        pollTaskStatus(mediaItem.taskId)
        
    } catch (error) {
        // Handle error silently
    }
}

const handleGenerateVideo = async (message) => {
    if (isGenerating.value || hasActiveMediaGeneration.value) return
    
    clearError()
    
    try {
        const prompt = extractTextFromMessage(message)
        
        if (!prompt) {
            alert('No text content found to generate video from')
            return
        }

        hasActiveMediaGeneration.value = true

        const mediaItem = await generateKlingVideo(prompt, {
            duration: 5,
            aspect_ratio: '1:1'
        })
        
        addMediaToMessage(message.id, mediaItem)
        pollTaskStatus(mediaItem.taskId)
        
    } catch (error) {
        // Handle error silently
    }
}

const handleRenewImage = (message) => {
    const messageMediaArray = getMessageMedia(message.id)
    openRegenerationModal(
        message.id,
        messageMediaArray,
        'image',
        extractTextFromMessage(message)
    )
}

const handleRenewVideo = (message) => {
    const messageMediaArray = getMessageMedia(message.id)
    openRegenerationModal(
        message.id,
        messageMediaArray,
        'video',
        extractTextFromMessage(message)
    )
}

const handleMediaRegeneration = async (data) => {
    if (isRegenerating.value) return
    
    isRegenerating.value = true
    
    try {
        await regenerateMedia(data, replaceMediaForMessage, pollTaskStatus)
    } catch (error) {
        alert('Failed to regenerate media: ' + (error.message || 'Unknown error'))
        isRegenerating.value = false
    }
}

const replaceMediaForMessage = (messageId, newMediaItem) => {
    const currentMedia = messageMedia.value.get(messageId) || []
    
    const updatedMedia = currentMedia.filter(item => 
        !(item.type === newMediaItem.type && item.status === 'completed')
    )
    
    updatedMedia.push(newMediaItem)
    
    messageMedia.value.set(messageId, updatedMedia)
}

const extractTextFromMessage = (message) => {
    let text = message.content || ''
    
    if (message.htmlContent) {
        const tempDiv = document.createElement('div')
        tempDiv.innerHTML = message.htmlContent
        text = tempDiv.textContent || tempDiv.innerText || text
    }
    
    text = text.trim()
    if (text.length > 500) {
        text = text.substring(0, 500) + '...'
    }
    
    return text
}

const pollTaskStatus = async (taskId) => {
    const maxAttempts = 60
    let attempts = 0
    
    const poll = async () => {
        try {
            const status = await checkTaskStatus(taskId)
            
            updateMessageMediaStatus(taskId, status)
            
            if (status.status === 'completed' || status.status === 'failed') {
                scrollToBottom()
                
                if (status.status === 'completed' && regenerationModal.value.isOpen) {
                    setTimeout(() => {
                        closeRegenerationModal()
                        setTimeout(() => {
                            scrollToBottom()
                        }, 200)
                    }, 1000)
                }
                return
            }
            
            if (attempts < maxAttempts && (status.status === 'pending' || status.status === 'processing')) {
                attempts++
                setTimeout(poll, 5000)
            }
        } catch (error) {
            // Handle error silently
        }
    }
    
    setTimeout(poll, 2000)
}

const updateMessageMediaStatus = (taskId, statusData) => {
    for (const [messageId, mediaArray] of messageMedia.value.entries()) {
        const mediaIndex = mediaArray.findIndex(item => item.taskId === taskId)
        if (mediaIndex !== -1) {
            mediaArray[mediaIndex].status = statusData.status
            
            if (statusData.status === 'completed' || statusData.status === 'failed') {
                isRegenerating.value = false
                
                const hasOtherActiveMedia = Array.from(messageMedia.value.values()).some(mediaArray =>
                    mediaArray.some(item => 
                        item.taskId !== taskId && 
                        (item.status === 'pending' || item.status === 'processing')
                    )
                )
                
                if (!hasOtherActiveMedia) {
                    hasActiveMediaGeneration.value = false
                }
            }
            
            if (statusData.status === 'completed' && statusData.output) {
                const output = statusData.output
                const formattedResult = {}
                
                if (output.image_url || output.image_urls || output.temporary_image_urls) {
                    const images = []
                    
                    if (output.image_url) {
                        images.push(output.image_url)
                    }
                    if (output.image_urls && Array.isArray(output.image_urls)) {
                        images.push(...output.image_urls)
                    }
                    if (output.temporary_image_urls && Array.isArray(output.temporary_image_urls)) {
                        images.push(...output.temporary_image_urls)
                    }
                    
                    formattedResult.images = images.filter(url => url)
                }
                
                if (output.video_url || output.video_urls) {
                    const videos = []
                    
                    if (output.video_url) {
                        videos.push(output.video_url)
                    }
                    if (output.video_urls && Array.isArray(output.video_urls)) {
                        videos.push(...output.video_urls)
                    }
                    
                    formattedResult.videos = videos.filter(url => url)
                }
                
                mediaArray[mediaIndex].result = formattedResult
            }
            
            messageMedia.value.set(messageId, [...mediaArray])
            break
        }
    }
}

const handleRefreshMedia = async (taskId) => {
    try {
        await checkTaskStatus(taskId)
        scrollToBottom()
    } catch (error) {
        // Handle error silently
    }
}

const handleCancelMedia = async (taskId) => {
    try {
        await cancelTask(taskId)
        scrollToBottom()
    } catch (error) {
        // Handle error silently
    }
}

const detectSplitInContent = (content) => {
    if (!content) return false
    
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = content
    const textContent = tempDiv.textContent || tempDiv.innerText || content
    
    return textContent.includes('<<----->>')
}

const getDetectedPostCount = (content) => {
    if (!content) return 0
    
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = content
    const textContent = tempDiv.textContent || tempDiv.innerText || content
    
    const delimiter = '<<----->>'
    const parts = textContent.split(delimiter)
    return parts.length
}

const getStreamingPostPreviews = (content) => {
    if (!content) return []
    
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = content
    const textContent = tempDiv.textContent || tempDiv.innerText || content
    const posts = textContent.split('<<----->>' ).filter(post => post.trim().length > 0)
    
    return posts.map(post => post.trim())
}

const scrollToBottom = (behavior = 'smooth') => {
    if (chatContainer.value) {
        nextTick(() => {
            chatContainer.value.scrollTo({
                top: chatContainer.value.scrollHeight,
                behavior: behavior
            })
        })
    }
}

watch(() => props.streamingContent, (newContent, oldContent) => {
    if (props.isStreaming && newContent && newContent !== oldContent) {
        scrollToBottom('auto')
    }
})

watch(() => props.isStreaming, (newState, oldState) => {
    if (newState && !oldState) {
        scrollToBottom('smooth')
    } else if (!newState && oldState) {
        setTimeout(() => {
            scrollToBottom('smooth')
        }, 100)
    }
})

watch(() => props.messages, (newMessages, oldMessages) => {
    if (newMessages.length > oldMessages.length) {
        scrollToBottom('smooth')
    }
}, { deep: true })

watch(generatedMedia, () => {
    setTimeout(() => {
        scrollToBottom('smooth')
    }, 100)
}, { deep: true })

// Listen for external collapse signal
const resetExpandedState = () => {
    editPrompt.value = ''
    improvedContent.value = null
    isEditingMessage.value = false
}

// Watch for expandedMessageId prop changes
watch(() => props.expandedMessageId, (newValue) => {
    if (newValue === null) {
        // Reset all local states when no message is expanded
        resetExpandedState()
    }
})

onMounted(() => {
    if (props.messages.length > 0) {
        setTimeout(() => {
            scrollToBottom('auto')
        }, 50)
    }
})

defineExpose({
    scrollToBottom,
    resetExpandedState
})
</script>

<style scoped>
/* Chat container with overflow handling */
.overflow-y-auto::-webkit-scrollbar {
    width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Smooth scrolling */
.overflow-y-auto {
    scroll-behavior: smooth;
}

/* Button hover effects */
button:hover {
    transform: translateY(-1px);
}

button:active {
    transform: translateY(0);
}

/* Expanded chat container styling - takes full height of the tab container */
.expanded-chat-container {
    height: 100%;
    overflow-y: auto;
}

/* Expanded message container positioning - takes full width of right panel */
.expanded-message-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    background: rgba(249, 250, 251, 0.98);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
}

/* Expanded message box styling */
.expanded-message-box {
    width: 100%;
    max-width: 700px;
    max-height: 90%;
    overflow-y: auto;
    animation: expandMessage 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
    transform-origin: center center;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Expand animation keyframes */
@keyframes expandMessage {
    0% {
        transform: scale(0.9);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Notification transitions */
.notification-enter-active, .notification-leave-active {
    transition: all 0.3s ease;
}

.notification-enter-from {
    opacity: 0;
    transform: translateX(100%) scale(0.95);
}

.notification-leave-to {
    opacity: 0;
    transform: translateX(100%) scale(0.95);
}

.notification-enter-to, .notification-leave-from {
    opacity: 1;
    transform: translateX(0) scale(1);
}

/* Focus states for accessibility */
button:focus-visible {
    outline: 2px solid #FF5EAB;
    outline-offset: 2px;
}

textarea:focus {
    outline: none;
}

/* Enhanced prose styling for content display */
.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: #1f2937;
    font-weight: 600;
}

.prose p {
    color: #374151;
    line-height: 1.6;
}

.prose ul, .prose ol {
    color: #374151;
}

.prose strong {
    color: #1f2937;
    font-weight: 600;
}

/* Improved scrollbar for expanded content */
.expanded-message-box::-webkit-scrollbar {
    width: 6px;
}

.expanded-message-box::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 3px;
}

.expanded-message-box::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.expanded-message-box::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
</style> 