<template>
    <div
        class="bg-white h-full rounded-xl border border-[#DCDDDD] shadow-[0px_4px_4px_0px_rgba(0,0,0,0.25)] overflow-hidden flex flex-col">
        <!-- Header with Platform Tabs -->
        <div class="bg-[#F8F9FA] border-b border-[#DCDDDD] px-24 py-16 flex-shrink-0">

            <!-- Platform Tabs -->
            <div class="flex gap-8 overflow-x-auto">
                <Button
                    @click="handleBackNavigation"
                    title="Back to platform selection"
                >
                    <VsxIcon iconName="ArrowLeft" :size="18" type="linear" />
                </Button>
                <div v-for="(count, platformId) in platformsData" :key="platformId"
                    class="flex-shrink-0 flex items-center gap-8 px-16 py-8 rounded-lg cursor-pointer transition-all duration-200 relative"
                    :class="activeTab === platformId
                        ? 'bg-white border-2 border-[#FF5EAB] shadow-sm'
                        : 'bg-white/50 border border-[#E5E7EB] hover:bg-white hover:border-[#FF5EAB]/30'"
                    @click="setActiveTab(platformId)">
                    <div class="flex items-center justify-center w-24 h-24 rounded-full relative"
                        :style="{ backgroundColor: getPlatformColor(platformId) + '20' }">
                        <VsxIcon :iconName="getPlatformIcon(platformId)" :size="16"
                            :color="getPlatformColor(platformId)" type="linear" />

                        <!-- Status indicator -->
                        <div v-if="getPlatformStatus(platformId) === 'generating'"
                            class="absolute -top-1 -right-1 w-6 h-6 bg-[#FF5EAB] rounded-full flex items-center justify-center">
                            <div class="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                        </div>
                        <div v-else-if="getPlatformStatus(platformId) === 'complete'"
                            class="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                            <VsxIcon iconName="TickCircle" :size="12" color="white" type="linear" />
                        </div>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-sm font-medium text-text-body">{{ getPlatformName(platformId) }}</span>
                        <span class="text-xs text-text-secondary">{{ count }} post{{ count > 1 ? 's' : '' }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Interface -->
        <div class="flex flex-col flex-1 min-h-0">
            <!-- Chat Header -->
            <div class="p-16 border-b border-[#DCDDDD] bg-[#FAFBFC] flex items-center justify-between flex-shrink-0">
                <div class="flex items-center gap-8">
                    <div class="flex items-center justify-center w-32 h-32 rounded-full"
                        :style="{ backgroundColor: getPlatformColor(activeTab) + '20' }">
                        <VsxIcon :iconName="getPlatformIcon(activeTab)" :size="20" :color="getPlatformColor(activeTab)"
                            type="linear" />
                    </div>
                    <div>
                        <h4 class="font-medium text-text-body">{{ getPlatformName(activeTab) }}</h4>
                        <p class="text-xs text-text-secondary">Generate content for this platform</p>
                    </div>
                </div>
                <div class="flex items-center gap-12">
                    <span class="text-xs text-text-secondary">{{ currentDate }}</span>
                    <Button
                        @click="handleSchedulePost"
                        color="action"
                        size="md"
                        class="flex items-center gap-6 px-16 py-8 text-sm font-semibold shadow-sm"
                    >
                        <VsxIcon iconName="Calendar" :size="16" type="linear" />
                        <span>Schedule Post</span>
                    </Button>
                </div>
            </div>

            <!-- Chat Messages -->
            <div class="flex-1 overflow-y-auto p-16 min-h-0" ref="chatContainer">
                <div v-if="getTabMessages(activeTab).length === 0 && !isTabStreaming"
                    class="flex flex-col items-center justify-center h-full text-center">
                    <div class="mb-16">
                        <VsxIcon :iconName="getPlatformIcon(activeTab)" :size="48" :color="getPlatformColor(activeTab)"
                            type="linear" class="opacity-30" />
                    </div>
                    <h5 class="font-medium text-text-body mb-8">{{ getPlatformName(activeTab) }} content
                    </h5>
                    <p class="text-sm text-text-secondary mb-16 max-w-[400px]">
                        {{ getEmptyStateMessage() }}
                    </p>
                    <div v-if="isLoading || isAutoGenerating" class="flex items-center gap-8">
                        <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-[#FF5EAB]"></div>
                        <span class="text-sm text-gray-600">{{ isAutoGenerating ? 'Generating content...' : 'Initializing...' }}</span>
                    </div>

                    <!-- Manual generation option (only show if automatic generation is complete) -->
                    <div v-else-if="!isAutoGenerating && !isLoading" class="mt-8">
                        <Button
                            size="sm"
                            variant="outline"
                            @click="generateContentForPlatform(activeTab)"
                            class="text-xs"
                        >
                            <VsxIcon iconName="Refresh" :size="14" type="linear" />
                            <span class="ml-4">Generate New Content</span>
                        </Button>
                    </div>
                </div>

                <div v-else class="space-y-16">
                    <div v-for="message in getTabMessages(activeTab)" :key="message.id" class="flex flex-col gap-8"
                        :class="message.role === 'user' ? 'items-end' : 'items-start'">

                        <div v-if="message.role === 'user'"
                            class="bg-[#FF5EAB] text-white rounded-[16px_16px_4px_16px] px-16 py-12 max-w-[80%]">
                            <p class="text-sm">{{ message.content }}</p>
                        </div>

                        <div v-else class="w-full">
                            <!-- Check if this is a completed post message (single or multiple posts) -->
                            <div v-if="message.isPost" class="space-y-12">
                                <!-- Individual Post Cards -->
                                <div class="space-y-12">
                                    <PostCard
                                        v-for="(post, index) in getIndividualPosts(message.content)"
                                        :key="`${message.id}-post-${index}`"
                                        :postContent="post.content"
                                        :postHtmlContent="post.htmlContent"
                                        :postIndex="index"
                                        :totalPosts="getPostCount(message.content)"
                                        :platformId="activeTab"
                                        :platformName="getPlatformName(activeTab)"
                                        :platformIcon="getPlatformIcon(activeTab)"
                                        :platformColor="getPlatformColor(activeTab)"
                                        :postMedia="getPostMedia(message.id, index)"
                                        :isLoading="isLoading || isTabStreaming"
                                        :isAnyMediaProcessActive="isAnyMediaProcessActive"
                                        :isEdited="post.isEdited"
                                        :editedAt="post.editedAt"
                                        @update-post="handleUpdatePost(message.id, $event)"
                                        @edit-post="handleEditPost(message.id, $event)"
                                        @duplicate-post="handleDuplicatePost(message.id, $event)"
                                        @delete-post="handleDeletePost(message.id, $event)"
                                        @generate-image="handleGeneratePostImage(message.id, $event)"
                                        @generate-video="handleGeneratePostVideo(message.id, $event)"
                                        @renew-image="handleRenewPostImage(message.id, $event)"
                                        @renew-video="handleRenewPostVideo(message.id, $event)"
                                        @refresh-media="handleRefreshMedia"
                                        @cancel-media="handleCancelMedia"
                                    />
                                </div>
                            </div>

                            <!-- Single post or non-post message (original layout) -->
                            <div v-else class="bg-[#F8F9FA] rounded-[16px_4px_16px_16px] px-16 py-12 max-w-[90%] w-full">
                                <!-- Inline editing mode -->
                                <div v-if="editingMessageId === message.id" class="space-y-12">
                                    <textarea
                                        v-model="editingContent"
                                        @keydown="handleInlineEditKeyDown"
                                        class="w-full px-12 py-8 bg-white rounded-lg text-sm outline-none border-0 resize-none min-h-[80px] leading-relaxed"
                                        placeholder="Edit your content..."
                                        rows="3"
                                    ></textarea>
                                    <div class="flex items-center gap-8">
                                        <Button size="sm" color="action" @click="saveInlineEdit">
                                            Save
                                        </Button>
                                        <Button size="sm" variant="outline" @click="cancelInlineEdit">
                                            Cancel
                                        </Button>
                                    </div>
                                </div>
                                <!-- Normal display mode -->
                                <div v-else>
                                    <div v-if="message.htmlContent"
                                        class="prose prose-sm max-w-none text-gray-700 cursor-pointer hover:bg-gray-50 rounded p-8 transition-colors"
                                        @click="startInlineEdit(message)"
                                        v-html="message.htmlContent">
                                    </div>
                                    <p v-else
                                        class="text-sm text-gray-700 cursor-pointer hover:bg-gray-50 rounded p-8 transition-colors"
                                        @click="startInlineEdit(message)">
                                        {{ message.content }}
                                    </p>
                                    <div class="text-xs text-gray-400 mt-4 opacity-0 hover:opacity-100 transition-opacity">
                                        Click to edit
                                    </div>
                                </div>

                                <!-- Edited indicator -->
                                <div v-if="message.isEdited"
                                    class="mt-8 text-xs text-gray-500 italic flex items-center gap-4">
                                    <VsxIcon iconName="Edit" :size="12" type="linear" />
                                    <span>Edited {{ message.editedAt }}</span>
                                </div>

                                <div v-if="message.isError" class="mt-8 text-12 text-red-600 font-medium">
                                    ⚠️ Error occurred
                                </div>

                                <!-- Generated Media Display -->
                                <div v-if="getMessageMedia(message.id).length > 0"
                                    class="mt-12 pt-12">
                                    <GeneratedMedia v-for="mediaItem in getMessageMedia(message.id)" :key="mediaItem.id"
                                        :mediaItem="mediaItem" @refresh="handleRefreshMedia" @cancel="handleCancelMedia" />
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex items-center justify-between mt-12 pt-8">
                                    <!-- Left side: Edit button -->
                                    <div class="flex items-center gap-8">
                                        <button v-if="message.isPost" @click="handleEditContent(message)"
                                            :disabled="isLoading || isTabStreaming || isAnyMediaProcessActive"
                                            class="flex items-center justify-center w-28 h-28 text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                            title="Edit post content">
                                            <VsxIcon iconName="Edit" :size="16" type="linear" />
                                        </button>
                                    </div>

                                    <!-- Right side: Media generation buttons -->
                                    <div class="flex items-center gap-8">
                                        <template v-if="hasCompletedMedia(getMessageMedia(message.id), 'image')">
                                            <button @click="handleRenewImage(message)" :disabled="isAnyMediaProcessActive"
                                                class="flex items-center gap-4 px-8 py-4 text-12 font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                title="Regenerate images for this content">
                                                <VsxIcon iconName="Refresh" :size="16" type="linear" />
                                                <span>Renew Images</span>
                                            </button>
                                        </template>
                                        <template v-else>
                                            <button @click="handleGenerateImage(message)"
                                                :disabled="isAnyMediaProcessActive"
                                                class="flex items-center gap-4 px-8 py-4 text-12 font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                title="Generate image for this content">
                                                <VsxIcon iconName="Gallery" :size="16" type="linear" />
                                                <span>Generate Image</span>
                                            </button>
                                        </template>

                                        <template v-if="hasCompletedMedia(getMessageMedia(message.id), 'video')">
                                            <button @click="handleRenewVideo(message)" :disabled="isAnyMediaProcessActive"
                                                class="flex items-center gap-4 px-8 py-4 text-12 font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                title="Regenerate videos for this content">
                                                <VsxIcon iconName="Refresh" :size="16" type="linear" />
                                                <span>Renew Videos</span>
                                            </button>
                                        </template>
                                        <template v-else>
                                            <button @click="handleGenerateVideo(message)"
                                                :disabled="isAnyMediaProcessActive"
                                                class="flex items-center gap-4 px-8 py-4 text-12 font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                title="Generate video for this content">
                                                <VsxIcon iconName="VideoPlay" :size="16" type="linear" />
                                                <span>Generate Video</span>
                                            </button>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Streaming indicator -->
                    <PlatformStreamingIndicator
                        v-if="isTabStreaming && activeTab === activeTabId"
                        :streamingContent="currentStreamingContent"
                        :platformId="activeTab"
                        :postCount="getPlatformPostCount(activeTab)"
                        :isInitialGeneration="isInitialContentGeneration"
                    />
                </div>

                <!-- Expanded Edit Interface -->
                <div v-if="expandedMessageId" class="flex items-start mt-16">
                    <div class="bg-[#F8F9FA] rounded-[16px_4px_16px_16px] px-16 py-12 w-full">
                        <div class="flex items-center justify-between mb-16">
                            <h4 class="text-sm font-medium text-gray-700">Edit Content</h4>
                            <button @click="closeExpandedEdit"
                                class="text-gray-400 hover:text-gray-600 transition-colors">
                                <VsxIcon iconName="CloseCircle" :size="16" type="linear" />
                            </button>
                        </div>

                        <!-- Edit Prompt Input -->
                        <div class="space-y-16">
                            <textarea v-model="editPrompt" :disabled="isEditingMessage"
                                placeholder="How would you like to improve this content?"
                                class="w-full px-16 py-12 bg-gray-50 rounded-lg text-sm outline-none focus:bg-white disabled:opacity-50 resize-none border-0"
                                rows="2"></textarea>

                            <div class="flex items-center justify-between">
                                <Button color="action" size="sm" @click="handleGenerateImprovement"
                                    :disabled="!editPrompt.trim() || isEditingMessage">
                                    {{ isEditingMessage ? 'Improving...' : 'Improve' }}
                                </Button>

                                <div v-if="isEditingMessage" class="flex items-center gap-8">
                                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF5EAB]"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Improved Content Preview -->
                        <div v-if="improvedContent && !isEditingMessage" class="mt-16 space-y-12">
                            <div class="bg-gray-50 rounded-lg p-16">
                                <div v-if="improvedContent.htmlContent"
                                    class="prose prose-sm max-w-none text-gray-700 mb-12"
                                    v-html="improvedContent.htmlContent"></div>
                                <p v-else class="text-gray-700 mb-12">{{ improvedContent.content }}</p>

                                <!-- Apply button -->
                                <div class="flex justify-end">
                                    <Button size="sm" color="action" @click="handleApplyChanges">
                                        Apply
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="p-16 border-t border-[#DCDDDD] flex-shrink-0">
                <div class="relative">
                    <textarea
                        ref="chatInputRef"
                        v-model="chatInput"
                        @keydown="handleKeyDown"
                        @input="autoResizeTextarea"
                        :disabled="isLoading || isTabStreaming"
                        placeholder="Ask for specific content adjustments..."
                        title="💡 Tip: Press Enter for new line, Shift+Enter to send"
                        :class="[
                            'w-full px-16 py-12 border border-[#DCDDDD] rounded-lg text-sm outline-none focus:ring-0 focus:border-[#FF5EAB] disabled:opacity-50 resize-none min-h-[48px] max-h-[200px] leading-relaxed transition-all duration-200',
                            chatInput.trim() ? 'pr-48' : 'pr-16'
                        ]"
                        rows="1"
                    ></textarea>

                    <!-- Send Button - Only visible when there's content -->
                    <button
                        v-if="chatInput.trim()"
                        @click="sendMessage"
                        :disabled="isLoading || isTabStreaming"
                        class="absolute right-8 bottom-12 w-32 h-32 bg-[#FF5EAB] hover:bg-[#E54A96] disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-sm"
                        title="Send message (Shift+Enter)"
                    >
                        <VsxIcon iconName="Send" :size="16" type="linear" />
                    </button>
                </div>
            </div>
        </div>

        <!-- Media Regeneration Modal -->
        <MediaRegenerationModal v-if="regenerationModal.isOpen" :isOpen="regenerationModal.isOpen"
            :mediaType="regenerationModal.mediaType" :originalPrompt="regenerationModal.originalPrompt"
            @close="closeRegenerationModal" @regenerate="handleMediaRegeneration" />
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { router } from '@inertiajs/vue3'
import { useTabChat } from '@/composables/useTabChat'
import { useMediaGeneration } from '@/composables/useMediaGeneration'
import { useMediaRegeneration } from '@/composables/useMediaRegeneration'
import { VsxIcon } from 'vue-iconsax'
import GeneratedMedia from '@/Components/MediaGeneration/GeneratedMedia.vue'
import MediaRegenerationModal from '@/Components/MediaGeneration/MediaRegenerationModal.vue'
import PlatformStreamingIndicator from './PlatformStreamingIndicator.vue'
import PostCard from './PostCard.vue'
import Showdown from 'showdown'
import Button from '../Button/Button.vue'


const props = defineProps({
    platformsData: {
        type: Object,
        required: true
    },
    questionsData: {
        type: Object,
        default: () => ({})
    },
    socialUrl: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['content-generated'])

// Tab chat composable
const {
    activeTabId,
    isStreaming: isTabStreaming,
    streamedContent,
    currentStreamingContent,
    getTabMessages,
    getTabGeneratedContent,
    generateTabContent,
    sendTabMessage,
    editTabMessage,
    updateTabMessage
} = useTabChat()

// Local state
const activeTab = ref('')
const isLoading = ref(false)
const chatInput = ref('')
const chatContainer = ref(null)
const chatInputRef = ref(null)
const isRegenerating = ref(false)
const hasActiveMediaGeneration = ref(false)
const isAutoGenerating = ref(false)
const converter = new Showdown.Converter()

// Edit functionality state
const expandedMessageId = ref(null)
const editPrompt = ref('')
const improvedContent = ref(null)
const isEditingMessage = ref(false)

// Inline editing state
const editingMessageId = ref(null)
const editingContent = ref('')
const originalEditingContent = ref('')
// Media generation composables
const {
    isGenerating,
    generateMidjourneyImage,
    generateKlingVideo,
    checkTaskStatus,
    cancelTask,
    clearError
} = useMediaGeneration()

const {
    regenerationModal,
    hasCompletedMedia,
    openRegenerationModal,
    closeRegenerationModal,
    regenerateMedia
} = useMediaRegeneration()

// Media storage for messages
const messageMedia = ref(new Map())

// Computed properties
const currentDate = computed(() => {
    const formatter = new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric'
    })
    return formatter.format(new Date())
})

const isAnyMediaProcessActive = computed(() => {
    if (isGenerating.value || isRegenerating.value || hasActiveMediaGeneration.value) return true

    for (const [, mediaArray] of messageMedia.value.entries()) {
        if (mediaArray.some(media => media.status === 'pending' || media.status === 'processing')) {
            return true
        }
    }

    return false
})

// Helper methods for streaming indicator
const getPlatformPostCount = (platformId) => {
    return props.platformsData[platformId] || 1
}

const isInitialContentGeneration = computed(() => {
    // Check if this is the initial content generation (no messages yet)
    return getTabMessages(activeTab.value).length === 0 && isTabStreaming.value
})

// Helper function for empty state messaging
const getEmptyStateMessage = () => {
    if (isAutoGenerating.value) {
        return 'Content is being generated automatically for all platforms...'
    }
    if (isLoading.value) {
        return 'Preparing to generate content...'
    }
    return 'Content will be generated automatically when you visit this page'
}

// Helper function to get platform status
const getPlatformStatus = (platformId) => {
    const messages = getTabMessages(platformId)
    const hasContent = messages.some(msg => msg.role === 'assistant' && msg.isInitialContent)

    if (hasContent) {
        return 'complete'
    }

    if ((isTabStreaming.value && activeTab.value === platformId) ||
        (isLoading.value && activeTab.value === platformId) ||
        isAutoGenerating.value) {
        return 'generating'
    }

    return 'pending'
}

// Helper functions for multiple posts handling
const hasMultiplePosts = (content) => {
    return content && content.includes('<<----->>') && content.split('<<----->>').length > 1
}

const getPostCount = (content) => {
    if (!content) return 0
    return content.split('<<----->>').filter(post => post.trim().length > 0).length
}

const getIndividualPosts = (content) => {
    if (!content) return []

    const posts = content.split('<<----->>').filter(post => post.trim().length > 0)
    return posts.map((post, index) => ({
        content: post.trim(),
        htmlContent: converter.makeHtml(post.trim()),
        isEdited: false,
        editedAt: ''
    }))
}

// Helper function to get media for specific post index
const getPostMedia = (messageId, postIndex) => {
    const allMedia = getMessageMedia(messageId)
    // Filter media by post index if we implement post-specific media tracking
    // For now, return all media for the message
    return allMedia
}

// Platform utilities
const getPlatformName = (platformId) => {
    const platforms = {
        'twitter': 'Twitter',
        'instagram': 'Instagram',
        'linkedin': 'LinkedIn',
        'facebook': 'Facebook',
        'pinterest': 'Pinterest',
        'tiktok': 'TikTok',
        'threads': 'Threads',
        'bluesky': 'Bluesky',
        'youtube': 'YouTube',
        'blog': 'Blog'
    }
    return platforms[platformId] || platformId
}

const getPlatformIcon = (platformId) => {
    const icons = {
        'twitter': 'Hashtag',
        'instagram': 'Instagram',
        'linkedin': 'UserSquare',
        'facebook': 'Facebook',
        'pinterest': 'Heart',
        'tiktok': 'VideoPlay',
        'threads': 'MessageText',
        'bluesky': 'Cloud',
        'youtube': 'Youtube',
        'blog': 'DocumentText1'
    }
    return icons[platformId] || 'DocumentText1'
}

const getPlatformColor = (platformId) => {
    const colors = {
        'twitter': '#1DA1F2',
        'instagram': '#E4405F',
        'linkedin': '#0077B5',
        'facebook': '#1877F2',
        'pinterest': '#BD081C',
        'tiktok': '#000000',
        'threads': '#000000',
        'bluesky': '#00A8E8',
        'youtube': '#FF0000',
        'blog': '#6B7280'
    }
    return colors[platformId] || '#6B7280'
}

// Methods
const setActiveTab = (platformId) => {
    activeTab.value = platformId
    activeTabId.value = platformId
}

const generateContentForPlatform = async (platformId) => {
    if (isLoading.value || isTabStreaming.value) return

    isLoading.value = true

    try {
        const context = buildContextFromData()
        const postCount = getPlatformPostCount(platformId)

        // Enhanced context with post count information
        const enhancedContext = `${context} Generate ${postCount} post${postCount > 1 ? 's' : ''} for ${getPlatformName(platformId)}. ${postCount > 1 ? 'Separate multiple posts with <<----->>' : ''}`

        const result = await generateTabContent(
            platformId,
            platformId,
            {
                context: enhancedContext,
                post_count: postCount,
                platform: platformId
            },
            props.questionsData
        )

        emit('content-generated', {
            platform: platformId,
            content: result.content,
            htmlContent: getTabGeneratedContent(platformId).htmlContent,
            postCount: postCount
        })

    } catch (error) {
        console.error('Error generating content:', error)
    } finally {
        isLoading.value = false
    }
}

const sendMessage = async () => {
    if (!chatInput.value.trim() || isLoading.value || isTabStreaming.value) return

    const message = chatInput.value.trim()
    chatInput.value = ''

    // Reset textarea height
    if (chatInputRef.value) {
        chatInputRef.value.style.height = 'auto'
        chatInputRef.value.rows = 1
    }

    try {
        await sendTabMessage(activeTab.value, message, {
            context: buildContextFromData(),
            questionsData: props.questionsData
        })
    } catch (error) {
        console.error('Error sending message:', error)
    }
}

// Enhanced keyboard handling for chat input
const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
        if (event.shiftKey) {
            // Shift+Enter: Send message
            event.preventDefault()
            sendMessage()
        }
        // Enter alone: Allow new line (default behavior)
    }
}

// Auto-resize textarea functionality
const autoResizeTextarea = () => {
    if (!chatInputRef.value) return

    const textarea = chatInputRef.value
    textarea.style.height = 'auto'

    const scrollHeight = textarea.scrollHeight
    const maxHeight = 200 // max-h-[200px] in pixels
    const minHeight = 48  // min-h-[48px] in pixels

    if (scrollHeight <= maxHeight) {
        textarea.style.height = Math.max(scrollHeight, minHeight) + 'px'
        textarea.style.overflowY = 'hidden'
    } else {
        textarea.style.height = maxHeight + 'px'
        textarea.style.overflowY = 'auto'
    }
}

const buildContextFromData = () => {
    let context = ""

    if (props.questionsData && Object.keys(props.questionsData).length > 0) {
        const { business_description, target_audience, post_tone, key_messages, call_to_action } = props.questionsData
        context += `Business context: ${business_description || 'Not specified'}. `
        context += `Target audience: ${target_audience || 'General audience'}. `
        context += `Tone: ${post_tone || 'professional'}. `
        if (key_messages) context += `Key messages: ${key_messages}. `
        if (call_to_action) context += `Call to action: ${call_to_action}. `
    }

    const platformName = getPlatformName(activeTab.value)
    const postCount = getPlatformPostCount(activeTab.value)

    context += `Create content specifically optimized for ${platformName}. `

    // Add platform-specific guidelines
    if (platformName === 'Twitter') {
        context += `Keep posts concise and engaging, suitable for Twitter's format. `
    } else if (platformName === 'LinkedIn') {
        context += `Use a professional tone suitable for LinkedIn's business audience. `
    } else if (platformName === 'Instagram') {
        context += `Create visually appealing content suitable for Instagram's visual platform. `
    }

    if (postCount > 1) {
        context += `Generate ${postCount} distinct posts with varied approaches to the same topic. `
    }

    return context
}

// Navigation methods
const handleBackNavigation = () => {
    router.visit('/build/posts')
}

const handleSchedulePost = () => {
    router.visit('/build/calendar')
}

// Media management functions
const getMessageMedia = (messageId) => {
    return messageMedia.value.get(messageId) || []
}

const addMediaToMessage = (messageId, mediaItem) => {
    const currentMedia = messageMedia.value.get(messageId) || []
    currentMedia.push(mediaItem)
    messageMedia.value.set(messageId, currentMedia)
}

// Edit functionality methods
const openExpandedEdit = (message) => {
    if (expandedMessageId.value) return // Prevent multiple expansions

    // Reset local state before opening
    editPrompt.value = ''
    improvedContent.value = null
    isEditingMessage.value = false

    expandedMessageId.value = message.id

    setTimeout(() => {
        scrollToBottom()
    }, 100)
}

const closeExpandedEdit = () => {
    if (isEditingMessage.value) {
        if (!confirm('Content generation is in progress. Are you sure you want to close?')) {
            return
        }
    }

    expandedMessageId.value = null
    editPrompt.value = ''
    improvedContent.value = null
    isEditingMessage.value = false
}

const handleGenerateImprovement = async () => {
    if (!editPrompt.value.trim() || !expandedMessageId.value) return

    const messages = getTabMessages(activeTab.value)
    const message = messages.find(m => m.id === expandedMessageId.value)
    if (!message) return

    isEditingMessage.value = true
    improvedContent.value = null

    try {
        const result = await editTabMessage(
            activeTab.value,
            message.id,
            editPrompt.value.trim(),
            {
                audience_gender: 'not specified',
                audience_age: 'not specified',
                audience_income: 'not specified',
                feature: 'social_media_content',
                selected_social_platform: activeTab.value.charAt(0).toUpperCase() + activeTab.value.slice(1)
            }
        )

        improvedContent.value = {
            content: result.improvedContent,
            htmlContent: converter.makeHtml(result.improvedContent)
        }

        setTimeout(() => {
            scrollToBottom()
        }, 100)

    } catch (error) {
        console.error('Error generating improvement:', error)
    } finally {
        isEditingMessage.value = false
    }
}

const handleApplyChanges = () => {
    if (!improvedContent.value || !expandedMessageId.value) return

    try {
        // The updateTabMessage function expects: (tabId, messageId, newContent, newHtmlContent)
        const updatedMessage = updateTabMessage(
            activeTab.value,
            expandedMessageId.value,
            improvedContent.value.content,
            improvedContent.value.htmlContent
        )

        if (updatedMessage) {
            console.log('Message updated successfully:', updatedMessage)
            closeExpandedEdit()
        }
    } catch (error) {
        console.error('Error applying changes:', error)
        alert('Failed to apply changes. Please try again.')
    }
}

// Inline editing methods
const startInlineEdit = (message) => {
    if (editingMessageId.value || isLoading.value || isTabStreaming.value) return

    editingMessageId.value = message.id
    editingContent.value = message.content || ''
    originalEditingContent.value = message.content || ''

    // Focus on the textarea after Vue updates the DOM
    nextTick(() => {
        const textarea = document.querySelector(`textarea[v-model="editingContent"]`)
        if (textarea) {
            textarea.focus()
            textarea.setSelectionRange(textarea.value.length, textarea.value.length)
        }
    })
}

const saveInlineEdit = async () => {
    if (!editingMessageId.value || !editingContent.value.trim()) return

    try {
        const updatedMessage = updateTabMessage(
            activeTab.value,
            editingMessageId.value,
            editingContent.value.trim(),
            converter.makeHtml(editingContent.value.trim())
        )

        if (updatedMessage) {
            cancelInlineEdit()
        }
    } catch (error) {
        console.error('Error saving inline edit:', error)
        alert('Failed to save changes. Please try again.')
    }
}

const cancelInlineEdit = () => {
    editingMessageId.value = null
    editingContent.value = ''
    originalEditingContent.value = ''
}

const handleInlineEditKeyDown = (event) => {
    if (event.key === 'Enter' && event.shiftKey) {
        // Shift+Enter: Save changes
        event.preventDefault()
        saveInlineEdit()
    } else if (event.key === 'Escape') {
        // Escape: Cancel editing
        event.preventDefault()
        cancelInlineEdit()
    }
    // Enter alone: Allow new line (default behavior)
}

// Action button handlers
const handleEditContent = (message) => {
    openExpandedEdit(message)
}

const handleGenerateImage = async (message) => {
    if (isGenerating.value || hasActiveMediaGeneration.value) return

    clearError()

    try {
        const prompt = extractTextFromMessage(message)

        if (!prompt) {
            alert('No text content found to generate image from')
            return
        }

        hasActiveMediaGeneration.value = true

        const mediaItem = await generateMidjourneyImage(prompt, {
            aspect_ratio: '1:1'
        })

        addMediaToMessage(message.id, mediaItem)
        pollTaskStatus(mediaItem.taskId)

    } catch (error) {
        console.error('Error generating image:', error)
    }
}

const handleGenerateVideo = async (message) => {
    if (isGenerating.value || hasActiveMediaGeneration.value) return

    clearError()

    try {
        const prompt = extractTextFromMessage(message)

        if (!prompt) {
            alert('No text content found to generate video from')
            return
        }

        hasActiveMediaGeneration.value = true

        const mediaItem = await generateKlingVideo(prompt, {
            duration: 5,
            aspect_ratio: '1:1'
        })

        addMediaToMessage(message.id, mediaItem)
        pollTaskStatus(mediaItem.taskId)

    } catch (error) {
        console.error('Error generating video:', error)
    }
}

const handleRenewImage = (message) => {
    const messageMediaArray = getMessageMedia(message.id)
    openRegenerationModal(
        message.id,
        messageMediaArray,
        'image',
        extractTextFromMessage(message)
    )
}

const handleRenewVideo = (message) => {
    const messageMediaArray = getMessageMedia(message.id)
    openRegenerationModal(
        message.id,
        messageMediaArray,
        'video',
        extractTextFromMessage(message)
    )
}

const extractTextFromMessage = (message) => {
    if (message.content) {
        // Remove HTML tags if present
        return message.content.replace(/<[^>]*>/g, '').trim()
    }
    return ''
}

// Task polling function
const pollTaskStatus = async (taskId) => {
    const maxAttempts = 60
    let attempts = 0

    const poll = async () => {
        try {
            const status = await checkTaskStatus(taskId)

            updateMessageMediaStatus(taskId, status)

            if (status.status === 'completed' || status.status === 'failed') {
                hasActiveMediaGeneration.value = false
                return
            }

            if (attempts < maxAttempts && (status.status === 'pending' || status.status === 'processing')) {
                attempts++
                setTimeout(poll, 5000)
            }
        } catch (error) {
            console.error('Error polling task status:', error)
            hasActiveMediaGeneration.value = false
        }
    }

    setTimeout(poll, 2000)
}

// Update media status in messageMedia
const updateMessageMediaStatus = (taskId, status) => {
    for (const [messageId, mediaArray] of messageMedia.value.entries()) {
        const mediaIndex = mediaArray.findIndex(media => media.taskId === taskId)
        if (mediaIndex !== -1) {
            mediaArray[mediaIndex].status = status.status

            if (status.status === 'completed') {
                // Store the full status response so GeneratedMedia.vue can access output
                mediaArray[mediaIndex].result = status
            }

            messageMedia.value.set(messageId, [...mediaArray])
            break
        }
    }
}

// Media refresh and cancel handlers
const handleRefreshMedia = async (taskId) => {
    try {
        await checkTaskStatus(taskId)
    } catch (error) {
        console.error('Error refreshing media:', error)
    }
}

const handleCancelMedia = async (taskId) => {
    try {
        await cancelTask(taskId)
    } catch (error) {
        console.error('Error canceling media:', error)
    }
}

// Media regeneration handler
const handleMediaRegeneration = async (data) => {
    if (isRegenerating.value) return

    isRegenerating.value = true

    try {
        await regenerateMedia(data, replaceMediaForMessage, pollTaskStatus)
    } catch (error) {
        console.error('Failed to regenerate media:', error)
        isRegenerating.value = false
    }
}

// PostCard event handlers
const handleUpdatePost = (messageId, postData) => {
    try {
        // Find the message and update the specific post
        const messages = getTabMessages(activeTab.value)
        const message = messages.find(m => m.id === messageId)
        if (!message) return

        // Split the content into individual posts
        const posts = message.content.split('<<----->>').filter(post => post.trim().length > 0)

        // Update the specific post
        if (posts[postData.index]) {
            posts[postData.index] = postData.content

            // Rejoin the posts
            const updatedContent = posts.join('\n\n<<------->>\n\n')

            // Update the message
            updateTabMessage(
                activeTab.value,
                messageId,
                updatedContent,
                converter.makeHtml(updatedContent)
            )
        }
    } catch (error) {
        console.error('Error updating post:', error)
    }
}

const handleEditPost = (messageId, postIndex) => {
    // For now, use the existing edit functionality
    // Could be enhanced to focus on specific post
    const messages = getTabMessages(activeTab.value)
    const message = messages.find(m => m.id === messageId)
    if (message) {
        handleEditContent(message)
    }
}

const handleDuplicatePost = (messageId, postIndex) => {
    try {
        const messages = getTabMessages(activeTab.value)
        const message = messages.find(m => m.id === messageId)
        if (!message) return

        const posts = message.content.split('<<----->>').filter(post => post.trim().length > 0)

        if (posts[postIndex]) {
            // Duplicate the post by inserting it after the original
            posts.splice(postIndex + 1, 0, posts[postIndex])

            const updatedContent = posts.join('\n\n<<------->>\n\n')

            updateTabMessage(
                activeTab.value,
                messageId,
                updatedContent,
                converter.makeHtml(updatedContent)
            )
        }
    } catch (error) {
        console.error('Error duplicating post:', error)
    }
}

const handleDeletePost = (messageId, postIndex) => {
    try {
        const messages = getTabMessages(activeTab.value)
        const message = messages.find(m => m.id === messageId)
        if (!message) return

        const posts = message.content.split('<<----->>').filter(post => post.trim().length > 0)

        // Don't allow deleting if only one post remains
        if (posts.length <= 1) return

        // Remove the post
        posts.splice(postIndex, 1)

        const updatedContent = posts.join('\n\n<<------->>\n\n')

        updateTabMessage(
            activeTab.value,
            messageId,
            updatedContent,
            converter.makeHtml(updatedContent)
        )
    } catch (error) {
        console.error('Error deleting post:', error)
    }
}

// Media generation handlers for individual posts
const handleGeneratePostImage = async (messageId, postIndex) => {
    if (isGenerating.value || hasActiveMediaGeneration.value) return

    clearError()

    try {
        const messages = getTabMessages(activeTab.value)
        const message = messages.find(m => m.id === messageId)
        if (!message) return

        const posts = message.content.split('<<----->>').filter(post => post.trim().length > 0)
        const postContent = posts[postIndex]?.trim()

        if (!postContent) {
            alert('No content found for this post')
            return
        }

        hasActiveMediaGeneration.value = true

        const mediaItem = await generateMidjourneyImage(postContent, {
            aspect_ratio: '1:1'
        })

        addMediaToMessage(messageId, mediaItem)
        pollTaskStatus(mediaItem.taskId)

    } catch (error) {
        console.error('Error generating image for post:', error)
    }
}

const handleGeneratePostVideo = async (messageId, postIndex) => {
    if (isGenerating.value || hasActiveMediaGeneration.value) return

    clearError()

    try {
        const messages = getTabMessages(activeTab.value)
        const message = messages.find(m => m.id === messageId)
        if (!message) return

        const posts = message.content.split('<<----->>').filter(post => post.trim().length > 0)
        const postContent = posts[postIndex]?.trim()

        if (!postContent) {
            alert('No content found for this post')
            return
        }

        hasActiveMediaGeneration.value = true

        const mediaItem = await generateKlingVideo(postContent, {
            duration: 5,
            aspect_ratio: '1:1'
        })

        addMediaToMessage(messageId, mediaItem)
        pollTaskStatus(mediaItem.taskId)

    } catch (error) {
        console.error('Error generating video for post:', error)
    }
}

const handleRenewPostImage = (messageId, postIndex) => {
    const messages = getTabMessages(activeTab.value)
    const message = messages.find(m => m.id === messageId)
    if (!message) return

    const posts = message.content.split('<<----->>').filter(post => post.trim().length > 0)
    const postContent = posts[postIndex]?.trim()

    if (!postContent) return

    const messageMediaArray = getMessageMedia(messageId)
    openRegenerationModal(
        messageId,
        messageMediaArray,
        'image',
        postContent
    )
}

const handleRenewPostVideo = (messageId, postIndex) => {
    const messages = getTabMessages(activeTab.value)
    const message = messages.find(m => m.id === messageId)
    if (!message) return

    const posts = message.content.split('<<----->>').filter(post => post.trim().length > 0)
    const postContent = posts[postIndex]?.trim()

    if (!postContent) return

    const messageMediaArray = getMessageMedia(messageId)
    openRegenerationModal(
        messageId,
        messageMediaArray,
        'video',
        postContent
    )
}

// Replace media for message (used by regeneration)
const replaceMediaForMessage = (messageId, newMediaItem) => {
    const currentMedia = messageMedia.value.get(messageId) || []
    const updatedMedia = currentMedia.filter(media => media.type !== newMediaItem.type)
    updatedMedia.push(newMediaItem)
    messageMedia.value.set(messageId, updatedMedia)
}

// Automatic content generation
const startAutomaticContentGeneration = async () => {
    if (isAutoGenerating.value) return // Prevent duplicate generation

    isAutoGenerating.value = true
    const platforms = Object.keys(props.platformsData)

    try {
        // Generate content for each platform sequentially
        for (const platformId of platforms) {
            // Check if platform already has content
            const existingMessages = getTabMessages(platformId)
            const hasContent = existingMessages.some(msg => msg.role === 'assistant' && msg.isInitialContent)

            if (!hasContent) {
                // Set active tab to show generation progress
                setActiveTab(platformId)

                // Generate content for this platform
                await generateContentForPlatform(platformId)

                // Small delay between platforms to show progress
                await new Promise(resolve => setTimeout(resolve, 500))
            }
        }
    } catch (error) {
        console.error('Error during automatic content generation:', error)
    } finally {
        isAutoGenerating.value = false
    }
}

// Scroll to bottom function
const scrollToBottom = (behavior = 'smooth') => {
    if (chatContainer.value) {
        nextTick(() => {
            chatContainer.value.scrollTo({
                top: chatContainer.value.scrollHeight,
                behavior: behavior
            })
        })
    }
}

// Initialize first tab and start automatic content generation
onMounted(() => {
    const platforms = Object.keys(props.platformsData)
    if (platforms.length > 0) {
        setActiveTab(platforms[0])
        // Start automatic content generation for all platforms
        startAutomaticContentGeneration()
    }
})

// Watch for platform changes
watch(() => props.platformsData, (newData) => {
    const platforms = Object.keys(newData)
    if (platforms.length > 0 && !activeTab.value) {
        setActiveTab(platforms[0])
    }
}, { immediate: true })
</script>


