<template>
    <Teleport to="body">
        <Transition name="modal" appear>
            <div 
                v-if="isOpen" 
                class="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6 lg:p-8"
                @keydown.esc="closeModal"
                tabindex="0"
            >
 
                <div 
                    class="absolute inset-0 bg-black/60 backdrop-blur-md transition-all duration-300"
                    @click.self="closeModal"
                ></div>
                

                <div class="relative p-12 bg-white rounded-md max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl ring-1 ring-black/5">
            
                    <div class="relative px-8 py-6 bg-gradient-to-r from-slate-50 to-gray-50 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-32 h-32 bg-gradient-to-br from-[#FF5EAB] to-[#E54A96] rounded-2xl flex items-center justify-center shadow-lg">
                                    <IconEdit class="w-20 h-20 text-white" />
                                </div>
                                <div>
                                    <h2 class="text-2xl font-bold text-gray-900">Edit Content</h2>
                                    <p class="text-sm text-gray-600 mt-1">Improve your content with AI assistance</p>
                                </div>
                            </div>
                            <button 
                                @click="closeModal"
                                class="p-2.5 text-gray-400 hover:text-gray-600 hover:bg-white/80 rounded-xl transition-all duration-200"
                            >
                                <svg class="w-20 h-20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

    
                    <div class="p-4 max-h-[70vh] overflow-y-auto">
                        <div class="flex gap-6 h-full pt-6">
                            <div class="flex-1 w-2/3 space-y-6">
                                <!-- Current Content -->
                                <div>
                                    <div class="flex items-center gap-2 mb-4">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        <h3 class="text-lg font-semibold text-gray-900">Current Content</h3>
                                    </div>
                                    <div class="p-6 shadow-sm bg-surface-grey-light/30 rounded-lg">
                                        <div 
                                            v-if="originalContent.htmlContent" 
                                            class="prose prose-gray max-w-none text-gray-700 leading-relaxed"
                                            v-html="originalContent.htmlContent"
                                        ></div>
                                        <p v-else class="text-gray-700 whitespace-pre-wrap leading-relaxed">{{ originalContent.content }}</p>
                                    </div>
                                </div>

                                <!-- Divider Line -->
                                <div class="border-t border-gray-200 my-6"></div>

                                <!-- Enhanced Loading State -->
                                <div v-if="isProcessing" class="space-y-4">
                                    <div class="flex items-center gap-2">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                        <h3 class="text-lg font-semibold text-gray-900">Generating</h3>
                                    </div>
                                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100 shadow-sm">
                                        <div class="flex items-center gap-4">
                                            <div class="relative">
                                                <div class="w-8 h-8 border-3 border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
                                                <div class="absolute inset-0 w-8 h-8 border-3 border-transparent border-r-blue-300 rounded-full animate-spin animation-delay-150"></div>
                                            </div>
                                            <div>
                                                <p class="text-lg font-medium text-blue-900">Generating improved content...</p>
                                                <p class="text-sm text-blue-700 mt-1">Our AI is analyzing and enhancing your content</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Enhanced Preview Improved Content -->
                                <div v-if="improvedContent && !isProcessing" class="space-y-4 mb-8">
                                    <div class="flex items-center gap-2">
                                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <h3 class="text-lg font-semibold text-gray-900">Improved Content Preview</h3>
                                        <div class="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                                            Ready to apply
                                        </div>
                                    </div>
                                    <div class="bg-pink-light/30 rounded-lg p-6">
                                        <div 
                                            v-if="improvedContent.htmlContent" 
                                            class="prose prose-gray max-w-none text-gray-700 leading-relaxed"
                                            v-html="improvedContent.htmlContent"
                                        ></div>
                                        <p v-else class="text-gray-700 whitespace-pre-wrap leading-relaxed">{{ improvedContent.content }}</p>
                                    </div>
                                </div>
                            </div>


                            <div class="w-1/3 space-y-6 flex flex-col h-full">

                                <div class="space-y-4 mb-12">
                                    <p class="text-bold font-medium text-gray-700">Quick suggestions:</p>
                                    <div class=" space-y-6">
                                        <button
                                            v-for="suggestion in quickSuggestions"
                                            :key="suggestion"
                                            @click="editPrompt = suggestion"
                                            :disabled="isProcessing"
                                            class="w-full p-6 text-sm bg-white border border-surface-grey-light text-gray-700 rounded-lg hover:bg-[#FF5EAB]/5 hover:text-[#FF5EAB] hover:border-[#FF5EAB]/20 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm text-left"
                                        >
                                            {{ suggestion }}
                                        </button>
                                    </div>
                                </div>

                                <div class="flex-2"></div>
                                
                                <!-- Edit Prompt Input - Bottom -->
                                <div class="space-y-4">
                                    <textarea
                                        v-model="editPrompt"
                                        placeholder="How would you like to improve this content?"
                                        rows="9"
                                        :disabled="isProcessing"
                                        class="w-full p-6 text-sm bg-white border border-surface-grey-light text-gray-700 rounded-lg hover:border-[#FF5EAB]/20 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm text-left"
                                    />
                                    
                                    <button 
                                        @click="handleGenerateImprovement"
                                        :disabled="!editPrompt.trim() || isProcessing"
                                        class="w-full p-8 bg-surface-grey-light/50 border border-surface-grey-light text-black hover:to-[#D63384] rounded-lg shadow-lg shadow-pink-500/25 disabled:shadow-none transition-all duration-200 disabled:opacity-50"
                                    >
                                        {{ isProcessing ? 'Generating...' : 'Generate Improvement' }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modern footer with better buttons -->
                    <div v-if="improvedContent && !isProcessing" class="px-12 py-12 bg-gradient-to-r from-gray-50 to-slate-50 border-t border-gray-100 flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            {{ isProcessing ? 'Processing your request...' : 'Preview your changes before applying them' }}
                        </div>
                        
                        <div class="flex items-center gap-3">
                            <button 
                                @click="closeModal"
                                :disabled="isProcessing"
                                class="p-6 border border-surface-grey-light rounded-md"
                            >
                                Cancel
                            </button>
                            
                            <button 
                                @click="handleApplyChanges"
                                class="p-6 bg-gradient-to-r from-[#FF5EAB] to-[#E54A96] hover:from-[#E54A96] hover:to-[#D63384] text-white shadow-lg shadow-pink-500/25 disabled:shadow-none transition-all duration-200"
                            >
                                Apply Changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </Transition>
    </Teleport>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import IconEdit from '@/Components/Icons/IconEdit.vue'
import Showdown from 'showdown'

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    },
    originalContent: {
        type: Object,
        required: true,
        default: () => ({ content: '', htmlContent: '' })
    },
    isProcessing: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['close', 'generate-improvement', 'apply-changes'])

const editPrompt = ref('')
const improvedContent = ref(null)
const converter = new Showdown.Converter()

const quickSuggestions = [
    'Make it more engaging and fun',
    'Add relevant emojis',
    'Make it shorter and punchier',
    'Change tone to professional',
    'Add trending hashtags',
]

const handleGenerateImprovement = () => {
    if (!editPrompt.value.trim()) return
    
    improvedContent.value = null
    emit('generate-improvement', {
        originalContent: props.originalContent,
        editPrompt: editPrompt.value.trim()
    })
}

const handleApplyChanges = () => {
    if (!improvedContent.value) return
    
    emit('apply-changes', improvedContent.value)
    closeModal()
}

const closeModal = () => {
    if (props.isProcessing) {
        if (!confirm('Content generation is in progress. Are you sure you want to close?')) {
            return
        }
    }
    
    editPrompt.value = ''
    improvedContent.value = null
    emit('close')
}

const handleKeydown = (event) => {
    if (!props.isOpen) return
    
    if (event.key === 'Escape') {
        closeModal()
    }
}

const setImprovedContent = (content) => {
    improvedContent.value = {
        content: content,
        htmlContent: converter.makeHtml(content)
    }
}

watch(() => props.isOpen, (isOpen) => {
    if (isOpen) {
        document.body.style.overflow = 'hidden'
    } else {
        document.body.style.overflow = ''
        editPrompt.value = ''
        improvedContent.value = null
    }
})

onMounted(() => {
    document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
    document.body.style.overflow = ''
})

defineExpose({
    setImprovedContent
})
</script>

<style scoped>
/* Modern modal transitions */
.modal-enter-active, .modal-leave-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-enter-from, .modal-leave-to {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

.modal-enter-to, .modal-leave-from {
    opacity: 1;
    transform: scale(1) translateY(0);
}

/* Backdrop transitions */
.modal-enter-active .absolute,
.modal-leave-active .absolute {
    transition: all 0.4s ease-out;
}

.modal-enter-from .absolute,
.modal-leave-to .absolute {
    opacity: 0;
    backdrop-filter: blur(0px);
}

.modal-enter-to .absolute,
.modal-leave-from .absolute {
    opacity: 1;
    backdrop-filter: blur(12px);
}

/* Enhanced modal container transitions */
.modal-enter-active .relative,
.modal-leave-active .relative {
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-enter-from .relative,
.modal-leave-to .relative {
    opacity: 0;
    transform: scale(0.85) translateY(-40px) rotateX(10deg);
}

.modal-enter-to .relative,
.modal-leave-from .relative {
    opacity: 1;
    transform: scale(1) translateY(0) rotateX(0deg);
}

/* Custom scrollbar for content area */
.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #e5e7eb, #d1d5db);
    border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #d1d5db, #9ca3af);
}

/* Animation delay utility */
.animation-delay-150 {
    animation-delay: 150ms;
}

/* Enhanced focus states */
button:focus-visible {
    outline: 2px solid #FF5EAB;
    outline-offset: 2px;
}

/* Smooth hover transitions for interactive elements */
button, .cursor-pointer {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern gradient text effect for headings */
h2, h3 {
    background: linear-gradient(135deg, #1f2937, #374151);
    -webkit-background-clip: text;
    background-clip: text;
}

/* Enhanced shadow for better depth perception */
.shadow-2xl {
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* Modern ring styling */
.ring-1 {
    box-shadow: 
        0 0 0 1px rgba(0, 0, 0, 0.05),
        0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Gradient background improvements */
.bg-gradient-to-r {
    background-size: 200% 200%;
    animation: gradient-shift 8s ease infinite;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Modern pulse animation for status indicators */
@keyframes modern-pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

.animate-pulse {
    animation: modern-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style> 