<template>
    <div class="bg-[#F3F3F4] rounded-2xl px-12 py-12 mt-12" :class="props.isFullscreen ? 'h-[calc(100%-70px)] w-full mt-0 rounded-none' : ''">
        <!-- Tab Header -->
        <div class="flex gap-12 mb-0" v-if="selectedChannels.length > 0">
            <!-- Dynamic Channel Tabs -->
            <div
                v-for="channel in selectedChannels"
                :key="channel"
                class="flex-1 flex items-center bg-white relative cursor-pointer"
                :class="activeTab === channel ? 'shadow-[inset_0px_-2px_0px_0px_#FF5EAB]' : 'shadow-[inset_0px_-1px_0px_0px_#DCDDDD]'"
                @click="setActiveTab(channel)"
            >
                <!-- Tab Content -->
                <div class="flex items-center gap-8 px-12 py-12 w-full justify-center">
                    <component :is="getChannelIcon(channel)" class="w-16 h-16" />
                    <span
                        class="font-body text-18 leading-[1.33] whitespace-nowrap"
                        :class="activeTab === channel ? 'font-bold text-[#FF5EAB]' : 'text-black'"
                    >
                        {{ getChannelDisplayName(channel) }}
                    </span>
                    <!-- Chat indicator -->
                    <div
                        v-if="getTabMessages(channel).length > 0"
                        class="w-6 h-6 bg-[#FF5EAB] rounded-full flex items-center justify-center ml-4"
                    >
                        <span class="text-white text-8 font-bold">{{ getTabMessages(channel).length }}</span>
                    </div>
                </div>

                <!-- Close Button -->
                <div class="absolute -top-[15px] -right-[8px] w-16 h-16 bg-white rounded-full flex items-center justify-center">
                    <button
                        @click.stop="removeChannel(channel)"
                        class="w-full h-full flex items-center justify-center hover:bg-gray-100 rounded-full"
                    >
                        <IconClose class="w-12 h-12" />
                    </button>
                </div>
            </div>


            <div class="flex items-center ml-auto">
                <button
                    v-if="!props.isFullscreen"
                    @click="$emit('toggle-fullscreen')"
                    class="flex items-center gap-4 px-8 py-8 text-12 font-medium text-gray-600 hover:text-[#FF5EAB] hover:bg-pink-50 rounded-lg transition-colors"
                    title="Enter Fullscreen"
                >
                    <svg class="w-14 h-14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                    </svg>
                    <span>Fullscreen</span>
                </button>
            </div>
        </div>

        <div
            class="bg-white w-full mt-[-1px] overflow-hidden flex flex-col relative pb-12"
            ref="contentContainer"
            :class="props.isFullscreen ? 'flex-1 h-[calc(100%-50px)]' : 'h-[520px]'"
        >
            <div v-if="isLoading" class="flex items-center justify-center h-full p-24">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-[#FF5EAB] mx-auto mb-16"></div>
                    <p class="font-body text-16 text-gray-600">Generating content for {{ getChannelDisplayName(activeTab) }}...</p>
                </div>
            </div>

            <!-- Chat Area -->
            <div v-else-if="activeTab" class="flex flex-col h-full relative">

                <div class="flex-1 overflow-y-auto relative">

                    <TabChatMessages
                        v-if="getTabMessages(activeTab).length > 0 || (isTabStreaming && activeTab === activeTabId)"
                        :messages="getTabMessages(activeTab)"
                        :isStreaming="isTabStreaming && activeTab === activeTabId"
                        :streamingContent="isTabStreaming && activeTab === activeTabId ? currentStreamingContent : ''"
                        :activeTabId="activeTab"
                        :editTabMessage="editTabMessage"
                        :updateTabMessage="updateTabMessage"
                        :expandedMessageId="expandedMessageId"
                        ref="chatMessagesRef"
                        @toggle-fullscreen="handleFullscreenToggle"
                        @message-expanded="handleMessageExpanded"
                        @message-collapsed="handleMessageCollapsed"
                    />

                    <div v-else class="flex items-center justify-center h-full p-24">
                <div class="text-center">
                    <div class="mb-16">
                        <component :is="getChannelIcon(activeTab)" class="w-48 h-48 mx-auto mb-16 opacity-50" />
                    </div>
                    <h3 class="font-header font-bold text-20 text-black mb-8">{{ getChannelDisplayName(activeTab) }} Content</h3>
                    <p class="font-body text-16 text-gray-600 mb-24 max-w-[300px]">Click the button below to generate tailored content for {{ getChannelDisplayName(activeTab) }}.</p>
                    <button
                        @click="generateContentForChannel(activeTab)"
                                :disabled="isLoading || isTabStreaming"
                                class="bg-[#FF5EAB] text-white px-24 py-12 rounded-xl font-body text-16 hover:bg-[#E54A96] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Generate {{ getChannelDisplayName(activeTab) }} Content
                    </button>
                </div>
                    </div>
                </div>


            </div>


            <div v-else class="flex items-center justify-center h-full p-24">
                <div class="text-center">
                    <p class="font-body text-16 text-gray-600">Select a channel tab to view or generate content.</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, computed, nextTick, onUnmounted } from 'vue'
import { useTabChat } from '@/composables/useTabChat'
import TabChatMessages from './TabChatMessages.vue'

import IconInstagram from '@/Components/Icons/IconInstagram.vue'
import IconFacebook from '@/Components/Icons/IconFacebook.vue'
import IconTikTok from '@/Components/Icons/IconTikTok.vue'
import IconPinterest from '@/Components/Icons/IconPinterest.vue'
import IconTwitter from '@/Components/Icons/IconTwitter.vue'
import IconYouTube from '@/Components/Icons/IconYouTube.vue'
import IconBrandLinkedin from '@/Components/Icons/IconBrandLinkedin.vue'
import IconClose from '@/Components/Icons/IconClose.vue'

import InstagramIcon from '@/Components/SocialButtons/InstagramIcon.vue'
import FacebookIcon from '@/Components/SocialButtons/FacebookIcon.vue'
import TikTokIcon from '@/Components/SocialButtons/TikTokIcon.vue'
import YouTubeIcon from '@/Components/SocialButtons/YouTubeIcon.vue'
import LinkedInIcon from '@/Components/SocialButtons/LinkedInIcon.vue'
import SnapchatIcon from '@/Components/SocialButtons/SnapchatIcon.vue'
import PinterestIcon from '@/Components/SocialButtons/PinterestIcon.vue'
import TwitterIcon from '@/Components/SocialButtons/TwitterIcon.vue'

const props = defineProps({
    selectedChannels: {
        type: Array,
        default: () => []
    },
    availableChannels: {
        type: Array,
        default: () => []
    },
    campaignData: {
        type: Object,
        default: () => ({})
    },
    audienceData: {
        type: Object,
        default: () => ({})
    },
    platformsData: {
        type: Object,
        default: () => ({})
    },
    expandedMessageId: {
        type: String,
        default: null
    },
    isFullscreen: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['remove-channel', 'tab-changed', 'content-generated', 'toggle-fullscreen', 'message-expanded', 'message-collapsed', 'tab-chat-message'])

const {
    activeTabId,
    isStreaming: isTabStreaming,
    currentStreamingContent,
    getTabMessages,
    getTabGeneratedContent,
    generateTabContent,
    sendTabMessage,
    clearTabChat,
    clearAllTabChats,
    editTabMessage,
    updateTabMessage
} = useTabChat()

const activeTab = ref('')
const isLoading = ref(false)
const contentContainer = ref(null)
const chatMessagesRef = ref(null)

onUnmounted(() => {
    if (clearAllTabChats) {
        clearAllTabChats()
    }
})

const scrollToBottom = () => {
    if (chatMessagesRef.value && chatMessagesRef.value.scrollToBottom) {
        chatMessagesRef.value.scrollToBottom()
    } else if (contentContainer.value) {
        nextTick(() => {
            const scrollElement = contentContainer.value.querySelector('.overflow-y-auto')
            if (scrollElement) {
                scrollElement.scrollTo({
                    top: scrollElement.scrollHeight,
                    behavior: 'smooth'
                })
            }
        })
    }
}

const generateContentForChannel = async (channel) => {
    if (isLoading.value || isTabStreaming.value) return

    isLoading.value = true

    clearTabChat(channel)

    try {
        // Get platform post count from platformsData
        const postCount = props.platformsData[channel] || 1

        // Build enhanced context with post count information
        let context = ""
        if (props.campaignData && Object.keys(props.campaignData).length > 0) {
            const { campaignType, targetAmount, campaignDescription } = props.campaignData
            context += `Campaign type: ${campaignType || 'brand awareness campaign'}. `
            context += `Target amount: ${targetAmount || 'not specified'}. `
            context += `Campaign description: ${campaignDescription || 'not specified'}. `
        }

        const platformName = getChannelDisplayName(channel)
        context += `Create content specifically optimized for ${platformName}. `
        context += `Generate ${postCount} post${postCount > 1 ? 's' : ''} for ${platformName}. `

        if (postCount > 1) {
            context += `Separate multiple posts with <<----->>`
        }

        const result = await generateTabContent(
            channel,
            channel,
            {
                context: context,
                post_count: postCount,
                platform: channel,
                ...props.campaignData
            },
            props.audienceData
        )

        emit('content-generated', {
            channel,
            content: result.content,
            htmlContent: getTabGeneratedContent(channel).htmlContent,
            postCount: postCount
        })

    } catch (error) {
        // Handle error silently
    } finally {
        isLoading.value = false
    }
}

const handleChatMessage = async (message) => {
    if (!activeTab.value || isTabStreaming.value) return

    try {
        const additionalPayload = {
            audience_gender: props.audienceData.gender || 'not specified',
            audience_age: props.audienceData.ageRange?.[0] || 'not specified',
            audience_income: 'not specified',
            feature: 'awareness',
            selected_social_platform: activeTab.value.charAt(0).toUpperCase() + activeTab.value.slice(1),
        }

        await sendTabMessage(activeTab.value, message, additionalPayload)

        setTimeout(() => {
            scrollToBottom()
        }, 50)

    } catch (error) {
        // Handle error silently
    }
}

let scrollTimeout = null
watch(currentStreamingContent, () => {
    if (isTabStreaming.value && activeTabId.value === activeTab.value) {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout)
        }

        scrollTimeout = setTimeout(() => {
            scrollToBottom()
        }, 50)
    }
})

watch(isTabStreaming, (newState) => {
    if (newState && activeTabId.value === activeTab.value) {
        isLoading.value = false
        scrollToBottom()
    } else if (!newState) {
        setTimeout(() => {
            scrollToBottom()
        }, 200)
    }
})

watch(activeTab, () => {
    if (activeTab.value && getTabMessages(activeTab.value).length > 0) {
        setTimeout(() => {
            scrollToBottom()
        }, 100)
    }
})

watch(() => props.selectedChannels, (newChannels, oldChannels) => {
    if (newChannels.length > 0) {
        activeTab.value = newChannels[0]
        emit('tab-changed', activeTab.value)

        if (getTabMessages(activeTab.value).length === 0 && !isTabStreaming.value) {
        generateContentForChannel(activeTab.value)
        }
    }
}, { immediate: true })

const removeChannel = (channel) => {
    clearTabChat(channel)
    emit('remove-channel', channel)
}

const setActiveTab = (channel) => {
    activeTab.value = channel
    emit('tab-changed', channel)

    if (getTabMessages(channel).length === 0 && !isTabStreaming.value) {
        generateContentForChannel(channel)
    }
}

const getChannelIcon = (channel) => {
    const iconMap = {
        'instagram': IconInstagram,
        'facebook': IconFacebook,
        'tiktok': IconTikTok,
        'pinterest': IconPinterest,
        'twitter': IconTwitter,
        'x': IconTwitter,
        'youtube': IconYouTube,
        'linkedin': IconBrandLinkedin,
    }

    const socialIconMap = {
        'youtube': YouTubeIcon,
        'linkedin': LinkedInIcon,
        'snapchat': SnapchatIcon,
        'pinterest': PinterestIcon,
        'twitter': TwitterIcon,
        'x': TwitterIcon
    }

    const channelLower = channel.toLowerCase()
    return iconMap[channelLower] || socialIconMap[channelLower] || IconInstagram
}

const getChannelDisplayName = (channel) => {
    const nameMap = {
        'instagram': 'Instagram',
        'facebook': 'Facebook',
        'tiktok': 'TikTok',
        'youtube': 'YouTube',
        'linkedin': 'LinkedIn',
        'snapchat': 'Snapchat',
        'pinterest': 'Pinterest',
        'twitter': 'X (Twitter)',
        'x': 'X (Twitter)'
    }

    return nameMap[channel.toLowerCase()] || channel.charAt(0).toUpperCase() + channel.slice(1)
}

const toggleFullscreen = () => {
    emit('toggle-fullscreen', !props.isFullscreen)
}

const handleFullscreenToggle = (fullscreenState) => {
    emit('toggle-fullscreen', fullscreenState)
}

const handleMessageExpanded = (messageData) => {
    emit('message-expanded', messageData)
}

const handleMessageCollapsed = (messageData) => {
    // Reset the chat messages component state
    if (chatMessagesRef.value && chatMessagesRef.value.resetExpandedState) {
        chatMessagesRef.value.resetExpandedState()
    }
    emit('message-collapsed', messageData)
}

defineExpose({
    activeTab: computed(() => activeTab.value),
    handleChatMessage,
    isTabStreaming: computed(() => isTabStreaming.value && activeTab.value === activeTabId.value)
})
</script>

<style scoped>
/* Content container smooth scrolling */
.overflow-y-auto {
    scroll-behavior: smooth;
}
</style>
