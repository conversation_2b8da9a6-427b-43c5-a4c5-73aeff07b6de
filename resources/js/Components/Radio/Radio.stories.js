import Radio from "./Radio.vue";

export default {
    component: Radio,
    tags: ["autodocs"],

    args: {},
};

export const Default = {
    args: {
        id: "test_radio",
        options: [
            { name: "Balanced", value: "balanced", id: 1 },
            {
                name: "Maximize average donation",
                value: "maximize_average_donation",
                id: 2,
            },
            {
                name: "Maximize conversion rate",
                value: "maximize_conversion_rate",
                id: 3,
            },
        ],
    },
};
