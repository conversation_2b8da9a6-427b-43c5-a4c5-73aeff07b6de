<template>
    <Modal
        :isVisible="showHubspotModal"
        @close="handleClose"
        title="Request a meeting"
        width="4xl"
    >
        <div class="mt-36 h-full">
            <!-- Start of Meetings Embed Script -->
            <div
                ref="hubspotContainer"
                class="meetings-iframe-container"
                data-src="https://meetings-eu1.hubspot.com/robyn-greaves/pravi-customer-call?embed=true"
            ></div>
            <!-- End of Meetings Embed Script -->
            <div class="text-center">
                <Button
                    class="h-40 w-full max-w-[160px]"
                    size="md"
                    @click="handleClose"
                >
                    Close
                </Button>
            </div>
        </div>
    </Modal>
</template>

<script setup>
import { ref, watch, onUnmounted } from "vue";
import Modal from "@/Components/Modal/Modal.vue";
import Button from "../Button/Button.vue";

// Props
const props = defineProps({
    showHubspotModal: Boolean,
});

const emit = defineEmits(["close"]);

// Reference to the container div
const hubspotContainer = ref(null);

// Handle closing the modal
const handleClose = () => {
    emit("close");
    removeHubspotScript(); // Clean up the script when the modal is closed
};

// Function to inject the HubSpot Meetings script
const loadHubspotScript = () => {
    if (!hubspotContainer.value) return;

    const script = document.createElement("script");
    script.type = "text/javascript";
    script.src =
        "https://static.hsappstatic.net/MeetingsEmbed/ex/MeetingsEmbedCode.js";
    script.async = true;

    hubspotContainer.value.appendChild(script);
};

// Function to clean up the script when modal is closed or destroyed
const removeHubspotScript = () => {
    if (hubspotContainer.value) {
        hubspotContainer.value.innerHTML = ""; // Clear the script and iframe
    }
};

// Watch for changes to showHubspotModal prop to load/unload the script
watch(
    () => props.showHubspotModal,
    (newVal) => {
        if (newVal) {
            loadHubspotScript(); // Inject script when modal is shown
        } else {
            removeHubspotScript(); // Remove script when modal is closed
        }
    },
);

// Clean up when the component is unmounted
onUnmounted(() => {
    removeHubspotScript();
});
</script>
