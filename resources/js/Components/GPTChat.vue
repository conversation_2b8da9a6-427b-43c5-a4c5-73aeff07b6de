<template>
    <!-- Ren<PERSON> chatbot messages -->
    <template v-for="(message, index) in gptMessages" :key="index">
        <ConversationUser
            v-if="message.role === 'user'"
            :text="message.content"
        />

        <ConversationPravi
            v-else-if="message.role === 'assistant'"
            fullWidth=""
        >
            <div class="group relative">
                <div
                    class="prose max-w-7xl prose-h1:text-base prose-h2:text-base prose-h3:text-base prose-h4:text-base prose-h5:text-base prose-h6:text-base"
                    :ref="(el) => (assistantRefs[index] = el)"
                >
                    <!-- Use AnimatedText only for the newest message that needs animation -->
                    <AnimatedText
                        v-if="
                            shouldAnimateMessage &&
                            index === gptMessages.length - 1
                        "
                        :text="getConvertedMessageText(message)"
                        :speed="1.5"
                        :html-mode="true"
                        @animation-complete="animationCompleted"
                    />
                    <!-- Static content for all other messages -->
                    <div v-else v-html="getConvertedMessageText(message)"></div>
                </div>
            </div>
            <!-- Copy button -->
            <div class="-mb-12 mt-24 flex w-full justify-between gap-16">
                <button class="p-4" @click="copyContent(index)" title="Copy">
                    <span class="sr-only">Copy</span>
                    <IconCopy
                        class="transition hover:scale-110 hover:stroke-icon-focus active:scale-125"
                    />
                </button>
                <ChatbotFeedback />
            </div>
        </ConversationPravi>
    </template>

    <LoaderBouncing
        v-if="findDonorsStore.isLoading"
        class="ml-28"
        :class="showChatInput ? 'bottom-64' : 'bottom-0'"
    />

    <div
        v-if="showChatInput"
        class="mt-auto border-t border-t-border-primary p-16 md:px-24"
    >
        <ChatInput
            v-model="userPrompt"
            @submitPrompt="submitPrompt"
            placeholder="What would you like to create?"
            :isLoading="findDonorsStore.isLoading"
            :disabled="submitIsDisabled"
            :socialUrl="socialUrl"
        />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useFindDonorsStore } from "@/stores/findDonors";
import ChatInput from "@/Components/ChatInput/ChatInput.vue";
import ConversationPravi from "@/Components/ConversationPravi/ConversationPravi.vue";
import ConversationUser from "@/Components/ConversationUser/ConversationUser.vue";
import IconCopy from "@/Components/Icons/IconCopy.vue";
import ChatbotFeedback from "@/Components/Feedback/ChatbotFeedback.vue";
import AnimatedText from "@/Components/AnimatedText/AnimatedText.vue";
import axios from "axios";
import Showdown from "showdown";
import LoaderBouncing from "./Loaders/LoaderBouncing.vue";

const findDonorsStore = useFindDonorsStore();
const { tab , socialUrl } = defineProps({ 
        tab: Object , 
        socialUrl: {
            type: String,
            required: false,
            default: null
        }
    });

const emit = defineEmits("newMessage");

// Initialize GPT messages from the store's chat session for this feature
const gptMessages = ref(findDonorsStore.getChatSession(tab.chatKey));
const converter = new Showdown.Converter();

// Simple flag to determine if the latest message should be animated
const shouldAnimateMessage = ref(false);

// Function to mark animation as completed
const animationCompleted = () => {
    shouldAnimateMessage.value = false;
};

// Function to get the converted text for a message
const getConvertedMessageText = (message) => {
    if (message.role !== "assistant") return "";
    return converter.makeHtml(message.content);
};

// Whenever gptMessages changes, update the store so that state is persisted
watch(
    gptMessages,
    (newMessages) => {
        findDonorsStore.setChatSession(tab.chatKey, newMessages);
    },
    { deep: true },
);

const showChatInput = ref(true);
const userPrompt = ref("");
const assistantRefs = ref([]);

const submitIsDisabled = computed(() => {
    return !userPrompt.value || findDonorsStore.isLoading;
});


const submitPrompt = async () => {
    if (!userPrompt.value) return;
    findDonorsStore.setIsLoading(true);

    // Push user message
    gptMessages.value.push({
        role: "user",
        content: userPrompt.value,
    });

    emit("newMessage");

    // Clear input
    userPrompt.value = "";

    try {
        const payload = {
            messages: gptMessages.value,
            audience_gender: findDonorsStore.selectedDonor.GENDER,
            audience_age: findDonorsStore.selectedDonor.AGE,
            audience_income: findDonorsStore.selectedDonor.SALARY,
        };
        const response = await axios.post("/api/openai/chat", payload);

        // Enable animation for the new message
        shouldAnimateMessage.value = true;

        // Add the new message
        gptMessages.value.push(response.data.message);
    } catch (error) {
        console.error("Error communicating with OpenAI:", error);
    } finally {
        findDonorsStore.setIsLoading(false);
        emit("newMessage");
    }
};

const submitFirstPrompt = async () => {
    findDonorsStore.setIsLoading(true);
    try {
        const payload = {
            messages: gptMessages.value,
            audience_gender: findDonorsStore.selectedDonor.GENDER,
            audience_age: findDonorsStore.selectedDonor.AGE,
            audience_income: findDonorsStore.selectedDonor.SALARY,
            feature: tab.feature,

            selected_social_platform:
                tab.feature === "awareness" ? tab.platform_channel : null,
            selected_communication_channel:
                tab.feature === "stewardship" ? tab.platform_channel : null,

            fundraising_campaigns: findDonorsStore.resultsAffinity.fundraising,
            preferred_communication_channel:
                findDonorsStore.resultsAffinity.communication,
            likely_donation_amount:
                findDonorsStore.selectedDonor.PREDICTED_AVERAGE_DONATION,
        };
        const response = await axios.post("/api/openai/chat", payload);

        // Enable animation for the new message
        shouldAnimateMessage.value = true;

        // Add the new message
        gptMessages.value.push(response.data.message);
    } catch (error) {
        console.error("Error communicating with OpenAI:", error);
    } finally {
        findDonorsStore.setIsLoading(false);
        emit("newMessage");
    }
};

const copyContent = (index) => {
    const textToCopy = assistantRefs.value[index]?.innerText || "";
    navigator.clipboard
        .writeText(textToCopy)
        .then(() => console.log("Content copied to clipboard!"))
        .catch((error) => console.error("Failed to copy content:", error));
};

onMounted(() => {
    // If no chat messages have been loaded yet, send an initial prompt.
    if (gptMessages.value.length === 0) {
        submitFirstPrompt();
    }
});
</script>
