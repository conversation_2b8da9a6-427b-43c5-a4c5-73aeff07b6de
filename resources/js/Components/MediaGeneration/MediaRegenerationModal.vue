<template>
    <Teleport to="body">
        <Transition name="modal" appear>
            <div 
                v-if="isOpen" 
                class="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6 lg:p-8"
                @keydown.esc="closeModal"
                tabindex="0"
            >
                <!-- Enhanced backdrop -->
                <div 
                    class="absolute inset-0 transition-all duration-300"
                    :class="isRegenerating ? 'bg-black/30 backdrop-blur-sm' : 'bg-black/60 backdrop-blur-md'"
                    @click.self="!isRegenerating && closeModal()"
                ></div>
                
                <div class="relative bg-white rounded-2xl max-w-5xl w-full max-h-[95vh] overflow-hidden shadow-2xl ring-1 ring-black/5"
                     :class="{ 'transform scale-95': isRegenerating }"
                >
                    <!-- Enhanced Header -->
                    <div class="relative px-8 py-6 bg-gradient-to-r from-slate-50 to-gray-50 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-32 h-32 p-3 bg-gradient-to-br from-[#FF5EAB] to-[#E54A96] rounded-2xl flex items-center justify-center shadow-lg">
                                    <IconImageRegeneration v-if="mediaType === 'image'" :width="96" :height="96" color="text-white" />
                                    <IconVideoRegeneration v-else :width="96" :height="96" color="text-white" />
                                </div>
                                <div>
                                    <h2 class="text-2xl font-bold text-gray-900">
                                        Regenerate {{ mediaType === 'image' ? 'Images' : 'Videos' }}
                                    </h2>
                                    <p class="text-sm text-gray-600 mt-1">
                                        {{ isRegenerating ? 'Your content is being generated...' : 'Enhance your media with AI assistance' }}
                                    </p>
                                </div>
                                <div v-if="isRegenerating" class="flex items-center gap-3 px-4 py-2 bg-blue-50 rounded-full">
                                    <div class="relative">
                                        <div class="w-4 h-4 border-2 border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
                                        <div class="absolute inset-0 w-4 h-4 border-2 border-transparent border-r-blue-300 rounded-full animate-spin animation-delay-150"></div>
                                    </div>
                                    <span class="text-sm font-medium text-blue-700">Regenerating...</span>
                                </div>
                            </div>
                            <button 
                                @click="closeModal"
                                class="p-2.5 text-gray-400 hover:text-gray-600 hover:bg-white/80 rounded-xl transition-all duration-200"
                            >
                                <svg class="w-20 h-20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="p-8 max-h-[70vh] overflow-y-auto">
                        <div class="flex gap-8 h-full">
                            <!-- Left Content - Current Media -->
                            <div class="flex-1 w-1/3 space-y-6">
                                <!-- Current Media Section -->
                                <div>
                                    <div class="flex items-center gap-2 mb-6">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        <h3 class="text-lg font-semibold text-gray-900">Current {{ mediaType === 'image' ? 'Images' : 'Videos' }}</h3>
                                    </div>
                                    
                                    <div v-if="mediaType === 'image'" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div v-for="(image, index) in currentMedia" :key="index" class="relative group flex justify-center">
                                            <div class="relative w-[150px] h-[150px] overflow-hidden rounded-xl shadow-lg bg-gray-50">
                                                <img 
                                                    :src="image.url || image" 
                                                    :alt="`Current image ${index + 1}`"
                                                    class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                                                    @error="handleImageError"
                                                    @load="handleImageLoad"
                                                />
                                                <div class="absolute inset-0 bg-transparent group-hover:bg-black/20 rounded-xl transition-all cursor-pointer"
                                                     @click="!isRegenerating && openImagePreview(index)">
                                                    <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                                        <div class="bg-white/20 backdrop-blur-sm rounded-full p-2">
                                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                                            </svg>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div v-else class="space-y-4">
                                        <div v-for="(video, index) in currentMedia" :key="index" class="relative">
                                            <div class="rounded-xl overflow-hidden shadow-lg bg-gray-50">
                                                <video 
                                                    :src="video.url || video" 
                                                    controls
                                                    class="w-full max-w-[150px] rounded-xl mx-auto"
                                                >
                                                    Your browser does not support the video tag.
                                                </video>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Divider -->
                                <div class="border-t border-gray-200 my-6"></div>

                                <!-- Enhanced Processing State -->
                                <div v-if="isRegenerating" class="space-y-4">
                                    <div class="flex items-center gap-2">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                        <h3 class="text-lg font-semibold text-gray-900">Generating</h3>
                                    </div>
                                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100 shadow-sm">
                                        <div class="flex items-center gap-4">
                                            <div class="relative">
                                                <div class="w-8 h-8 border-3 border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
                                                <div class="absolute inset-0 w-8 h-8 border-3 border-transparent border-r-blue-300 rounded-full animate-spin animation-delay-150"></div>
                                            </div>
                                            <div>
                                                <p class="text-lg font-medium text-blue-900">Generating new {{ mediaType === 'image' ? 'images' : 'videos' }}...</p>
                                                <p class="text-sm text-blue-700 mt-1">Our AI is creating enhanced media based on your prompt</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Panel - Controls -->
                            <div class="w-2/3 space-y-6 flex flex-col h-full">
                                <!-- Prompt Input -->
                                <div class="space-y-4">
                                    <label class="block text-lg font-semibold text-gray-900">
                                        Update Prompt
                                        <span v-if="isRegenerating" class="text-sm font-normal text-blue-600">(Processing...)</span>
                                    </label>
                                    <textarea
                                        v-model="newPrompt"
                                        rows="12"
                                        :disabled="isRegenerating"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF5EAB] focus:border-transparent resize-none text-base leading-relaxed transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
                                        placeholder="Enter a new prompt to regenerate the media..."
                                    ></textarea>
                                </div>

                                <!-- Options Grid -->
                                <div class="space-y-4">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div v-if="mediaType === 'image'">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Aspect Ratio
                                            </label>
                                            <select 
                                                v-model="options.aspect_ratio"
                                                :disabled="isRegenerating"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF5EAB] focus:border-transparent text-base transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
                                            >
                                                <option value="1:1">Square (1:1)</option>
                                                <option value="4:3">Landscape (4:3)</option>
                                                <option value="3:4">Portrait (3:4)</option>
                                                <option value="16:9">Widescreen (16:9)</option>
                                                <option value="9:16">Story (9:16)</option>
                                            </select>
                                        </div>

                                        <div v-if="mediaType === 'video'">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Duration (seconds)
                                            </label>
                                            <select 
                                                v-model="options.duration"
                                                :disabled="isRegenerating"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF5EAB] focus:border-transparent text-base transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
                                            >
                                                <option :value="3">3 seconds</option>
                                                <option :value="5">5 seconds</option>
                                                <option :value="10">10 seconds</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Model
                                            </label>
                                            <select 
                                                v-model="options.model"
                                                :disabled="isRegenerating"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF5EAB] focus:border-transparent text-base transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
                                            >
                                                <template v-if="mediaType === 'image'">
                                                    <option value="midjourney">Midjourney</option>
                                                    <option value="flux">Flux</option>
                                                </template>
                                                <template v-else>
                                                    <option value="kling">Kling</option>
                                                    <option value="luma">Luma</option>
                                                </template>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Spacer -->
                                <div class="flex-1"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Footer -->
                    <div class="px-8 py-6 bg-gradient-to-r from-gray-50 to-slate-50 border-t border-gray-100 flex items-center justify-between">
                        <div v-if="isRegenerating" class="flex items-center gap-3 text-blue-600">
                            <div class="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                            <span class="font-medium">Your new {{ mediaType === 'image' ? 'images' : 'videos' }} are being generated...</span>
                        </div>
                        <div v-else class="text-sm text-gray-600">
                            You can continue using the app while regeneration is in progress
                        </div>
                        
                        <div class="flex items-center gap-3">
                            <button 
                                @click="closeModal"
                                :disabled="isRegenerating"
                                class="px-12 py-6 border border-gray-300 text-gray-700  hover:bg-gray-50 transition-colors disabled:opacity-50 font-medium disabled:cursor-not-allowed"
                            >
                                {{ isRegenerating ? 'Continue in Background' : 'Cancel' }}
                            </button>
                            <button 
                                @click="handleRegenerate"
                                :disabled="!newPrompt.trim() || isRegenerating"
                                class="px-12 py-6 bg-gradient-to-r from-[#FF5EAB] to-[#E54A96] hover:from-[#E54A96] hover:to-[#D63384] text-white  shadow-lg shadow-pink-500/25 disabled:shadow-none transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3 font-medium"
                            >
                                <div v-if="isRegenerating" class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                {{ isRegenerating ? 'Regenerating...' : 'Regenerate Media' }}
                            </button>
                        </div>
                    </div>
                </div>

                <ImageModal
                    :is-open="imagePreviewModal.isOpen && !isRegenerating"
                    :images="imagePreviewModal.images"
                    :current-index="imagePreviewModal.currentIndex"
                    :current-image="imagePreviewModal.currentImage"
                    @close="closeImagePreview"
                    @navigate="navigateImagePreview"
                />
            </div>
        </Transition>
    </Teleport>
</template>

<script setup>
import { ref, watch, computed, onMounted, onUnmounted } from 'vue'
import ImageModal from '@/Components/Common/ImageModal.vue'
import IconImageRegeneration from '@/Components/Icons/IconImageRegeneration.vue'
import IconVideoRegeneration from '@/Components/Icons/IconVideoRegeneration.vue'

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    },
    mediaType: {
        type: String,
        required: true,
        validator: value => ['image', 'video'].includes(value)
    },
    currentMedia: {
        type: Array,
        default: () => []
    },
    originalPrompt: {
        type: String,
        default: ''
    },
    isRegenerating: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['close', 'regenerate'])

const newPrompt = ref('')
const options = ref({
    aspect_ratio: '1:1',
    duration: 5,
    model: 'midjourney'
})

const imagePreviewModal = ref({
    isOpen: false,
    images: [],
    currentIndex: 0,
    currentImage: null
})

const preparedImages = computed(() => {
    return props.currentMedia.map((item, index) => ({
        url: item.url || item,
        alt: `Preview image ${index + 1}`,
        width: '1024',
        height: '1024'
    }))
})

const openImagePreview = (index) => {
    imagePreviewModal.value.images = preparedImages.value
    imagePreviewModal.value.currentIndex = index
    imagePreviewModal.value.currentImage = preparedImages.value[index]
    imagePreviewModal.value.isOpen = true
}

const closeImagePreview = () => {
    imagePreviewModal.value.isOpen = false
    imagePreviewModal.value.images = []
    imagePreviewModal.value.currentIndex = 0
    imagePreviewModal.value.currentImage = null
}

const navigateImagePreview = (direction) => {
    const images = imagePreviewModal.value.images
    let newIndex = imagePreviewModal.value.currentIndex

    if (direction === 1 && newIndex < images.length - 1) {
        newIndex++
    } else if (direction === -1 && newIndex > 0) {
        newIndex--
    }

    imagePreviewModal.value.currentIndex = newIndex
    imagePreviewModal.value.currentImage = images[newIndex]
}

const handleRegenerate = () => {
    if (!newPrompt.value.trim()) return

    emit('regenerate', {
        prompt: newPrompt.value.trim(),
        options: { ...options.value }
    })

    // Auto-close modal to let process run in background
    emit('close')
}

const closeModal = () => {
    if (props.isRegenerating) {
        if (confirm('Regeneration is in progress. Are you sure you want to close? The process will continue in the background.')) {
            emit('close')
        }
        return
    }
    emit('close')
}

const handleImageError = (event) => {
    console.warn('Image failed to load:', event.target.src)
    const target = event.target
    target.style.backgroundColor = '#f3f4f6'
    target.style.display = 'flex'
    target.style.alignItems = 'center'
    target.style.justifyContent = 'center'
    target.innerHTML = '<span style="color: #9ca3af; font-size: 14px;">Image not available</span>'
}

const handleImageLoad = (event) => {
    console.log('Image loaded successfully:', event.target.src)
}

const handleKeydown = (event) => {
    if (!props.isOpen) return
    
    if (event.key === 'Escape') {
        closeModal()
    }
}

watch(() => props.isOpen, (isOpen) => {
    if (isOpen) {
        newPrompt.value = props.originalPrompt
        options.value.model = props.mediaType === 'image' ? 'midjourney' : 'kling'
        if (!props.isRegenerating) {
            document.body.style.overflow = 'hidden'
        }
    } else {
        document.body.style.overflow = ''
    }
})

watch(() => props.currentMedia, (newMedia) => {
    console.log('Current media updated:', newMedia)
}, { deep: true })

watch(() => props.isRegenerating, (isRegenerating) => {
    if (isRegenerating) {
        document.body.style.overflow = ''
    } else if (props.isOpen) {
        document.body.style.overflow = 'hidden'
    }
})

watch(() => props.mediaType, (type) => {
    if (type === 'image') {
        options.value.model = 'midjourney'
    } else {
        options.value.model = 'kling'
    }
})

onMounted(() => {
    document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
    document.body.style.overflow = ''
})
</script>

<style scoped>
/* Modern modal transitions */
.modal-enter-active, .modal-leave-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-enter-from, .modal-leave-to {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

.modal-enter-to, .modal-leave-from {
    opacity: 1;
    transform: scale(1) translateY(0);
}

/* Backdrop transitions */
.modal-enter-active .absolute,
.modal-leave-active .absolute {
    transition: all 0.4s ease-out;
}

.modal-enter-from .absolute,
.modal-leave-to .absolute {
    opacity: 0;
    backdrop-filter: blur(0px);
}

.modal-enter-to .absolute,
.modal-leave-from .absolute {
    opacity: 1;
    backdrop-filter: blur(12px);
}

/* Enhanced modal container transitions */
.modal-enter-active .relative,
.modal-leave-active .relative {
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-enter-from .relative,
.modal-leave-to .relative {
    opacity: 0;
    transform: scale(0.85) translateY(-40px) rotateX(10deg);
}

.modal-enter-to .relative,
.modal-leave-from .relative {
    opacity: 1;
    transform: scale(1) translateY(0) rotateX(0deg);
}

/* Custom scrollbar for content area */
.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #e5e7eb, #d1d5db);
    border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #d1d5db, #9ca3af);
}

/* Animation delay utility */
.animation-delay-150 {
    animation-delay: 150ms;
}

/* Enhanced focus states */
button:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline: 2px solid #FF5EAB;
    outline-offset: 2px;
}

/* Smooth hover transitions for interactive elements */
button, .cursor-pointer, select, textarea {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern gradient text effect for headings */
h2, h3 {
    background: linear-gradient(135deg, #1f2937, #374151);
    -webkit-background-clip: text;
    background-clip: text;
}

/* Enhanced shadow for better depth perception */
.shadow-2xl {
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* Modern ring styling */
.ring-1 {
    box-shadow: 
        0 0 0 1px rgba(0, 0, 0, 0.05),
        0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Gradient background improvements */
.bg-gradient-to-r {
    background-size: 200% 200%;
    animation: gradient-shift 8s ease infinite;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Modern pulse animation for status indicators */
@keyframes modern-pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

.animate-pulse {
    animation: modern-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced hover effects for media items */
.group:hover .group-hover\:scale-105 {
    transform: scale(1.05);
}

.hover\:scale-110:hover {
    transform: scale(1.1);
}

/* Enhanced loading spinner */
@keyframes enhanced-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.animate-spin {
    animation: enhanced-spin 1s linear infinite;
}

/* Modern input styling enhancements */
input:focus, select:focus, textarea:focus {
    box-shadow: 0 0 0 3px rgba(255, 94, 171, 0.1);
}

/* Smooth state transitions */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced disabled states */
.disabled\:opacity-50:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Modern card styling */
.bg-white {
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
}

/* Enhanced border radius for modern look */
.rounded-xl {
    border-radius: 0.75rem;
}

.rounded-2xl {
    border-radius: 1rem;
}

/* Better color transitions */
.hover\:bg-gray-50:hover {
    background-color: rgba(249, 250, 251, 0.8);
}

/* Enhanced shadow for buttons */
.shadow-lg {
    box-shadow: 
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Modern border styling */
.border-gray-300 {
    border-color: rgba(209, 213, 219, 0.8);
}

.border-gray-100 {
    border-color: rgba(243, 244, 246, 0.8);
}

/* Enhanced text colors */
.text-gray-900 {
    color: rgba(17, 24, 39, 0.95);
}

.text-gray-700 {
    color: rgba(55, 65, 81, 0.9);
}

.text-gray-600 {
    color: rgba(75, 85, 99, 0.85);
}
</style> 