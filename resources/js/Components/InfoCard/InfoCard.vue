<template>
    <div class="rounded-3xl bg-surface-page p-12 shadow md:p-24">
        <div class="mb-16">
            <slot name="icon"></slot>
        </div>

        <h3 class="fs-md mb-8 font-semibold text-text-headings">
            {{ title }}
        </h3>

        <p class="fs-sm mb-8 font-medium text-text-grey">
            {{ subtitle }}
        </p>

        <p
            v-if="lastValue !== null"
            class="fs-xs mb-8 font-medium text-text-grey"
        >
            Latest value: {{ lastValue }}
        </p>

        <p class="fs-xs text-text-grey-light">
            {{ text }}
        </p>
    </div>
</template>

<script setup>
defineProps({
    title: String,
    subtitle: String,
    text: String,
    lastValue: {
        type: [String, Number],
        default: null,
    },
});
</script>
