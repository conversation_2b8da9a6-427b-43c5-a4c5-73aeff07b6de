<template>
    <table class="fs-xs w-full text-left text-text-headings">
        <tbody>
            <tr
                v-for="(item, index) in tableRows"
                :key="index"
                class="border-t-4 border-white bg-surface-page"
                :class="[item.isHighlighted ? 'rounded' : '']"
            >
                <td
                    :class="[
                        'w-[246px] px-16 py-12 font-header font-bold',
                        item.isHighlighted
                            ? 'rounded bg-surface-action-3x-light'
                            : 'bg-surface-grey-2x-light',
                    ]"
                >
                    {{ item.label }}
                </td>
                <td
                    class="fs-md relative w-[246px] px-16 py-12 after:absolute after:bottom-1 after:left-0 after:h-1 after:w-full after:bg-border-primary-light"
                    :class="
                        item.isHighlighted
                            ? 'font-header font-bold'
                            : 'font-normal'
                    "
                >
                    <span class="flex items-center justify-between">
                        {{ tableData[item.key] }}

                        <IconEdit v-if="!item.isHighlighted" />
                    </span>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script setup>
const props = defineProps({
    tableData: { type: Object, required: true, default: () => ({}) },
});

// Define the table rows with labels, keys, and a flag for highlighted rows
const tableRows = [
    { label: "Social media channels this person uses", key: "social_media" },
    { label: "Communication channels this person uses", key: "communication" },
    { label: "Other causes they support", key: "other_causes" },
    // { label: "Brands they engage with", key: "brands" },
    { label: "Fundraising campaigns they respond to", key: "fundraising" },
];
</script>
