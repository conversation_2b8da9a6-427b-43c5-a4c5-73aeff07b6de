import TableAffinity from "./TableAffinity.vue";

export default {
    component: TableAffinity,
    tags: ["autodocs"],
    args: {},
};

export const Persona1 = {
    args: {
        tableData: {
            social_media: "Instagram, TikTok",
            communication: "Email, Social Media DMs",
            fundraising: "Peer-to-peer, Challenge Events",
        },
    },
    // Adding methods to handle the emitted events
    argTypes: {
        action: { action: "clicked" }, // This will allow Storybook to log actions
    },
};
