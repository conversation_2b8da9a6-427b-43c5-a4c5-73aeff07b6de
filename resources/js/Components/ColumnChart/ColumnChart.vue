<template>
    <div class="rounded-3xl bg-surface-page p-20 shadow md:px-24 md:py-40">
        <h2 class="fs-xl pl-16 font-bold">{{ chartData.title }}</h2>

        <VueApexCharts
            type="bar"
            :options="chartOptions"
            :series="series"
            height="320px"
        >
        </VueApexCharts>
    </div>
</template>

<script setup>
import { ref } from "vue";
import VueApexCharts from "vue3-apexcharts";

const { chartData } = defineProps({
    chartData: Object,
});

const series = ref([
    {
        data: chartData.data,
    },
]);

const chartOptions = ref({
    xaxis: {
        categories: chartData.categories,
        labels: {
            show: false,
        },
    },
    yaxis: {
        labels: {
            show: false,
        },
    },
    colors: ["#FFCAE3", "#CAE0ED"],

    plotOptions: {
        bar: {
            borderRadius: 16,
            columnWidth: "68px",
            distributed: true,

            dataLabels: {
                position: "top",
            },
        },
    },

    grid: {
        show: true,
        strokeDashArray: 6,
    },

    tooltip: {
        enabled: false,
    },
    dataLabels: {
        enabled: true,

        formatter: chartData.formatter,

        offsetY: -30,

        style: {
            fontSize: "14px",
            // fontFamily: "Helvetica, Arial, sans-serif",
            fontWeight: "400",
            colors: "#000",
            borderRadius: "40px",
        },

        background: {
            enabled: true,
            foreColor: "#fff",
            padding: 16,
            borderRadius: 10,
            borderWidth: 1,
            borderColor: "#000",
            opacity: 0.9,
            dropShadow: {
                enabled: false,
                top: 1,
                left: 1,
                blur: 1,
                color: "#000",
                opacity: 0.45,
            },
        },
    },

    legend: {
        show: true,
        position: "top",
        horizontalAlign: "left",
        itemMargin: {
            // horizontal: 60,
            vertical: 32,
        },
    },

    states: {
        hover: {
            filter: {
                type: "none",
            },
        },
    },

    chart: {
        toolbar: {
            show: false,
        },
    },
});
</script>
