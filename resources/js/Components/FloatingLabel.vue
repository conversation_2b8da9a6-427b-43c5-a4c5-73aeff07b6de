<template>
    <label
        :for="for"
        class="absolute -top-2 left-8 -translate-y-1/2 scale-[0.875] bg-surface-page px-4 font-bold text-text-highlight transition-all peer-placeholder-shown:top-1/2 peer-placeholder-shown:scale-[1] peer-placeholder-shown:font-normal peer-placeholder-shown:text-text-grey peer-focus:-top-2 peer-focus:scale-[0.875] peer-focus:font-bold peer-focus:text-text-highlight peer-disabled:bg-surface-disabled"
    >
        {{ text }}
    </label>
</template>

<script setup>
defineProps({
    text: String,
    for: String,
});
</script>
