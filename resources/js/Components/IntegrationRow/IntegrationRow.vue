<template>
    <div
        class="flex flex-col justify-between gap-16 border-b border-border-primary py-24 md:px-24 xl:flex-row xl:items-center"
    >
        <div class="flex gap-16">
            <h3 class="font-bold">{{ service.name }}</h3>

            <p>
                {{ service.description }}
            </p>
        </div>

        <div class="shrink-0">
            <div
                class="mb-12 flex flex-col items-center justify-end gap-16 sm:flex-row"
            >
                <Button
                    @click="
                        isConnected
                            ? service.handleDisconnect(
                                  service.name.toLowerCase(),
                              )
                            : service.handleConnect()
                    "
                    :color="isConnected ? 'error' : 'action'"
                >
                    {{ isConnected ? "Disconnect" : "Connect" }}
                </Button>

                <Tag :color="isConnected ? 'success' : 'warning'">{{
                    isConnected ? "Connected" : "Not Connected"
                }}</Tag>
            </div>

            <div
                v-if="service.id === 'meta' && isConnected"
                class="flex flex-col items-center justify-end gap-16 sm:flex-row"
            >
                <ButtonLink
                    v-if="company.is_facebook_integrated"
                    href="/settings/integrations/facebook-data"
                    >Facebook data</ButtonLink
                >
                <ButtonLink
                    v-if="company.is_instagram_integrated"
                    href="/settings/integrations/instagram-data"
                    >Instagram data</ButtonLink
                >
            </div>
        </div>
    </div>
</template>

<script setup>
import Button from "@/Components/Button/Button.vue";
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";
import Tag from "@/Components/Tag/Tag.vue";

const props = defineProps({
    service: Object,
    isConnected: Boolean,
    company: Object,
});
</script>
