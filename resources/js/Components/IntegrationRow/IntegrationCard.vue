<template>
    <div class="rounded-2xl border border-border-primary p-24 text-center">
        <div
            class="relative mx-auto mb-20 inline-block rounded-full bg-surface-action-2x-light p-16 after:absolute after:bottom-2 after:right-2 after:size-16 after:rounded-full"
            :class="
                isConnected
                    ? 'after:bg-surface-success-dark'
                    : 'after:bg-surface-grey'
            "
        >
            <component class="size-40" :is="getIconComponent(service.id)" />
        </div>

        <h3 class="mb-8 font-bold">{{ service.name }}</h3>

        <p class="mb-20">{{ service.description }}</p>

        <div class="flex flex-col items-center gap-20">
            <Button
                class="min-w-[150px]"
                @click="
                    isConnected
                        ? service.handleDisconnect(service.name.toLowerCase())
                        : service.handleConnect()
                "
                :color="isConnected ? 'error' : 'action'"
            >
                {{ isConnected ? "Disconnect" : "Connect" }}
            </Button>

            <Tag :color="isConnected ? 'success' : 'warning'">{{
                isConnected ? "Connected" : "Not Connected"
            }}</Tag>
        </div>
    </div>
</template>

<script setup>
import Button from "@/Components/Button/Button.vue";
import Tag from "@/Components/Tag/Tag.vue";
import IconBrandFacebook from "../Icons/IconBrandFacebook.vue";
import IconBrandInstagram from "../Icons/IconBrandInstagram.vue";
import IconBrandGoogle from "../Icons/IconBrandGoogle.vue";

const props = defineProps({
    service: Object,
    handleConnect: Function,
    handleDisconnect: Function,
    auth: Object,
    isConnected: Boolean,
});

const getIconComponent = (serviceName) => {
    const icons = {
        meta: IconBrandFacebook,
        google: IconBrandGoogle,
    };
    return icons[serviceName] || null;
};
</script>
