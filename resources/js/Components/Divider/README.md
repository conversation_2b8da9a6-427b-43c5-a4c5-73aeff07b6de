# Divider Component

The Divider component displays centered text with horizontal lines extending to both sides, commonly used to separate content sections or provide alternative options in forms.

## Basic Usage

```vue
<template>
  <Divider />
</template>

<script setup>
import Divider from '@/Components/Divider/Divider.vue'
</script>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `text` | String | `"or"` | The text to display in the center of the divider |
| `size` | String | `"md"` | Text size: `"md"`, `"lg"`, `"xl"` |

### Size Options
- `"md"` - Medium text size (16px on XL screens)
- `"lg"` - Large text size (18px on XL screens)  
- `"xl"` - Extra large text size (20px on XL screens)

## Examples

### Default Divider
```vue
<template>
  <Divider />
</template>
```

### Custom Text
```vue
<template>
  <Divider text="and" />
</template>
```

### Large Size
```vue
<template>
  <Divider text="or" size="lg" />
</template>
```

### In Form Context
```vue
<template>
  <div class="space-y-4">
    <div>
      <label>Email</label>
      <input type="email" placeholder="Enter your email">
    </div>
    
    <Divider text="or" />
    
    <Button>Sign in with Google</Button>
  </div>
</template>

<script setup>
import Divider from '@/Components/Divider/Divider.vue'
import Button from '@/Components/Button/Button.vue'
</script>
```

### Between Content Sections
```vue
<template>
  <div class="space-y-6">
    <section>
      <h3>First Section</h3>
      <p>Content for the first section...</p>
    </section>
    
    <Divider text="or" size="lg" />
    
    <section>
      <h3>Second Section</h3>
      <p>Content for the second section...</p>
    </section>
  </div>
</template>
```

## CSS Classes

The component uses Tailwind utility classes directly:

- `flex items-center w-full` - Base divider layout with flex and full width
- `flex-1 h-px bg-border-primary` - Horizontal line styling (1px height, border color, flexible width)
- `px-12 font-body text-text-body` - Text styling with 12px horizontal padding and typography
- `text-sm-XL` - Medium text size (16px on XL screens)
- `text-md-XL` - Large text size (18px on XL screens)
- `text-lg-XL` - Extra large text size (20px on XL screens)

## Design Specifications

- **Spacing**: 12px padding on both sides of the text (consistent with button icon spacing)
- **Lines**: 1px height with primary border color
- **Alignment**: Text and lines are vertically centered on the same horizontal axis
- **Responsive**: Text sizes scale appropriately across different screen sizes
- **Accessibility**: Uses semantic HTML structure

## Storybook

View all divider variations and test the component in Storybook:

```bash
npm run storybook
```

Navigate to "Divider" in the sidebar to see all examples including form context and section separation use cases.
