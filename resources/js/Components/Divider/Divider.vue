<template>
    <div class="flex items-center w-full">
        <div class="flex-1 h-1 bg-border-primary"></div>
        <span class="px-12 font-body text-text-body" :class="textSizeClass">{{ text }}</span>
        <div class="flex-1 h-1 bg-border-primary"></div>
    </div>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
    text: {
        type: String,
        default: "or",
    },
    size: {
        type: String,
        default: "md",
        validator: (value) => {
            return ["md", "lg", "xl"].includes(value);
        },
    },
});

const textSizeClass = computed(() => {
    return {
        "text-sm-XL": props.size === "md",
        "text-md-XL": props.size === "lg",
        "text-lg-XL": props.size === "xl",
    };
});
</script>
