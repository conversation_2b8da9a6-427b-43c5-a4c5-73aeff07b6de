<template>
    <Modal
        :isVisible="showWaitlistModal"
        @close="handleClose"
        title="Join Newsletter"
        width="2xl"
    >
        <div class="mt-36 space-y-16">
            <p>Enter your details to join our newsletter.</p>

            <form @submit.prevent="subscribe" class="">
                <TextInput
                    id="first_name"
                    v-model="form.first_name"
                    type="text"
                    name="first_name"
                    label="First Name"
                    :rules="[rules.required]"
                    :serverError="form.errors.first_name"
                    required
                />
                <TextInput
                    id="last_name"
                    v-model="form.last_name"
                    type="text"
                    name="last_name"
                    label="Last Name"
                    :rules="[rules.required]"
                    :serverError="form.errors.last_name"
                    required
                />
                <TextInput
                    id="email"
                    v-model="form.email"
                    type="email"
                    name="email"
                    label="Email address"
                    :rules="[rules.email]"
                    :serverError="form.errors.email"
                    required
                />
                <TextInput
                    id="organisation_name"
                    v-model="form.organisation_name"
                    type="text"
                    name="organisation_name"
                    label="Organisation Name"
                    :rules="[rules.required]"
                    :serverError="form.errors.organisation_name"
                    required
                />
                <TextInput
                    id="country"
                    v-model="form.country"
                    type="text"
                    name="country"
                    label="Country"
                    :rules="[rules.required]"
                    :serverError="form.errors.country"
                    required
                />
                <Button
                    type="submit"
                    color="action"
                    class="h-[48px] w-full max-w-[260px]"
                    size="md"
                    :disabled="form.processing"
                >
                    Subscribe
                </Button>
            </form>
            <div v-if="successMessage">{{ successMessage }}</div>
            <div v-if="errorMessage" class="text-text-error">
                {{ errorMessage }}
            </div>
            <p>
                By clicking subscribe you agree to the
                <a
                    href="/privacy-policy"
                    class="text-text-action-hover underline"
                    target="_blank"
                >
                    Pravi Privacy Policy</a
                >.
            </p>
        </div>
    </Modal>
</template>

<script setup>
import { ref } from "vue";
import { Link, useForm } from "@inertiajs/vue3";
import Modal from "@/Components/Modal/Modal.vue";
import TextInput from "@/Components/TextInput/TextInput.vue";
import rules from "@/utilities/validation-rules";
import Button from "@/Components/Button/Button.vue";

defineProps({
    showWaitlistModal: Boolean,
});

const emit = defineEmits(["close"]);

const handleClose = () => {
    emit("close");
};

const form = useForm({
    first_name: "",
    last_name: "",
    email: "",
    organisation_name: "",
    country: "",
});
const successMessage = ref("");
const errorMessage = ref("");

const subscribe = async () => {
    form.post(route("hubspot.subscribe"), {
        onSuccess: () => {
            form.reset();
            successMessage.value =
                "Thank you for signing up to our newsletter!";
        },
        onError: () => {
            errorMessage.value =
                "There was an error signing you to the newsletter.";
        },
    });
};
</script>
