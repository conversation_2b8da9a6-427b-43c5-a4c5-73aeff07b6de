<template>
    <div class="flex rounded-full bg-surface-disabled p-8">
        <div
            v-for="(step, i) in steps"
            :key="i"
            class="inline-flex min-w-[110px] justify-center rounded-full px-24 py-12 lg:min-w-[183px]"
            :class="{
                'border border-text-action bg-surface-action-2x-light font-bold text-text-action':
                    i === currentStep,
            }"
        >
            {{ step }}
        </div>
    </div>
</template>

<script setup>
defineProps({
    steps: Array,
    currentStep: Number,
});
</script>
