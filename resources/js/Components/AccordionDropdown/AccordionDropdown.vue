<template>
    <div
        class="rounded-[20px] border border-border-primary bg-surface-page shadow-md"
    >
        <div>
            <button
                type="button"
                class="flex w-full items-center justify-start gap-8 p-16 text-left md:p-24"
                @click="$emit('toggle')"
            >
                <slot name="header" />

                <IconCheck
                    v-if="showCheck"
                    class="ml-auto mr-8"
                    :class="
                        isComplete
                            ? 'stroke-icon-success-light'
                            : 'stroke-icon-disabled'
                    "
                />
                <IconChevron
                    class="shrink-0 transition-transform duration-250 ease-out"
                    :class="{ 'rotate-0': isOpen, 'rotate-180': !isOpen }"
                />
            </button>
        </div>
        <transition
            name="accordion"
            @enter="onEnter"
            @after-enter="onAfterEnter"
            @leave="onLeave"
            @after-leave="onAfterLeave"
        >
            <div v-show="isOpen" class="accordion-content px-16 pb-16 md:px-24">
                <slot name="body" />
            </div>
        </transition>
    </div>
</template>

<script setup>
import IconChevron from "../Icons/IconChevron.vue";
import IconCheck from "@/Components/Icons/IconCheck.vue";

const props = defineProps({
    showCheck: Boolean,
    isComplete: Boolean,
    isOpen: Boolean,
});

const emit = defineEmits(["toggle"]);

// Animation functions for smooth expand/collapse
const onEnter = (el) => {
    el.style.height = '0';
    el.style.overflow = 'hidden';
    el.style.transition = 'height 0.25s ease-out';

    // Force reflow
    el.offsetHeight;

    // Set to auto height to measure content
    el.style.height = 'auto';
    const height = el.scrollHeight + 'px';
    el.style.height = '0';

    // Force reflow again
    el.offsetHeight;

    // Animate to full height
    el.style.height = height;
};

const onAfterEnter = (el) => {
    el.style.height = 'auto';
    el.style.overflow = 'visible';
};

const onLeave = (el) => {
    el.style.height = el.scrollHeight + 'px';
    el.style.overflow = 'hidden';
    el.style.transition = 'height 0.25s ease-in';

    // Force reflow
    el.offsetHeight;

    // Animate to 0 height
    el.style.height = '0';
};

const onAfterLeave = (el) => {
    el.style.height = '';
    el.style.overflow = '';
    el.style.transition = '';
};
</script>

<style scoped>
.accordion-content {
    transform-origin: top;
}

/* Fallback CSS transitions for browsers that don't support the JS animations */
.accordion-enter-active,
.accordion-leave-active {
    transition: all 0.3s ease-out;
    overflow: hidden;
}

.accordion-enter-from,
.accordion-leave-to {
    height: 0;
    opacity: 0;
    transform: scaleY(0);
}

.accordion-enter-to,
.accordion-leave-from {
    height: auto;
    opacity: 1;
    transform: scaleY(1);
}
</style>
