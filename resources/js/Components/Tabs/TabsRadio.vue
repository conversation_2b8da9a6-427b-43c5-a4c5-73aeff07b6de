<template>
    <div
        class="fs-md border-b border-border-primary text-center"
        :class="fullWidth ? 'block' : 'inline-block'"
    >
        <ul
            class="flex flex-wrap justify-center gap-4 md:justify-start lg:gap-16"
        >
            <li
                v-for="(tab, index) in tabs"
                :key="index"
                class="bg-surface-page"
                :class="{
                    'cursor-not-allowed': tab.disabled,
                    'flex-1': fullWidth,
                }"
            >
                <label
                    :class="[
                        'inline-block min-w-[180px] cursor-pointer rounded-t-lg border-b-2 px-24 py-12 transition md:min-w-[212px]',
                        tab.disabled || allDisabled
                            ? 'cursor-not-allowed text-text-disabled'
                            : selectedTab === tab.value
                              ? 'border-border-action font-bold text-text-action-hover'
                              : 'border-transparent hover:border-border-action hover:text-text-action-hover',
                        { 'w-full': fullWidth },
                    ]"
                >
                    <input
                        type="radio"
                        :name="radioName"
                        :value="tab.value"
                        v-model="selectedTab"
                        :disabled="tab.disabled || allDisabled"
                        class="hidden"
                        @change="handleTabClick(tab)"
                        :class="{ 'w-full': fullWidth }"
                    />
                    <button
                        v-if="showCloseBtn && tab.allowClose"
                        @click.prevent="handleClose(tab)"
                        class="mr-4 p-3"
                        :disabled="allDisabled"
                    >
                        <IconX />
                        <span class="sr-only">Close</span>
                    </button>

                    <span
                        :class="{
                            'before:relative before:top-2 before:mr-8 before:inline-block before:size-16 before:rounded-full before:border-2 before:border-border-primary':
                                showDot,
                        }"
                        >{{ tab.name }}</span
                    >
                </label>
            </li>
        </ul>
    </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from "vue";
import IconX from "../Icons/IconX.vue";
import { P } from "storybook/internal/components";

const props = defineProps({
    tabs: {
        type: Array,
        required: true,
    },
    modelValue: {
        type: String,
        default: null,
    },
    showDot: {
        type: Boolean,
        default: true,
    },
    showCloseBtn: {
        type: Boolean,
        default: false,
    },
    allDisabled: {
        type: Boolean,
        default: false,
    },
    fullWidth: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(["tab-click", "update:modelValue"]);

// Radio name for unique grouping of radio inputs
const radioName = "tabs-radio-group";
const selectedTab = ref(props.modelValue);

// Emit event when tab is clicked or selected
function handleTabClick(tab) {
    if (!tab.disabled) {
        emit("tab-click", tab);
        emit("update:modelValue", tab.value);
    }
}

// Update selectedTab if modelValue changes externally
watch(
    () => props.modelValue,
    (newValue) => {
        selectedTab.value = newValue;
    },
);

const handleClose = (tab) => {
    if (!tab.disabled) {
        emit("tab-close", tab.value);
    }
};
</script>
