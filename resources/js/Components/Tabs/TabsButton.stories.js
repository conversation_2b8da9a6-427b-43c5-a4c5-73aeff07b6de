import TabsButton from "./TabsButton.vue";

export default {
    component: TabsButton,
    tags: ["autodocs"],
    argTypes: {},
    args: {},
};

export const Default = {
    args: {
        tabs: [
            {
                name: "Profile",
                id: 1,
                disabled: false,
            },
            {
                name: "Notifications",
                id: 2,
                disabled: false,
            },
            {
                name: "Subscription",
                id: 3,
                disabled: true,
            },
            {
                name: "Help",
                id: 4,
                disabled: false,
            },
        ],
    },
};
