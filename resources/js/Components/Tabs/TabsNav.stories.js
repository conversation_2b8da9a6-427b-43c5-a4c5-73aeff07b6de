import TabsNav from "./TabsNav.vue";

export default {
    component: TabsNav,
    tags: ["autodocs"],
    argTypes: {},
    args: {},
};

export const Default = {
    args: {
        tabs: [
            {
                name: "Profile",
                href: "/settings/profile",
                disabled: false,
            },
            {
                name: "Notifications",
                href: "/settings/notifications",
                disabled: false,
            },
            {
                name: "Subscription",
                href: "/settings/subscription",
                disabled: false,
            },
            {
                name: "Help",
                href: "/settings/help",
                disabled: false,
            },
        ],
    },
};
