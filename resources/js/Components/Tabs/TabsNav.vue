<template>
    <div class="fs-md inline-block border-b border-border-primary text-center">
        <ul class="flex flex-wrap">
            <li
                v-for="(tab, index) in tabs"
                :key="index"
                class="me-2"
                :class="{ 'cursor-not-allowed': tab.disabled }"
            >
                <Link
                    :href="tab.href"
                    :class="[
                        'inline-block rounded-t-lg border-b-2 px-24 py-12 transition',
                        tab.disabled
                            ? 'cursor-not-allowed text-text-disabled'
                            : $page.url === tab.href
                              ? 'border-border-action font-bold text-text-action-hover'
                              : 'border-transparent hover:border-border-action hover:text-text-action-hover',
                    ]"
                    :aria-current="tab.active ? 'page' : null"
                    @click.prevent="handleTabClick(tab)"
                >
                    {{ tab.name }}
                </Link>
            </li>
        </ul>
    </div>
</template>

<script setup>
import { Link } from "@inertiajs/vue3";
import { defineEmits } from "vue";

const props = defineProps({
    tabs: {
        type: Array,
        required: true,
    },
});

const emit = defineEmits(["tab-click"]);

function handleTabClick(tab) {
    if (!tab.disabled) {
        emit("tab-click", tab);
    }
}
</script>
