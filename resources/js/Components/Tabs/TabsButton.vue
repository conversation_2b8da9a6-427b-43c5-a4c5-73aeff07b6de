<template>
    <ul
        class="fs-md flex flex-col flex-wrap items-center gap-4 sm:flex-row sm:gap-0"
    >
        <li
            v-for="(tab, index) in tabs"
            :key="index"
            :class="{ 'cursor-not-allowed': tab.disabled }"
        >
            <button
                :class="[
                    'inline-block rounded-3xl border-b border-l border-t border-border-primary-light px-24 py-12 transition',
                    tab.disabled
                        ? 'cursor-not-allowed text-text-disabled'
                        : selectedTabId === tab.id
                          ? 'border-accent-primary border-b-2 bg-surface-action-2x-light' // Active tab styles
                          : 'bg-surface-page',
                    {
                        'sm:rounded-none sm:rounded-l-3xl': isFirstItem(
                            tabs,
                            tab,
                        ),
                    },
                    {
                        'sm:rounded-none sm:rounded-r-3xl sm:border-r':
                            isLastItem(tabs, tab),
                    },
                ]"
                :aria-current="selectedTabId === tab.id ? 'page' : null"
                @click.prevent="handleTabClick(tab)"
            >
                {{ tab.name }}
            </button>
        </li>
    </ul>
</template>

<script setup>
import { defineEmits, ref } from "vue";

const props = defineProps({
    tabs: {
        type: Array,
        required: true,
    },
});

// Tracks the ID of the selected/active tab
const selectedTabId = ref(props.tabs[0].id); // Set the initial active tab to the first tab

const emit = defineEmits(["tab-click"]);

// Function to check if an item is the first item in an array
function isFirstItem(array, item) {
    return array.length > 0 && array[0] === item;
}

// Function to check if an item is the last item in an array
function isLastItem(array, item) {
    return array.length > 0 && array[array.length - 1] === item;
}

// Handles tab click event
function handleTabClick(tab) {
    if (!tab.disabled) {
        selectedTabId.value = tab.id; // Set the clicked tab as the active tab
        emit("tab-click", tab); // Emit the tab-click event to the parent component
    }
}
</script>
