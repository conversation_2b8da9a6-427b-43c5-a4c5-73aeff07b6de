<template>
    <div class="mt-8 flex items-center justify-center">
        <!-- Back Button -->
        <button
            @click="changePage(posts.current_page - 1)"
            class="group size-48 border-b border-l border-t border-border-primary bg-white p-16"
            :disabled="!posts.prev_page_url"
        >
            <IconBackArrow class="group-disabled:opacity-40" />
            <span class="sr-only">Previous page</span>
        </button>

        <!-- Page Number Buttons -->
        <button
            v-for="page in displayedPages"
            :key="page"
            @click="changePage(page)"
            class="size-48 border-b border-l border-t border-border-primary px-16 py-12 transition"
            :class="{
                'font-bold text-text-highlight': posts.current_page === page,
            }"
        >
            {{ page }}
        </button>

        <!-- Next Button -->
        <button
            @click="changePage(posts.current_page + 1)"
            class="group size-48 border border-border-primary bg-white p-16"
            :disabled="!posts.next_page_url"
        >
            <IconNextArrow class="group-disabled:opacity-40" />
            <span class="sr-only">Next page</span>
        </button>
    </div>
</template>

<script setup>
import IconBackArrow from "@/Components/Icons/IconBackArrow.vue";
import IconNextArrow from "@/Components/Icons/IconNextArrow.vue";
import { computed } from "vue";

const { posts } = defineProps({
    posts: Object,
});

const emit = defineEmits(["changePage"]);

const changePage = (page) => {
    emit("changePage", page);
};

// Computed property to limit pagination numbers to 5
const displayedPages = computed(() => {
    const totalPages = posts.last_page;
    const currentPage = posts.current_page;
    const maxButtons = 5;

    if (totalPages <= maxButtons) {
        return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + maxButtons - 1);

    if (endPage - startPage < maxButtons - 1) {
        startPage = Math.max(1, endPage - maxButtons + 1);
    }

    return Array.from(
        { length: endPage - startPage + 1 },
        (_, i) => startPage + i,
    );
});
</script>
