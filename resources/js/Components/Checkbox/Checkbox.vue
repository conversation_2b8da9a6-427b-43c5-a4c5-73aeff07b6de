<template>
    <div
        :class="{
            'pb-12': errorMessage,
            'pb-32': !errorMessage,
        }"
    >
        <div class="flex items-center">
            <input
                v-model="proxyChecked"
                type="checkbox"
                :value="value"
                :id="id"
                class="h-16 w-16 rounded border-2 border-border-primary text-border-focus focus:ring-2 focus:ring-border-focus"
            />
            <label :for="id" class="pl-8">
                <slot></slot>
            </label>
        </div>

        <p v-if="errorMessage" class="fs-xs mt-4 text-text-error">
            {{ errorMessage }}
        </p>
    </div>
</template>

<script setup>
import { computed } from "vue";

const emit = defineEmits(["update:checked"]);

const props = defineProps({
    checked: {
        type: [Array, Boolean],
        default: false,
    },
    value: {
        type: String,
        default: null,
    },
    id: Text,
    required: Boolean,
    errorMessage: String,
});

const proxyChecked = computed({
    get() {
        return props.checked;
    },

    set(val) {
        emit("update:checked", val);
    },
});
</script>
