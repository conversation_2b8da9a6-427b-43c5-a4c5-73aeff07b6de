import Table from "./Table.vue";

export default {
    component: Table,
    tags: ["autodocs"],
    args: {},
};

export const Users = {
    args: {
        tableData: {
            headings: ["First name", "Last name", "Email", "Role", "Action"],
            rows: [
                {
                    firstName: "Lorem",
                    lastName: "Ipsum",
                    email: "<EMAIL>",
                    role: "Admin",
                    action: "Delete",
                },
                {
                    firstName: "Lorem",
                    lastName: "Ipsum",
                    email: "<EMAIL>",
                    role: "Admin",
                    action: "Delete",
                },
                {
                    firstName: "Lorem",
                    lastName: "Ipsum",
                    email: "<EMAIL>",
                    role: "Admin",
                    action: "Delete",
                },
            ],
        },
    },
    // Adding methods to handle the emitted events
    argTypes: {
        action: { action: "clicked" }, // This will allow Storybook to log actions
    },
};
