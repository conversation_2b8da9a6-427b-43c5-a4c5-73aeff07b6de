<template>
    <div
        class="relative overflow-x-auto rounded-lg border border-border-primary-light"
    >
        <table class="fs-sm w-full text-left text-text-headings">
            <thead class="fs-lg bg-surface-grey-2x-light text-text-headings">
                <tr>
                    <th
                        v-for="(heading, index) of tableData.headings"
                        :key="index"
                        scope="col"
                        class="px-16 py-12"
                    >
                        {{ heading }}
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr
                    v-for="(row, rowIndex) of tableData.rows"
                    :key="row.id"
                    class="border-t border-border-primary-light bg-surface-page hover:bg-surface-action-hover-extra-light"
                >
                    <td
                        v-for="(item, index) of Object.entries(row).filter(
                            ([key]) => key !== 'id',
                        )"
                        :key="index"
                        class="whitespace-nowrap px-16 py-12 font-normal"
                    >
                        <!-- Check if the column is 'Action' -->
                        <Button
                            v-if="item[0] === 'action'"
                            @click="handleAction(item[1], row, rowIndex)"
                            class="h-32 capitalize"
                        >
                            {{ item[1] }}
                        </Button>

                        <span v-else>
                            {{ item[1] }}
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup>
import Button from "@/Components/Button/Button.vue";

// Define the props and emits
const props = defineProps({
    tableData: Object,
});

// Emit events for actions
const emit = defineEmits(["action"]);

// Handle action clicks
const handleAction = (action, row, rowIndex) => {
    emit("action", { action, row, rowIndex });
};
</script>
