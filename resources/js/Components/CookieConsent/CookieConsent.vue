<template>
    <div
        v-if="isVisible"
        class="fixed inset-x-0 bottom-0 z-50 border-t border-border-primary bg-surface-page py-24 text-text-headings"
    >
        <div
            class="container flex flex-col items-center justify-between gap-20 md:flex-row lg:max-w-screen-lg"
        >
            <p class="text-center md:text-left">
                We use cookies to ensure you get the best experience on our
                website.
            </p>
            <div class="flex gap-16">
                <Button @click="acceptCookies" class="h-40" color="action">
                    Accept
                </Button>
                <Button @click="declineCookies" class="h-40"> Decline </Button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import Button from "@/Components/Button/Button.vue";

const isVisible = ref(false);

const gaTrackingId = computed(() => import.meta.env.VITE_GA_TRACKING_ID);

const showConsentBanner = () => {
    const consent = localStorage.getItem("cookie_consent");
    isVisible.value = consent === null;
};

const acceptCookies = () => {
    localStorage.setItem("cookie_consent", "accepted");
    loadGoogleAnalytics();
    isVisible.value = false;
};

const declineCookies = () => {
    localStorage.setItem("cookie_consent", "declined");
    isVisible.value = false;
};

const loadGoogleAnalytics = () => {
    // Check if the script is already loaded to prevent duplicate scripts
    if (!document.querySelector('script[src*="google-analytics.com"]')) {
        const script = document.createElement("script");
        script.async = true;
        script.src = `https://www.googletagmanager.com/gtag/js?id=${gaTrackingId.value}`;
        document.head.appendChild(script);

        script.onload = () => {
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                window.dataLayer.push(arguments);
            }
            gtag("js", new Date());
            gtag("config", gaTrackingId.value);
        };
    }
};

onMounted(() => {
    const consent = localStorage.getItem("cookie_consent");
    if (consent === "accepted") {
        loadGoogleAnalytics();
    }
    showConsentBanner();
});
</script>
