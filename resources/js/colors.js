// Figma Variables 2. Colors Base
export const colorsBase = {
    blue: {
        900: "rgba(4, 49, 71, 1)", // Blue/900
        800: "rgba(12, 82, 106, 1)", // Blue/800
        700: "rgba(25, 102, 130, 1)", // Blue/700
        600: "rgba(47, 122, 152, 1)", // Blue/600
        500: "rgba(73, 142, 173, 1)", // Blue/500
        400: "rgba(102, 163, 192, 1)", // Blue/400
        300: "rgba(134, 183, 209, 1)", // Blue/300
        200: "rgba(168, 204, 223, 1)", // Blue/200
        100: "rgba(202, 224, 237, 1)", // Blue/100
        50: "rgba(237, 245, 249, 1)", // Blue/50
        base: "rgba(153, 221, 255, 1)", // Blue/Base
    },
    brown: {
        900: "rgba(62, 56, 49, 1)", // Brown/900
        800: "rgba(82, 74, 64, 1)", // <PERSON>/800
        700: "rgba(102, 93, 81, 1)", // Brown/700
        600: "rgba(122, 113, 100, 1)", // Brown/600
        500: "rgba(143, 133, 120, 1)", // Brown/500
        400: "rgba(163, 154, 142, 1)", // <PERSON>/400
        300: "rgba(184, 175, 165, 1)", // <PERSON>/300
        200: "rgba(204, 197, 189, 1)", // <PERSON>/200 (also <PERSON>/Base)
        100: "rgba(224, 220, 215, 1)", // Converted RGB values
        50: "rgba(245, 243, 241, 1)",
        base: "rgba(204, 197, 189, 1)", // Brown/Base (Alias of Brown/200)
    },
    green: {
        900: "rgba(6, 67, 16, 1)",
        800: "rgba(5, 88, 22, 1)",
        700: "rgba(18, 109, 32, 1)",
        600: "rgba(40, 131, 48, 1)",
        500: "rgba(66, 152, 68, 1)",
        400: "rgba(95, 172, 94, 1)",
        300: "rgba(128, 192, 124, 1)",
        200: "rgba(162, 211, 157, 1)",
        100: "rgba(199, 229, 195, 1)",
        50: "rgba(236, 246, 234, 1)",
        base: "rgba(147, 235, 143, 1)",
    },
    grey: {
        900: "rgba(48, 48, 48, 1)",
        800: "rgba(57, 57, 58, 1)",
        700: "rgba(75, 75, 76, 1)",
        600: "rgba(94, 94, 95, 1)",
        500: "rgba(113, 114, 115, 1)",
        400: "rgba(134, 134, 135, 1)",
        300: "rgba(154, 155, 156, 1)",
        200: "rgba(176, 177, 177, 1)",
        100: "rgba(198, 198, 199, 1)",
        50: "rgba(220, 221, 221, 1)",
        25: "rgba(243,243,244,1)",
        base: "rgba(36, 36, 36, 1)",
    },
    red: {
        900: "rgba(131, 0, 0, 1)",
        800: "rgba(170, 0, 0, 1)",
        700: "rgba(205, 0, 0, 1)",
        600: "rgba(237, 0, 0, 1)",
        500: "rgba(255, 34, 14, 1)",
        400: "rgba(255, 81, 48, 1)",
        300: "rgba(255, 121, 85, 1)",
        200: "rgba(255, 160, 128, 1)",
        100: "rgba(255, 199, 175, 1)",
        50: "rgba(255, 236, 228, 1)",
        base: "rgba(255, 0, 0, 1)",
    },
    teal: {
        900: "rgba(0, 66, 80, 1)",
        800: "rgba(0, 87, 105, 1)",
        700: "rgba(0, 108, 129, 1)",
        600: "rgba(0, 129, 152, 1)",
        500: "rgba(0, 150, 173, 1)",
        400: "rgba(27, 170, 191, 1)",
        300: "rgba(93, 190, 208, 1)",
        200: "rgba(141, 209, 223, 1)",
        100: "rgba(188, 228, 236, 1)",
        50: "rgba(233, 246, 249, 1)",
        base: "rgba(233, 246, 249, 1)", // Aliased as Teal/50
    },
    yellow: {
        900: "rgba(78, 53, 0, 1)",
        800: "rgba(101, 70, 0, 1)",
        700: "rgba(125, 88, 0, 1)",
        600: "rgba(149, 107, 0, 1)",
        500: "rgba(172, 127, 15, 1)",
        400: "rgba(193, 148, 53, 1)",
        300: "rgba(213, 170, 91, 1)",
        200: "rgba(230, 193, 133, 1)",
        100: "rgba(243, 217, 179, 1)",
        50: "rgba(252, 242, 229, 1)",
        base: "rgba(255, 200, 92, 1)",
    },
    pink: {
        900: "rgba(120, 0, 67, 1)", // Pink/900
        800: "rgba(156, 0, 88, 1)", // Pink/800
        700: "rgba(189, 0, 109, 1)", // Pink/700
        600: "rgba(218, 12, 130, 1)", // Pink/600
        500: "rgba(241, 57, 151, 1)", // Pink/500
        400: "rgba(255, 94, 171, 1)", // Pink/400
        300: "rgba(255, 130, 190, 1)", // Pink/300
        200: "rgba(255, 166, 209, 1)", // Pink/200
        100: "rgba(255, 202, 227, 1)", // Pink/100
        50: "rgba(255, 237, 246, 1)", // Pink/50
        25: "rgba(255, 245, 250, 1)", // Pink/25
        base: "rgba(255, 130, 190, 1)", // Pink/Base
    },
};

// Figma Variables 3. Color Tokens
export const colorTokens = {
    transparent: "transparent",
    current: "currentColor",
    white: "rgba(255, 255, 255, 1)",
    black: colorsBase.grey["base"],

    neutral: {
        "extra-dark": colorsBase.grey["900"],
        dark: colorsBase.grey["700"],
        base: colorsBase.grey["300"],
        light: colorsBase.grey["200"],
        "extra-light": colorsBase.grey["50"],
        "2x-extra-light": colorsBase.grey["25"],
    },

    information: {
        "extra-dark": colorsBase.blue["600"],
        dark: colorsBase.blue["500"],
        base: colorsBase.blue["300"],
        light: colorsBase.blue["base"],
        "extra-light": colorsBase.blue["100"],
        "2x-extra-light": colorsBase.blue["50"],
    },

    success: {
        base: colorsBase.green["300"],
        dark: colorsBase.green["700"],
        "extra-dark": colorsBase.green["900"],
        light: colorsBase.green["200"],
        "extra-light": colorsBase.green["100"],
    },

    warning: {
        base: colorsBase.yellow["300"],
        dark: colorsBase.yellow["500"],
        "extra-dark": colorsBase.yellow["700"],
        light: colorsBase.yellow["200"],
        "extra-light": colorsBase.yellow["50"],
        "gold-stars": colorsBase.yellow["base"],
    },

    error: {
        base: colorsBase.red["base"],
        dark: colorsBase.red["700"],
        "extra-dark": colorsBase.red["900"],
        light: colorsBase.red["300"],
        "extra-light": colorsBase.red["100"],
        "2x-extra-light": colorsBase.red["50"],
    },

    base: {
        teal: colorsBase.teal["base"],
        pink: colorsBase.pink["base"],
        blue: colorsBase.blue["base"],
        brown: colorsBase.brown["base"],
        green: colorsBase.green["50"],
        grey: colorsBase.grey["base"],
        red: colorsBase.red["base"],
        yellow: colorsBase.yellow["base"],
    },

    brown: {
        darker: colorsBase.brown["500"],
        dark: colorsBase.brown["400"],
        base: colorsBase.brown["300"],
        light: colorsBase.brown["200"],
        lighter: colorsBase.brown["100"],
    },

    pink: {
        "extra-dark": colorsBase.pink["500"],
        dark: colorsBase.pink["400"],
        base: colorsBase.pink["300"],
        light: colorsBase.pink["200"],
        "extra-light": colorsBase.pink["100"],
        "2x-extra-light": colorsBase.pink["50"],
        "3x-extra-light": colorsBase.pink["25"],
    },
};

// Figma Variables 4. Theme - Light Theme
export const themeLight = {
    text: {
        headings: colorTokens.black,
        body: colorTokens.black,
        "body-white": colorTokens.white,
        "body-always-black": colorTokens.black,
        "body-always-white": colorTokens.white,
        disabled: colorTokens.neutral.base,
        grey: colorTokens.neutral.base,
        highlight: colorTokens.pink.base,
        information: colorTokens.information["extra-dark"],
        success: colorTokens.success["extra-dark"],
        "success-light": colorTokens.success.base,
        warning: colorTokens.warning.dark,
        "warning-light": colorTokens.warning.base,
        error: colorTokens.error.dark,
        "error-light": colorTokens.error.light,
        action: colorTokens.pink.base,
        "action-light": colorTokens.pink.light,
        "action-hover": colorTokens.pink.dark,
    },

    icon: {
        information: colorTokens.information["extra-dark"],
        "information-light": colorTokens.information["extra-light"],
        success: colorTokens.success["extra-dark"],
        "success-light": colorTokens.success.light,
        warning: colorTokens.warning.dark,
        "warning-light": colorTokens.warning.base,
        "warning-lighter": colorTokens.warning["extra-light"],
        error: colorTokens.error.dark,
        "error-light": colorTokens.error["extra-light"],
        grey: colorTokens.neutral.light,
        white: colorTokens.white,
        "white-always": colorTokens.white,
        black: colorTokens.black,
        "black-always": colorTokens.black,
        disabled: colorTokens.neutral.base,
        action: colorTokens.pink.base,
        "action-light": colorTokens.pink["extra-light"],
        "action-hover": colorTokens.pink.dark,
        focus: colorTokens.pink["extra-dark"],
    },

    surface: {
        page: colorTokens.white,
        "page-inv": colorTokens.black,
        "page-always-black": colorTokens.black,
        "true-black": "#000000",
        disabled: colorTokens.neutral["2x-extra-light"],
        "success-darkest": colorTokens.success["extra-dark"],
        "success-dark": colorTokens.success.dark,
        success: colorTokens.success["extra-light"],
        "error-darkest": colorTokens.error["extra-dark"],
        "error-dark": colorTokens.error.dark,
        error: colorTokens.error["extra-light"],
        "error-2x-light": colorTokens.error["2x-extra-light"],
        "warning-darkest": colorTokens.warning["extra-dark"],
        "warning-dark": colorTokens.warning.base,
        warning: colorTokens.warning["extra-light"],
        "information-darkest": colorTokens.information["extra-dark"],
        "information-dark": colorTokens.information.base,
        information: colorTokens.information["extra-light"],
        "information-light": colorTokens.information["2x-extra-light"],
        modal: "#000000A5",
        "alert-success": colorTokens.success["extra-light"],
        "alert-information": colorTokens.information["extra-light"],
        "alert-warning": colorTokens.warning["extra-light"],
        "alert-error": colorTokens.error["extra-light"],
        "neutral-lightest-grey": colorTokens.neutral["2x-extra-light"],
        "neutral-darkest-grey": colorTokens.neutral["extra-dark"],
        "action-light": colorTokens.pink["extra-light"],
        "action-2x-light": colorTokens.pink["2x-extra-light"],
        "action-3x-light": colorTokens.pink["3x-extra-light"],
        action: colorTokens.pink["extra-dark"],

        "action-hover": colorTokens.pink["dark"],
        "action-hover-extra-light": colorTokens.pink["2x-extra-light"],
        focus: colorTokens.pink.base,
        "grey-2x-light": colorTokens.neutral["2x-extra-light"],
        "grey-light": colorTokens.neutral["extra-light"],
        grey: colorTokens.neutral.base,
    },

    border: {
        primary: colorTokens.neutral["extra-light"],
        "primary-light": colorTokens.neutral["2x-extra-light"],
        information: colorTokens.information.light,
        "information-dark": colorTokens.information.dark,
        success: colorTokens.success.light,
        "success-dark": colorTokens.success["extra-dark"],
        warning: colorTokens.warning.light,
        "warning-dark": colorTokens.warning["extra-dark"],
        error: colorTokens.error.dark,
        "error-dark": colorTokens.error["extra-dark"],
        highlight: colorTokens.white,
        disabled: colorTokens.neutral.base,
        action: colorTokens.pink["dark"],
        "action-hover": colorTokens.pink["light"],
        focus: colorTokens.pink["base"],
        black: colorTokens.black,
        "dark-black": "#000000",
    },
};
