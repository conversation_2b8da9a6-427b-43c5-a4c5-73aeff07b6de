import { nextTick } from "vue";

// Delay function
const delay = (ms: number): Promise<void> =>
    new Promise((resolve) => setTimeout(resolve, ms));

// Scroll to bottom of container
export const scrollToElementEnd = async (
    container: HTMLElement | null,
    delayMs: number = 0,
): Promise<void> => {
    await nextTick();

    if (container) {
        container.scrollTop = container.scrollHeight;
    }

    await delay(delayMs);
};

// Transform array of strings into SelectInput-like objects
export const arrayToSelectInput = (
    array: string[],
): { id: string; name: string }[] => {
    return array.map((item) => ({
        id: item,
        name: item,
    }));
};

// Trim content to a number of words, stripping HTML
export const trimContent = (content: string, wordCount: number): string => {
    const text = content.replace(/<[^>]+>/g, " ");
    const words = text.split(/\s+/).filter(Boolean);
    if (words.length <= wordCount) return words.join(" ");
    return words.slice(0, wordCount).join(" ") + " ...";
};

// Get full image URL based on environment
export const getImageUrl = (imagePath: string | null | undefined): string => {
    if (!imagePath) return "/images/blog-default.jpg";
    if (import.meta.env.VITE_APP_ENV === "local") {
        return `/storage/${imagePath}`;
    }
    return `${import.meta.env.VITE_AWS_URL}/${imagePath}`;
};

// Donor persona type (lightweight)
interface DonorAvatarInput {
    AGE: string;
    GENDER: string;
}

// Get avatar path based on donor AGE and GENDER
export const getDonorAvatarPath = (donor: DonorAvatarInput | null): string => {
    if (!donor || !donor.AGE || !donor.GENDER) return "";

    const age = donor.AGE;
    const gender = donor.GENDER.toLowerCase();
    let imageName = "";

    if (gender === "female") {
        if (["18-24", "25-29"].includes(age)) {
            imageName = "female1.png";
        } else if (["30-34", "35-39", "40-44"].includes(age)) {
            imageName = "female2.png";
        } else if (["45-49", "50-54", "55-59", "60-64", "65+"].includes(age)) {
            imageName = "female3.png";
        }
    } else if (gender === "male") {
        if (["18-24", "25-29"].includes(age)) {
            imageName = "male1.png";
        } else if (["30-34", "35-39", "40-44"].includes(age)) {
            imageName = "male2.png";
        } else if (["45-49", "50-54", "55-59", "60-64", "65+"].includes(age)) {
            imageName = "male3.png";
        }
    }

    if (!imageName) imageName = "female2.png"; // Fallback

    return `/images/affinity-avatars/${imageName}`;
};
