// Validation rules for client side validation of form inputs

type ValidationRule = (value: string) => boolean | string;

interface ValidationRules {
    required: ValidationRule;
    email: ValidationRule;
    password: ValidationRule;
}

const validationRules: ValidationRules = {
    required: (value: string): boolean | string =>
        !!value || "This field is required",
    email: (value: string): boolean | string => {
        const pattern =
            /^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
        return pattern.test(value) || "Must be an email address";
    },
    password: (value: string): boolean | string => {
        const pattern =
            /(?=^.{8,}$)((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/;
        return pattern.test(value) || "Password must meet the rules below";
    },
};

export default validationRules;
