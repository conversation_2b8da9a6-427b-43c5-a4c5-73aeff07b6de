import axios, { AxiosError } from "axios";
import { useFindDonorsStore } from "../stores/findDonors"; // Import the Pinia store
import type { Don<PERSON>Form } from "../types/donors";
import { arrayToSelectInput } from "@/utilities/helpers";

// Success and Error callback types
type Callback = () => void;
type ErrorCallback = (errors: Record<string, string>) => void;

// Submit Find Donors request
export const submitFindDonors = async (
    form: DonorForm,
    onSuccess: Callback,
    onError: ErrorCallback,
) => {
    const findDonorsStore = useFindDonorsStore();

    try {
        const payload = {
            REGION_CATEGORY: form.REGION_CATEGORY,
            GENERAL_CATEGORY: form.GENERAL_CATEGORY,
            SUB_CATEGORY: form.SUB_CATEGORY,
            AREA_OF_FOCUS: form.AREA_OF_FOCUS,
            GENDER: form.GENDER,
            AGE: form.AGE,
            LOCATION: form.LOCATION,
            SALARY: form.SALARY,
            STRATEGY_TYPE: form.STRATEGY_TYPE,
            FREQUENCY_WEIGHT: null,
            DONATION_WEIGHT: null,
            AVERAGE_DONATION_WEIGHT: null,
            NUMBER_OF_DONORS_WEIGHT: null,
        };

        const response = await axios.post("/data/top-donor-details", payload);

        if (response.data.data?.length) {
            findDonorsStore.setDonorsResults(response.data.data);
        }

        const distribution = response.data.distribution;
        if (distribution?.GENDER?.length > 0) {
            findDonorsStore.setResultsGender(distribution.GENDER);
        }
        if (distribution?.AGE_BAND?.length > 0) {
            findDonorsStore.setResultsAge(distribution.AGE_BAND);
        }
        if (distribution?.SALARY_BAND?.length > 0) {
            findDonorsStore.setResultsSalary(distribution.SALARY_BAND);
        }

        onSuccess();
    } catch (error) {
        const err = error as AxiosError;

        if (
            err.response &&
            err.response.data &&
            (err.response.data as any).errors
        ) {
            const errorsData = (err.response.data as any).errors;
            const formattedErrors: Record<string, string> = Object.fromEntries(
                Object.entries(errorsData as Record<string, string[]>).map(
                    ([key, value]) => [key, value.join(" ")],
                ),
            );
            onError(formattedErrors);
        } else {
            console.error("An unexpected error occurred:", error);
            onError({
                general:
                    "An unexpected error occurred. Please try again later.",
            });
        }
    }
};

// Feedback submission
export const submitFeedback = async (
    isPositive: boolean,
    type: string,
    message?: string,
) => {
    const payload = {
        score: isPositive,
        type,
        message: message ?? null,
    };

    try {
        await axios.post("/api/feedbacks/store", payload);
    } catch (error) {
        console.error("An unexpected error occurred:", error);
    }
};

// Save campaign request
export const saveCampaign = async () => {
    const findDonorsStore = useFindDonorsStore();

    // Validate that we have a donor and a form.
    const donor = findDonorsStore.donorsResults?.[0];
    const form = findDonorsStore.form;
    if (!donor || !form) return;

    // Create a plain object with only the properties you need from the store.
    const plainStoreData = {
        isCustomPrediction: findDonorsStore.isCustomPrediction,
        donorsResults: JSON.parse(
            JSON.stringify(findDonorsStore.donorsResults),
        ),
        resultsGender: JSON.parse(
            JSON.stringify(findDonorsStore.resultsGender),
        ),
        resultsAge: JSON.parse(JSON.stringify(findDonorsStore.resultsAge)),
        resultsSalary: JSON.parse(
            JSON.stringify(findDonorsStore.resultsSalary),
        ),
        resultsAffinity: JSON.parse(
            JSON.stringify(findDonorsStore.resultsAffinity),
        ),
        selectedDonor: JSON.parse(
            JSON.stringify(findDonorsStore.selectedDonor),
        ),
        shownResultIndex: findDonorsStore.shownResultIndex,
        dashboardTabs: JSON.parse(
            JSON.stringify(findDonorsStore.dashboardTabs),
        ),
        selectedDashboardTab: findDonorsStore.selectedDashboardTab,
        chatSessions: JSON.parse(JSON.stringify(findDonorsStore.chatSessions)),
    };

    // Build the payload by nesting the store data under "data"
    const campaignData = {
        data: plainStoreData, // the "data" key is now present and holds an object/array
        gender: donor.GENDER,
        age: donor.AGE,
        category: form.GENERAL_CATEGORY,
        subcategory: form.SUB_CATEGORY,
        uuid: findDonorsStore.uuid,
    };

    try {
        const res = await axios.post("/save-campaign", campaignData);
        if (res.data.uuid) {
            findDonorsStore.setUuid(res.data.uuid);
        }
    } catch (err) {
        console.error("Error saving campaign:", err);
    }
};

// Fetch general categories only if they aren't already in the store
export const fetchGeneralCategories = async () => {
    const findDonorsStore = useFindDonorsStore();

    const { setGeneralCategoryOptions, setSubCategoryOptions, updateForm } =
        findDonorsStore;

    // Reset sub-categories and related fields
    setSubCategoryOptions([]);
    updateForm({ SUB_CATEGORY: null });

    try {
        const response = await axios.get("/data/charity-categories");
        setGeneralCategoryOptions(arrayToSelectInput(response.data));
    } catch (error) {
        console.error("Error fetching general categories:", error);
    }
};

// Always fetch sub-categories whenever a general category is selected
export const fetchSubCategories = async (general_category: string) => {
    const findDonorsStore = useFindDonorsStore();
    const { setSubCategoryOptions } = findDonorsStore;

    try {
        const response = await axios.post("/data/charity-sub-categories", {
            GENERAL_CATEGORY: general_category,
        });
        if (response.data.length) {
            setSubCategoryOptions(arrayToSelectInput(response.data));
        }
    } catch (error) {
        console.error("Error fetching sub-categories:", error);
    }
};
