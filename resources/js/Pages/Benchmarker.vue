<template>
    <DashboardLayout title="Benchmarker">
        <main
            class="w-full overflow-auto bg-surface-grey-2x-light p-16 pt-80 sm:p-16 md:px-32 md:py-24"
            ref="conversationWindow"
        >
            <NoSubCard v-if="!auth.user.has_stripe_subscription"></NoSubCard>

            <div v-else>
                <div
                    class="mb-24 flex items-center justify-between rounded-2xl bg-surface-page p-12 md:col-span-2 md:px-24 xl:col-span-3"
                >
                    <TabsButton :tabs="tabs" @tab-click="switchTabs" />

                    <!-- <SelectInput
                label="From - To"
                :options="periodOptions"
                v-model="selectedPeriod"
                class="-mb-32 w-[192px]"
            >
            </SelectInput> -->
                </div>

                <div
                    v-if="!company.is_meta_integrated"
                    class="mx-auto grid max-w-2xl place-items-center rounded-3xl bg-surface-page p-16 shadow md:p-24"
                >
                    <div class="text-center">
                        <h1 class="fs-md mb-8 font-bold">
                            Facebook not connected
                        </h1>

                        <p class="fs-md mb-16">
                            Connect to your organisation Facebook page to use
                            the Facebook benchmarker tool.
                        </p>

                        <ButtonLink
                            class="h-56"
                            color="action"
                            href="/settings/integrations"
                        >
                            Connect
                        </ButtonLink>
                    </div>
                </div>

                <div
                    v-else
                    class="grid min-h-full gap-24 md:grid-cols-2 xl:grid-cols-3 xl:gap-x-40"
                >
                    <ColumnChart :chartData="chartData.postsPerWeek" />
                    <ColumnChart :chartData="chartData.cpc" />
                    <ColumnChart :chartData="chartData.cpm" />
                    <ColumnChart :chartData="chartData.ctr" />

                    <div
                        class="grid place-items-center rounded-3xl bg-surface-page p-16 shadow md:p-24"
                    >
                        <div class="text-center">
                            <p class="fs-md mb-16 text-text-grey">
                                We can help you find new donors<br />
                                based on your data
                            </p>

                            <ButtonLink
                                class="h-56"
                                color="action"
                                href="/build/campaign"
                            >
                                Find new donors
                            </ButtonLink>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { ref, onMounted } from "vue";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import NoSubCard from "@/Components/NoSubCard/NoSubCard.vue";
import ColumnChart from "@/Components/ColumnChart/ColumnChart.vue";
import StatCard from "@/Components/StatCard/StatCard.vue";
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";
import TabsButton from "@/Components/Tabs/TabsButton.vue";
import SelectInput from "@/Components/SelectInput/SelectInput.vue";
import axios from "axios";

const { auth, industry_standards, company } = defineProps([
    "auth",
    "industry_standards",
    "company",
]);

// Initialize the chartData object with default values.
// The second values (index 1) in each chart's data array are kept as the industry standard.
const chartData = ref({
    // organicEngagementRate: {
    //     title: "Organic engagement rate",
    //     data: [0.14, industry_standards.organic_engagement_rate.value],
    //     categories: ["Your engagement rate", "Industry standard"],
    //     formatter: function (val) {
    //         return val + "%";
    //     },
    // },
    postsPerWeek: {
        title: "Number of posts per week",
        // The first value will be replaced with the length of the posts array from the API.
        data: [0, industry_standards.posts_per_week.value],
        categories: ["Your posts per week", "Industry standard"],
        formatter: function (val) {
            return val;
        },
    },
    cpc: {
        title: "Cost per click (CPC)",
        // The first value will be updated with the real CPC from the API.
        data: [0, industry_standards.cpc.value],
        categories: ["Your CPC", "Industry standard"],
        formatter: function (val) {
            return "£" + val;
        },
    },
    // roas: {
    //     title: "Return On Advertising Spend",
    //     data: [1.5, industry_standards.roas.value],
    //     categories: ["Your ROAS", "Industry standard"],
    //     formatter: function (val) {
    //         return val + "%";
    //     },
    // },
    cpm: {
        title: "Average cost per 1,000 impressions (CPM)",
        data: [0, industry_standards.cpm.value],
        categories: ["Your CPM", "Industry standard"],
        formatter: function (val) {
            return "£" + val;
        },
    },
    ctr: {
        title: "Paid click through rate (CTR)",
        // The first value will be updated with the real CTR from the API.
        data: [0, industry_standards.ctr.value],
        categories: ["Your CTR", "Industry standard"],
        formatter: function (val) {
            return val + "%";
        },
    },
});

// const periodOptions = ref([
//   { id: 0, name: "Last 90 days" },
//   { id: 1, name: "Last 60 days" },
//   { id: 2, name: "Last 30 days" },
// ]);
const selectedPeriod = ref(null);

// Fetch real data when the component mounts.
onMounted(async () => {
    try {
        const response = await axios.get("/insights/facebook");
        // console.log("Data received from /insights/facebook:", response.data);

        // Update postsPerWeek with the number of posts (i.e. the length of the posts_per_week array)
        if (
            response.data.page &&
            response.data.page.posts_per_week &&
            Array.isArray(response.data.page.posts_per_week)
        ) {
            const postsCount = response.data.page.posts_per_week.length;
            chartData.value.postsPerWeek.data[0] = postsCount;
        }

        // Update CPC with the real value (convert string to a number)
        if (response.data.ad && response.data.ad.cpc) {
            chartData.value.cpc.data[0] = parseFloat(
                response.data.ad.cpc,
            ).toFixed(2);
        }

        // Update CTR with the real value (convert string to a number)
        if (response.data.ad && response.data.ad.ctr) {
            chartData.value.ctr.data[0] = parseFloat(
                response.data.ad.ctr,
            ).toFixed(2);
        }

        // Update CPM with the real value (convert string to a number)
        if (response.data.ad && response.data.ad.cpm) {
            chartData.value.cpm.data[0] = parseFloat(
                response.data.ad.cpm,
            ).toFixed(2);
        }
    } catch (error) {
        console.error("Error during GET request to /insights/facebook:", error);
    }
});

//
// Tab Navigation switches handlers
// ==========================================================================

const tabs = ref([
    {
        name: "Facebook",
        id: 1,
        disabled: false,
    },
    {
        name: "Instagram",
        id: 2,
        disabled: true,
    },
]);

const activeControlsTab = ref(1);

const switchTabs = (tab) => {
    activeControlsTab.id = tab.id;
};
</script>
