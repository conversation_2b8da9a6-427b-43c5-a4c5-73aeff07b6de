<template>
    <DashboardLayout title="Saved Campaigns">
        <main
            class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24"
            ref="conversationWindow"
        >
            <NoSubCard v-if="!auth.user.has_stripe_subscription"></NoSubCard>

            <div
                v-else
                class="relative flex min-h-full flex-col rounded-2xl bg-surface-page p-24 md:p-40"
            >
                <div
                    class="flex items-center gap-8 border-b border-border-primary pb-24"
                >
                    <IconBookmark />
                    <h1 class="fs-2xl font-bold">Saved Campaigns</h1>
                </div>

                <div
                    v-if="saved_campaigns.length > 0"
                    class="my-56 grid gap-24 md:grid-cols-2 lg:grid-cols-3 lg:gap-40"
                >
                    <CampaignCard
                        v-for="(campaign, index) in saved_campaigns"
                        :key="index"
                        :campaign="campaign"
                        @click="selectCampaign(campaign)"
                    />
                </div>

                <div v-else class="grid flex-1 place-items-center">
                    <div class="space-y-16 text-center">
                        <p class="fs-lg">
                            You haven't saved any campaigns yet.
                        </p>

                        <ButtonLink
                            href="/build/campaign"
                            color="action"
                            class="h-40"
                            >Get started</ButtonLink
                        >
                    </div>
                </div>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { ref, computed } from "vue";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import NoSubCard from "@/Components/NoSubCard/NoSubCard.vue";
import { useFindDonorsStore } from "@/stores/findDonors";
import IconBookmark from "@/Components/Icons/IconBookmark.vue";
import CampaignCard from "@/Components/CampaignCard/CampaignCard.vue";
import IconCategory from "@/Components/Icons/FacebookData/IconCategory.vue";
import { router } from "@inertiajs/vue3";
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";

const findDonorsStore = useFindDonorsStore();

const props = defineProps(["auth", "saved_campaigns"]);

const selectCampaign = (campaign) => {
    router.visit(route("build.audience.result", campaign.uuid));
};
</script>
