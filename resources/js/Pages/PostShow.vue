<template>
    <LandingLayout @handleJoinWaitlist="handleJoinWaitlist">
        <Head :title="post.title">
            <meta name="description" :content="trimContent(post.content, 30)" />
        </Head>

        <Header>
            <div class="mx-auto max-w-[920px]">
                <h1 class="fs-5xl my-24 font-extrabold">{{ post.title }}</h1>

                <img :src="getImageUrl(post.image)" alt="" class="w-full" />
            </div>
        </Header>

        <div class="container">
            <div class="mx-auto mb-72 max-w-[920px] md:mt-48">
                <!-- Render the full WYSIWYG content -->
                <div class="prose max-w-none" v-html="post.content"></div>
            </div>

            <!-- Other Posts Section -->
            <div>
                <h2 class="fs-3xl my-32 font-bold">Other Posts</h2>

                <div
                    class="grid grid-cols-1 gap-40 border-t border-border-primary pt-40 md:grid-cols-2 lg:gap-40 xl:grid-cols-4"
                >
                    <PostCard
                        v-for="other in otherPosts"
                        :key="other.id"
                        :post="other"
                    />
                </div>
            </div>

            <!-- All Categories Section -->
            <div class="my-80">
                <h2 class="fs-3xl mb-4 text-center font-bold md:text-left">
                    All Categories
                </h2>
                <div
                    class="mt-40 flex flex-wrap gap-24 border-t border-border-primary pt-40 lg:gap-40"
                >
                    <Pill
                        v-for="category in categories"
                        :key="category.id"
                        class="cursor-pointer"
                        @click="selectCategory(category.id)"
                    >
                        {{ category.name }}
                    </Pill>
                </div>
            </div>
        </div>

        <WaitlistModal
            :showWaitlistModal="showWaitlistModal"
            @close="showWaitlistModal = false"
        />
    </LandingLayout>
</template>

<script setup>
import { ref } from "vue";
import { Head, router } from "@inertiajs/vue3";
import LandingLayout from "@/Layouts/LandingLayout.vue";
import Header from "@/Components/Header/Header.vue";
import Pill from "@/Components/Pill/Pill.vue";
import WaitlistModal from "@/Components/WaitlistModal/WaitlistModal.vue";
import PostCard from "@/Components/PostCard/PostCard.vue";
import { getImageUrl, trimContent } from "@/utilities/helpers";

const props = defineProps({
    post: Object,
    otherPosts: Array,
    categories: Array,
});

const showWaitlistModal = ref(false);

const handleJoinWaitlist = () => {
    showWaitlistModal.value = true;
};

const selectCategory = (categoryId) => {
    // Navigate to the blog page with the selected category (resetting page to 1)
    router.visit(route("inertia.index.blog"), {
        method: "get",
        data: { category: categoryId, page: 1 },
        preserveState: false,
        preserveScroll: false,
    });
};
</script>
