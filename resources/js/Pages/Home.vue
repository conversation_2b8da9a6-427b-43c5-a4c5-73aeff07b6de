<template>
    <LandingLayout @handleJoinWaitlist="handleJoinWaitlist">
        <Head title="Home">
            <meta
                name="description"
                content="Find new donors to give to your cause. <PERSON>ravi helps you find and convert new donors without needing an existing donor database."
            />
        </Head>

        <HomeBanner
            @handleJoinWaitlist="handleJoinWaitlist"
            class="mb-48 md:mb-80"
        />

        <HomeBlockSection class="my-48 md:mb-104 md:mt-48" />

        <HomeTableSection
            class="my-48 md:my-80"
            @handleJoinWaitlist="handleJoinWaitlist"
        />

        <HomeFlowchartSection
            class="mb-80 mt-48 md:my-80"
            @handleJoinWaitlist="handleJoinWaitlist"
        />

        <HomeInsightsSection
            class="my-48 md:my-80"
            @handleJoinWaitlist="handleJoinWaitlist"
        />

        <HomeFeaturesSection class="my-48 md:my-80" />

        <HomePlans
            :plans="plans"
            class="my-48 md:my-80"
            @showWaitlistModal="showWaitlistModal = true"
        />

        <HomeTeamSection class="mb-24 mt-48 md:my-80" />

        <HomePartnershipSection class="my-48 md:my-80" />

        <WaitlistModal
            :showWaitlistModal="showWaitlistModal"
            @close="showWaitlistModal = false"
        />
    </LandingLayout>
</template>

<script setup>
import { ref } from "vue";
import { Head } from "@inertiajs/vue3";
import LandingLayout from "@/Layouts/LandingLayout.vue";
import HomeBanner from "@/Components/HomePage/HomeBanner.vue";
import HomeBlockSection from "@/Components/HomePage/HomeBlockSection.vue";
import HomeTableSection from "@/Components/HomePage/HomeTableSection.vue";
import WaitlistModal from "@/Components/WaitlistModal/WaitlistModal.vue";
import HomeFlowchartSection from "@/Components/HomePage/HomeFlowchartSection.vue";
import HomeInsightsSection from "@/Components/HomePage/HomeInsightsSection.vue";
import HomeFeaturesSection from "@/Components/HomePage/HomeFeaturesSection.vue";
import HomePartnershipSection from "@/Components/HomePage/HomePartnershipSection.vue";
import HomeTeamSection from "@/Components/HomePage/HomeTeamSection.vue";
import HomePlans from "@/Components/HomePage/HomePlans.vue";

defineProps({
    canLogin: {
        type: Boolean,
    },
    canRegister: {
        type: Boolean,
    },

    plans: { type: Array },
});

const showWaitlistModal = ref(false);

const handleJoinWaitlist = () => {
    showWaitlistModal.value = true;
};
</script>
