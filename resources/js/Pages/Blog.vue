<template>
    <LandingLayout @handleJoinWaitlist="handleJoinWaitlist">
        <Head title="Blog">
            <meta
                name="description"
                content="Read the latest blog posts and non profit community resources on Pravi."
            />
        </Head>

        <Header title="Our blog Non profit community resources">
            <h1 class="fs-5xl -mt-64 font-extrabold md:mt-0">
                Our blog <br />Non profit community resources
            </h1>
            <form
                @submit.prevent="updateSearch"
                class="relative mx-auto my-8 flex max-w-[560px] items-center justify-center"
            >
                <TextInput
                    v-model="searchQuery"
                    placeholder="Search"
                    class="h-48 w-full pr-56"
                />

                <Button
                    type="submit"
                    color="black"
                    class="group absolute right-0 top-0 size-48 rounded-l-none px-12"
                >
                    <span class="sr-only">Search</span>
                    <IconSearch
                        class="size-24 stroke-white transition group-hover:stroke-black"
                    ></IconSearch>
                </Button>
            </form>
        </Header>

        <!-- Blog Post List -->
        <div class="container my-80">
            <div
                class="mb-40 flex items-center justify-between border-b border-border-primary pb-40"
            >
                <h2 class="text-3xl font-bold">All Posts</h2>

                <SelectInput
                    v-model.number="selectedCategory"
                    @update:modelValue="updateCategory"
                    :options="categoryOptions"
                    placeholder="Categories"
                    class="min-w-[150px] pb-0 md:min-w-[260px]"
                />
            </div>

            <div
                v-if="posts.data.length > 0"
                class="mx-auto grid max-w-screen-xl gap-40"
            >
                <div
                    v-for="post in posts.data"
                    :key="post.id"
                    class="flex flex-col items-center justify-start gap-16 md:flex-row xl:gap-32"
                >
                    <Link
                        :href="`/blog/${post.slug}`"
                        class="max-h-[325px] w-full max-w-[500px]"
                    >
                        <img
                            :src="getImageUrl(post.image)"
                            alt=""
                            class="max-h-[325px] w-full max-w-[500px] object-cover"
                        />
                    </Link>
                    <div class="w-full">
                        <Link :href="`/blog/${post.slug}`">
                            <h3 class="fs-2xl mb-8 font-bold">
                                {{ post.title }}
                            </h3>
                        </Link>

                        <div
                            v-html="trimContent(post.content, 30)"
                            class="fs-md mb-8"
                        ></div>

                        <div class="flex flex-wrap gap-8">
                            <Pill
                                v-for="category in post.categories"
                                :key="category.id"
                            >
                                {{ category.name }}
                            </Pill>
                        </div>
                    </div>
                </div>
            </div>

            <p class="text-center" v-else>No posts found.</p>
        </div>

        <Pagination :posts="posts" @changePage="changePage" />

        <!-- All Categories Section -->
        <div class="container my-80">
            <h2 class="mb-4 text-center text-2xl font-bold md:text-left">
                All Categories
            </h2>
            <div
                class="mt-40 flex flex-wrap gap-24 border-t border-border-primary pt-40 lg:gap-40"
            >
                <Pill
                    v-for="category in categories"
                    :key="category.id"
                    class="cursor-pointer"
                    @click="selectCategory(category.id)"
                >
                    {{ category.name }}
                </Pill>
            </div>
        </div>

        <WaitlistModal
            :showWaitlistModal="showWaitlistModal"
            @close="showWaitlistModal = false"
        />
    </LandingLayout>
</template>

<script setup>
import { ref, computed } from "vue";
import { Head, usePage, router, Link } from "@inertiajs/vue3";
import LandingLayout from "@/Layouts/LandingLayout.vue";
import Header from "@/Components/Header/Header.vue";
import SelectInput from "@/Components/SelectInput/SelectInput.vue";
import Pill from "@/Components/Pill/Pill.vue";
import WaitlistModal from "@/Components/WaitlistModal/WaitlistModal.vue";
import Pagination from "@/Components/Pagination/Pagination.vue";
import { trimContent, getImageUrl } from "@/utilities/helpers";
import Button from "@/Components/Button/Button.vue";
import TextInput from "@/Components/TextInput.vue";
import IconSearch from "@/Components/Icons/IconSearch.vue";

const props = defineProps({
    posts: Object,
    categories: Array,
    selectedCategory: String,
    search: String,
});

// Waitlist handlers
const showWaitlistModal = ref(false);

const handleJoinWaitlist = () => {
    showWaitlistModal.value = true;
};
// Search handlers
const searchQuery = ref(props.search || "");

const updateSearch = () => {
    // When searching, reset to page 1 and include the current category filter if set
    const data =
        selectedCategory.value === 0
            ? { page: 1, search: searchQuery.value }
            : {
                  category: selectedCategory.value,
                  page: 1,
                  search: searchQuery.value,
              };

    router.visit(route("inertia.index.blog"), {
        method: "get",
        data,
        preserveState: false,
        preserveScroll: true,
    });
};

// Category handlers
const selectedCategory = ref(props.selectedCategory || 0);

const categoryOptions = computed(() => [
    { name: "All Categories", id: 0 },
    ...props.categories.map((category) => ({
        name: category.name,
        id: category.id,
    })),
]);

// When pill is clicked, change category
const selectCategory = (id) => {
    selectedCategory.value = id;
    updateCategory(false);
};

// Change category and reset pagination to page 1
const updateCategory = (preserveScroll = true) => {
    const data =
        selectedCategory.value === 0
            ? { page: 1 }
            : { category: selectedCategory.value, page: 1 };

    router.visit(route("inertia.index.blog"), {
        method: "get",
        data,
        preserveState: false,
        preserveScroll: preserveScroll,
    });
};

// Pagination
const changePage = (page) => {
    const data =
        selectedCategory.value === 0
            ? { page }
            : { category: selectedCategory.value, page };

    router.visit(route("inertia.index.blog"), {
        method: "get",
        data,
        preserveState: false,
        preserveScroll: false,
    });
};
</script>
