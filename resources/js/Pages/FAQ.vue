<template>
    <LandingLayout @handleJoinWaitlist="handleJoinWaitlist">
        <Head title="FAQ">
            <meta
                name="description"
                content="Find new donors to give to your cause. Pravi helps you find and convert new donors without needing an existing donor database."
            />
        </Head>

        <Header>
            <h1 class="fs-5xl font-extrabold">FAQ</h1>
            <div class="flex items-center justify-center gap-24">
                <ButtonLink href="/#plans" class="min-h-48" color="action">
                    Try For Free
                </ButtonLink>
            </div>
        </Header>

        <FAQAccordion :faqs="faqs" class="my-80" />

        <div class="mx-auto my-80 max-w-5xl">
            <p>
                If you have more questions, feel free to reach out to us via our
                <Link
                    href="/contact-us"
                    class="font-bold text-text-action-hover underline"
                    >contact form.</Link
                >
            </p>
        </div>

        <WaitlistModal
            :showWaitlistModal="showWaitlistModal"
            @close="showWaitlistModal = false"
        />
    </LandingLayout>
</template>

<script setup>
import { ref } from "vue";
import { Head, <PERSON> } from "@inertiajs/vue3";
import LandingLayout from "@/Layouts/LandingLayout.vue";
import WaitlistModal from "@/Components/WaitlistModal/WaitlistModal.vue";
import FAQAccordion from "@/Components/FAQ/FAQAccordion.vue";
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";
import Button from "@/Components/Button/Button.vue";
import Header from "@/Components/Header/Header.vue";

defineEmits(["handleJoinWaitlist"]);

const faqs = [
    {
        title: "Who is Pravi for?",
        description:
            "Pravi is designed for charities and nonprofits of any size that want to find new individual donors to support their cause.",
    },
    {
        title: "What does Pravi do?",
        description:
            "Pravi analyzes billions of data points to create tailored donor personas for your organization. Pravi tells you the types of people who are most interested in your cause, how much to ask them for, and how likely they are to become regular givers.",
    },
    {
        title: "When will Pravi be available?",
        description:
            "Pravi is available now for UK-based charities - click 'Try For Free' to get started! If your charity is outside the UK, join our waitlist, and we'll let you know as soon as Pravi is available in your country.",
    },
    {
        title: "Can I cancel my subscription?",
        description:
            "Yes, you can cancel your subscription at any time without penalty.",
    },
    {
        title: "Do I need to be tech-savvy to use Pravi?",
        description:
            "No, Pravi is designed to be user-friendly. Our friendly support team is available to assist you and ensure your success with the platform.",
    },
    {
        title: "Do I need to have an established regular giving program to use Pravi?",
        description:
            "No, Pravi is suitable for charities of any size. Whether you're starting from scratch or looking to expand your existing fundraising efforts, Pravi can help you find and convert new donors.",
    },
    {
        title: "Do I need access to additional software, such as a CRM or donor management system?",
        description:
            "No, the initial version of Pravi does not require any additional systems. Future versions will offer integration options with existing systems for personalized insights and recommendations.",
    },
    {
        title: "Will my data be secure?",
        description:
            "Protecting your data is our top priority. We never store or share personal donor information with anyone for any reason. We adhere to the highest security standards and conduct regular penetration testing to ensure our systems remain secure. For more details, please refer to our <a class='text-text-action underline' href='/privacy-policy'>Privacy Policy</a>.",
    },
    {
        title: "Is Pravi GDPR compliant?",
        description:
            "Yes, Pravi is based in the UK and complies with all relevant UK legislation, including GDPR. We never collect or share personal donor details. For more details, please refer to our <a class='text-text-action underline' href='/privacy-policy'>Privacy Policy</a>.",
    },
];

const showWaitlistModal = ref(false);

const handleJoinWaitlist = () => {
    showWaitlistModal.value = true;
};
</script>
