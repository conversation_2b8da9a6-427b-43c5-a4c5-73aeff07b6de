<template>
    <DashboardLayout title="Campaign details">
        <main
            class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24"
            ref="conversationWindow"
        >
            <NoSubCard v-if="!auth.user.has_stripe_subscription" />

            <div
                v-else
                class="relative flex min-h-full flex-col rounded-2xl bg-surface-page"
            >
                <TabsRadio
                    :tabs="tabs"
                    v-model="selectedTab"
                    @tab-click="handleTabClick"
                    :showDot="false"
                    showCloseBtn
                    class="m-16"
                    @tab-close="handleTabClose"
                    :allDisabled="findDonorsStore.isLoading"
                />

                <div
                    class="mt-auto flex flex-col justify-end space-y-24 p-16 md:space-y-40 md:p-24"
                >
                    <template
                        v-if="
                            selectedTab === 'current-chat' ||
                            selectedTab === 'campaign-chat'
                        "
                    >
                        <IconLogoGrey class="mx-auto" />

                        <!-- Render conversation steps. In this page, we start with the FindDonorsResults step -->
                        <template
                            v-for="(step, index) in conversationSteps"
                            :key="index"
                        >
                            <component
                                :is="step.component"
                                v-bind="step.props"
                                @handleNextStep="step.eventHandlers?.next"
                                @handlePreviousStep="
                                    step.eventHandlers?.previous
                                "
                                @handleNextText="step.eventHandlers?.nextText"
                            />
                        </template>

                        <LoaderBouncing
                            v-if="findDonorsStore.isLoading"
                            class="ml-28"
                            :class="showChatInput ? 'bottom-64' : 'bottom-0'"
                        />
                    </template>

                    <GPTChat
                        v-if="
                            selectedTab !== 'current-chat' &&
                            selectedTab !== 'campaign-chat'
                        "
                        :tab="getTab(selectedTab)"
                        :key="selectedTab"
                        @newMessage="newMessage"
                        :socialUrl="social_url"
                    />
                </div>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { usePage, router } from "@inertiajs/vue3";
import { format } from "date-fns";

// Layout and common components
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import TabsRadio from "@/Components/Tabs/TabsRadio.vue";
import Module from "@/Components/Module/Module.vue";
import NoSubCard from "@/Components/NoSubCard/NoSubCard.vue";
import LoaderBouncing from "@/Components/Loaders/LoaderBouncing.vue";
import GPTChat from "@/Components/GPTChat.vue";
import ConversationUser from "@/Components/ConversationUser/ConversationUser.vue";
import ConversionModule from "@/Components/ConversionModule/ConversionModule.vue";

// Icons
import IconLogoGrey from "@/Components/Icons/IconLogoGrey.vue";

// Find Donors components and store
import FindDonorsResults from "@/Components/FindDonorsResults/FindDonorsResults.vue";
import { useFindDonorsStore } from "@/stores/findDonors";
import { scrollToElementEnd } from "@/utilities/helpers";

const props = defineProps({
    campaign: Object,
    auth: Object,
    social_media_channels: Array,
    social_url: String,
    communication_channels: Array,
    fundraising_campaigns: Array,
});

const findDonorsStore = useFindDonorsStore();

// Tabs and selected tab (same as Dashboard.vue)

const selectedTab = computed({
    get: () =>
        findDonorsStore.selectedDashboardTab === "current-chat"
            ? "campaign-chat"
            : findDonorsStore.selectedDashboardTab,
    set: (val) => findDonorsStore.setDashboardActiveTab(val),
});

const campaignTitle = computed(() => {
    if (props.campaign && props.campaign.gender && props.campaign.age) {
        return `${props.campaign.gender} ${props.campaign.age}`;
    }
    return "Campaign"; // Fallback title
});

const tabs = computed(() => {
    const baseTabs = [...findDonorsStore.dashboardTabs];

    if (baseTabs.length && baseTabs[0].value === "current-chat") {
        baseTabs[0] = {
            name: campaignTitle.value,
            value: "campaign-chat",
            disabled: false,
            allowClose: false,
        };
    }

    return baseTabs;
});

function getTab(value) {
    const tab = findDonorsStore.dashboardTabs.find(
        (tab) => tab.value === value,
    );
    return tab ? tab : {};
}
const handleTabClick = async (tab) => {
    if (findDonorsStore.isLoading) return;
    findDonorsStore.setDashboardActiveTab(tab.value);
};
const handleTabClose = (tabValue) => {
    if (findDonorsStore.isLoading) return;
    findDonorsStore.removeDashboardTab(tabValue);
};

// Auto scroll handler
const conversationWindow = ref(null);
const newMessage = async () => {
    await scrollToElementEnd(conversationWindow.value, 500);
};

// Conversation steps: In this page, we want the first step to be the results step already.
const conversationSteps = ref([]);

// Define a function to set up the conversation steps as if nextDonorsStep had already occurred.
const initConversationSteps = async () => {
    conversationSteps.value = []; // clear any previous steps
    // Directly push the FindDonorsResults component with appropriate props
    conversationSteps.value.push({
        component: FindDonorsResults,
        props: {
            socialMediaChannels: props.social_media_channels || [],
            communicationChannels: props.communication_channels || [],
            fundraisingCampaigns: props.fundraising_campaigns || [],
            hasSaveButton: false,
        },
        eventHandlers: {
            next: finalDonorsStep,
            previous: previousDonorsStep,
        },
    });

    // If there are chat sessions, move to the final step
    if (Object.keys(findDonorsStore.chatSessions).length > 0) {
        await finalDonorsStep();
    }
};

const finalDonorsStep = async () => {
    conversationSteps.value.splice(4);
    conversationSteps.value.push({
        component: ConversationUser,
        props: { text: "Select these donors" },
    });
    conversationSteps.value.push({
        component: ConversionModule,
    });
    await scrollToElementEnd(conversationWindow.value, 500);
};

const previousDonorsStep = async () => {
    router.visit("/campaigns");
};

// On mounted, restore the store with campaign data and initialize the conversation steps.
onMounted(() => {
    // Assume the props.campaign.data_array holds the portion of the findDonorsStore to restore.
    if (props.campaign.data_array) {
        findDonorsStore.$patch(props.campaign.data_array);
    }
    initConversationSteps();
});

// If you need to format the saved date anywhere, here’s an example computed property:
const formattedDate = computed(() => {
    return props.campaign.updated_at
        ? format(new Date(props.campaign.updated_at), "dd/MM/yy")
        : "";
});
</script>
