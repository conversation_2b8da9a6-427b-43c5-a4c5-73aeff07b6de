<template>
    <LandingLayout>
        <Head :title="title" />
        <div
            class="container grid min-h-[calc(100vh-214px)] place-items-center"
        >
            <div class="my-48 space-y-32 text-center">
                <img
                    v-if="status === 404"
                    src="../../images/404.svg"
                    alt="404"
                />
                <h1 v-else class="fs-4xl font-extrabold">{{ title }}</h1>
                <div class="fs-2xl font-bold">{{ description }}</div>
                <ButtonLink
                    @click="handleBack"
                    color="action"
                    class="h-48 w-104"
                >
                    Go Back
                </ButtonLink>
            </div>
        </div>
    </LandingLayout>
</template>

<script setup>
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";
import LandingLayout from "@/Layouts/LandingLayout.vue";
import { Head } from "@inertiajs/vue3";
import { computed } from "vue";

const props = defineProps({ status: Number });

const title = computed(() => {
    return {
        503: "503: Service Unavailable",
        500: "500: Server Error",
        404: "404: Page Not Found",
        403: "403: Forbidden",
        422: "Permission Denied"
    }[props.status];
});

const description = computed(() => {
    return {
        503: "Sorry, we are doing some maintenance. Please check back soon.",
        500: "Whoops, something went wrong on our servers.",
        404: "Sorry, the page you are looking for could not be found.",
        403: "Sorry, you are forbidden from accessing this page.",
        422: "Whoops. Looks like you do not have permissions to access dashboard. Please consult with the administrator of the company."
    }[props.status];
});

const handleBack = () => {
    window.history.back();
};
</script>
