<template>
    <DashboardLayout title="Organisation Instagram page data">
        <main
            class="w-full overflow-auto bg-surface-grey-2x-light p-16 pt-80 sm:p-16 md:px-32 md:py-24"
        >
            <div class="my-24 flex items-center justify-between gap-20">
                <h1 class="fs-2xl font-bold">
                    Your organisation Instagram page data
                </h1>
                <ButtonLink color="action" href="/settings/integrations">
                    Back
                </ButtonLink>
            </div>

            <p class="mb-24 max-w-5xl">
                This dashboard shows your Instagram analytics in one intuitive
                view. Here, you can review key performance indicators – such as
                reach, impressions, engagement, and more – across various time
                periods. Use these insights to make informed decisions and
                better connect with your audience.
            </p>

            <div class="grid min-h-full gap-24 md:grid-cols-2 xl:grid-cols-3">
                <!-- ConfigData Cards -->
                <template v-if="configData">
                    <InfoCard
                        v-for="(card, index) in cards"
                        :key="'config-' + index"
                        :title="card.title"
                        :subtitle="card.subtitle"
                        :text="card.text"
                    >
                        <template #icon>
                            <component :is="card.component" />
                        </template>
                    </InfoCard>
                </template>

                <!-- PageData Cards -->
                <template v-if="pageData">
                    <InfoCard
                        v-for="(card, index) in pageCards"
                        :key="'page-' + index"
                        :title="card.title"
                        :subtitle="card.subtitle"
                        :lastValue="card.lastValue"
                        :text="card.text"
                    >
                        <template #icon>
                            <IconPieChart />
                        </template>
                    </InfoCard>
                </template>

                <!-- AdData Cards -->
                <template v-if="adData">
                    <InfoCard
                        v-for="(card, index) in adCards"
                        :key="'ad-' + index"
                        :title="card.title"
                        :subtitle="card.subtitle"
                        :lastValue="card.lastValue"
                        :text="card.text"
                    >
                        <template #icon>
                            <IconAd />
                        </template>
                    </InfoCard>
                </template>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { computed } from "vue";
import _ from "lodash";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import InfoCard from "@/Components/InfoCard/InfoCard.vue";
import IconInstagram from "@/Components/Icons/InstagramData/IconInstagram.vue";
import IconAd from "@/Components/Icons/FacebookData/IconAd.vue";
import IconCategory from "@/Components/Icons/FacebookData/IconCategory.vue";
import IconPieChart from "@/Components/Icons/FacebookData/IconPieChart.vue";
import IconId from "@/Components/Icons/FacebookData/IconId.vue";
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";

const props = defineProps({
    configData: {
        type: Object,
        required: false,
    },
    pageData: {
        type: Object,
        required: false,
    },
    adData: {
        type: Object,
        required: false,
    },
});

// Helper function to format title strings.
// For example, converts "page_fan_adds (days_28)" to "Page Fan Adds (days 28)".
function formatTitle(title) {
    if (!title) return "Unknown Metric";
    const match = title.match(/^(.+?)\s*\((.+)\)$/);
    if (match) {
        const mainPart = match[1];
        const parenPart = match[2];
        return `${_.startCase(mainPart)} (${parenPart.replace(/_/g, " ")})`;
    } else {
        return _.startCase(title.replace(/_/g, " "));
    }
}

// Build an array of card objects for the configData
const cards = computed(() => [
    {
        subtitle: "Your Instagram Page",
        title: props.configData.page.name,
        text: "A public profile on Instagram representing your business, brand, or organization.",
        component: IconInstagram,
    },
    {
        subtitle: "Your Instagram Business Account",
        title: props.configData.instagram_business_account
            ? props.configData.instagram_business_account.id
            : "N/A",
        text: "The Instagram Business Account associated with your organisation.",
        component: IconId,
    },
    {
        subtitle: "Your Business Name",
        title: props.configData.business.name,
        text: "The official name of your business as registered in your account.",
        component: IconId,
    },
    {
        subtitle: "Your Instagram ID",
        title: props.configData.instagram.id,
        text: "A unique identifier for your Instagram account.",
        component: IconId,
    },
    {
        subtitle: "Your Instagram Ad Account ID",
        title: props.configData.ad_account.account_id,
        text: "A unique number that identifies your advertising account on Instagram.",
        component: IconAd,
    },
    {
        subtitle: "Your Latest Instagram Ad Performance",
        title: props.configData.latestAdPerformance || "N/A",
        text: "A summary of key metrics from your most recent Instagram ad campaign.",
        component: IconAd,
    },
]);

// Build an array of card objects for the pageData insights
const pageCards = computed(() => {
    const result = [];
    // Loop over each metric array in pageData (e.g., "day", "day_grouped", etc.)
    Object.values(props.pageData).forEach((metricArray) => {
        metricArray.forEach((insight) => {
            // Get the last value if available.
            const lastValue =
                insight.values && insight.values.length
                    ? insight.values[insight.values.length - 1].value
                    : insight.total_value
                      ? insight.total_value.value
                      : null;

            // Safely determine the period text.
            const periodText = insight.period
                ? insight.period.replace(/_/g, " ")
                : "N/A";

            // Build a raw title string.
            const rawTitle =
                insight.title ||
                (insight.name
                    ? insight.period
                        ? `${insight.name} (${insight.period})`
                        : insight.name
                    : "Unknown Metric");

            // Format the title.
            const formattedTitle = formatTitle(rawTitle);

            result.push({
                title: formattedTitle,
                subtitle: `Period: ${periodText}`,
                lastValue: lastValue,
                text: insight.description || "",
            });
        });
    });
    return result;
});

// Build an array of card objects for the adData
const adCards = computed(() => {
    const result = [];
    if (props.adData) {
        // Card for Campaign details
        if (props.adData.campaign_name) {
            result.push({
                title: "Campaign Name",
                subtitle: `Campaign ID: ${props.adData.campaign_id}`,
                lastValue: null,
                text: props.adData.campaign_name,
            });
        }
        // Card for Ad details
        if (props.adData.ad_name) {
            result.push({
                title: "Ad Name",
                subtitle: `Ad ID: ${props.adData.ad_id}`,
                lastValue: null,
                text: props.adData.ad_name,
            });
        }
        // Card for Ad Set details
        if (props.adData.adset_name) {
            result.push({
                title: "Ad Set Name",
                subtitle: "Ad Set",
                lastValue: null,
                text: props.adData.adset_name,
            });
        }
        // Card for overall metrics
        result.push({
            title: "Ad Metrics",
            subtitle: "Performance Overview",
            lastValue: null,
            text: `Impressions: ${props.adData.impressions}, Clicks: ${props.adData.clicks}, Spend: $${props.adData.spend}, CTR: ${props.adData.ctr}%, CPC: $${props.adData.cpc}, CPM: $${props.adData.cpm}, Organic Engagement Rate: ${props.adData.oganicEngagementRate}%`,
        });
        // Cards for each action in the actions array
        if (props.adData.actions && Array.isArray(props.adData.actions)) {
            props.adData.actions.forEach((action) => {
                result.push({
                    title: _.startCase(action.action_type),
                    subtitle: "Action Count",
                    lastValue: action.value,
                    text: `Number of ${_.startCase(action.action_type)} actions.`,
                });
            });
        }
        // Card for date range and publisher platform
        result.push({
            title: "Ad Date & Publisher",
            subtitle: "Date Range",
            lastValue: null,
            text: `From ${props.adData.date_start} to ${props.adData.date_stop}. Publisher: ${props.adData.publisher_platform}`,
        });
    }
    return result;
});
</script>
