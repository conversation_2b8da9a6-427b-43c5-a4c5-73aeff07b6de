<script setup>
import { <PERSON>, <PERSON> } from "@inertiajs/vue3";
import AppLayout from "../Layouts/AppLayout.vue";

defineProps({
    canLogin: {
        type: Boolean,
    },
    canRegister: {
        type: <PERSON>olean,
    },
});
</script>

<template>
    <AppLayout>
        <Head title="Welcome" />

        <div class="h-200 p-24">
            <h1 class="fs-2xl mb-40 font-bold">Pravi Home</h1>
            <Link href="/register" class="underline">Register</Link>
        </div>
    </AppLayout>
</template>
