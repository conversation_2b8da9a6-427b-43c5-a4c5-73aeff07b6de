<template>
    <LandingLayout @handleJoinWaitlist="handleJoinWaitlist">
        <Head title="Contact us">
            <meta
                name="description"
                content="Find new donors to give to your cause. <PERSON>ravi helps you find and convert new donors without needing an existing donor database."
            />
        </Head>

        <div class="container">
            <div class="mx-auto my-80 max-w-3xl">
                <h1 class="fs-3xl mb-16 font-extrabold">Contact us</h1>
                <p>If you have any questions, feel free to reach out to us.</p>

                <form @submit.prevent="submit" class="my-40">
                    <TextInput
                        id="email"
                        v-model="form.email"
                        name="email"
                        label="Your email"
                        type="email"
                        :rules="[rules.required, rules.email]"
                        required
                        :serverError="form.errors.email"
                    ></TextInput>

                    <TextInput
                        id="name"
                        v-model="form.name"
                        name="name"
                        label="Your name"
                        type="text"
                        :serverError="form.errors.name"
                    ></TextInput>

                    <TextInput
                        id="company"
                        v-model="form.company"
                        name="company"
                        label="Your organisation"
                        type="text"
                        :serverError="form.errors.company"
                    ></TextInput>

                    <SelectInput
                        id="subject"
                        :options="contactFormSubjects"
                        label="Reason for contact"
                        v-model.number="form.subject"
                        required
                        :serverError="form.errors.subject"
                    ></SelectInput>

                    <TextArea
                        id="message"
                        v-model="form.message"
                        name="message"
                        label="Your message"
                        rows="6"
                        required
                        :rules="[rules.required]"
                        :serverError="form.errors.message"
                    ></TextArea>

                    <Button
                        type="submit"
                        class="order-2 h-[48px] w-full max-w-[260px] shrink"
                        color="action"
                        size="md"
                        :disabled="form.processing"
                    >
                        Submit
                    </Button>

                    <p v-if="successMessage" class="mt-8 text-text-success">
                        {{ successMessage }}
                    </p>
                </form>
            </div>
        </div>

        <WaitlistModal
            :showWaitlistModal="showWaitlistModal"
            @close="showWaitlistModal = false"
        />
    </LandingLayout>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { Head, useForm } from "@inertiajs/vue3";
import Button from "@/Components/Button/Button.vue";
import TextInput from "@/Components/TextInput/TextInput.vue";
import LandingLayout from "@/Layouts/LandingLayout.vue";
import WaitlistModal from "@/Components/WaitlistModal/WaitlistModal.vue";
import TextArea from "@/Components/TextArea/TextArea.vue";
import rules from "@/utilities/validation-rules";
import SelectInput from "@/Components/SelectInput/SelectInput.vue";
import { contactFormSubjects } from "@/constants";

const successMessage = ref("");
const recaptchaSiteKey = computed(
    () => import.meta.env.VITE_RECAPTCHA_SITE_KEY,
);
const recaptchaResponseToken = ref("");

// Load reCAPTCHA v3 script dynamically
const loadReCaptchaScript = () => {
    return new Promise((resolve, reject) => {
        if (typeof grecaptcha !== "undefined") {
            resolve(grecaptcha);
            return;
        }

        const script = document.createElement("script");
        script.src = `https://www.google.com/recaptcha/api.js?render=${recaptchaSiteKey.value}`;
        script.async = true;
        script.defer = true;

        script.onload = () => resolve(grecaptcha);
        script.onerror = reject;

        document.head.appendChild(script);
    });
};

onMounted(async () => {
    try {
        const grecaptcha = await loadReCaptchaScript();
        grecaptcha.ready(() => {
            grecaptcha
                .execute(recaptchaSiteKey.value, { action: "submit" })
                .then((token) => {
                    recaptchaResponseToken.value = token;
                });
        });
    } catch (error) {
        console.error("Failed to load reCAPTCHA v3 script:", error);
    }
});

// Form data and submission
const form = useForm({
    email: "",
    name: "",
    company: "",
    message: "",
    subject: null,
    "g-recaptcha-response": "",
});

const submit = () => {
    if (!recaptchaResponseToken.value) {
        successMessage.value = "Please complete the reCAPTCHA.";
        return;
    }

    form["g-recaptcha-response"] = recaptchaResponseToken.value;

    form.post(route("api.contact_us"), {
        onSuccess: () => {
            successMessage.value =
                "Thank you, your message was successfully submitted.";
            form.email = "";
            form.name = "";
            form.company = "";
            form.message = "";
            form.subject = null;
        },
    });
};

const showWaitlistModal = ref(false);
const handleJoinWaitlist = () => {
    showWaitlistModal.value = true;
};
</script>
