<template>
    <LandingLayout @handleJoinWaitlist="handleJoinWaitlist">
        <Head title="Terms and conditions">
            <meta name="description" content="Terms and conditions of Pravi." />
        </Head>

        <div class="border-t border-border-primary-light">
            <div
                class="prose mx-auto my-32 w-full max-w-prose lg:my-48"
                v-html="terms"
            />
        </div>

        <WaitlistModal
            :showWaitlistModal="showWaitlistModal"
            @close="showWaitlistModal = false"
        />
    </LandingLayout>
</template>

<script setup>
import { Head } from "@inertiajs/vue3";
import LandingLayout from "@/Layouts/LandingLayout.vue";
import WaitlistModal from "@/Components/WaitlistModal/WaitlistModal.vue";
import { ref } from "vue";

defineProps({
    terms: String,
});

const showWaitlistModal = ref(false);

const handleJoinWaitlist = () => {
    showWaitlistModal.value = true;
};
</script>
