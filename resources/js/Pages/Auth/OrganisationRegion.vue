<template>

    <Head title="Organisation region">
        <meta name="description" content="Add your organisation region to your Pravi account." />
    </Head>

    <OnboardingLayout :currentStep="{ step: 4, sub: 1 }">
        <div class="space-y-48">
            <p>To help us generate content for you</p>
            <h1 class="fs-2xl font-bold">
                Tell us where you do this<span class="text-text-action">*</span>
            </h1>
        </div>

        <form @submit.prevent="submit"">
            <TextInput id="region" v-model="form.region" name="region"
                label="National, North West England, Durham...you get the idea" type="text" :rules="[rules.required]"
                :serverError="form.errors.region" required></TextInput>

            <div class="mt-24 flex justify-between lg:mt-80">
                <button @click.prevent="back" class="btn--secondary" :disabled="form.processing">
                    <IconArrowLeft />
                    Back
                </button>

                <button type="submit" class="btn--secondary group" :disabled="form.processing || !form.region">
                    Continue

                    <IconArrowRight class="stroke-text-action-hover group-disabled:stroke-text-disabled" />
                </button>
            </div>
        </form>

        <template v-slot:image>
            <img class="h-auto w-auto max-w-[600px] lg:max-h-full xl:max-w-[800px] xl:-translate-x-64"
                src="/images/onboarding/onboarding-3.png" alt="" width="800" height="645" />
        </template>
    </OnboardingLayout>
</template>

<script setup>
import { Head, useForm, router } from "@inertiajs/vue3";
import OnboardingLayout from "@/Layouts/OnboardingLayout.vue";
import IconArrowRight from "@/Components/Icons/IconArrowRight.vue";
import TextInput from "@/Components/TextInput/TextInput.vue";
import IconArrowLeft from "@/Components/Icons/IconArrowLeft.vue";
import rules from "@/utilities/validation-rules";

const props = defineProps({
    region: String,
});

const form = useForm({
    region: props.region ?? "",
});

const submit = () => {
    form.post(route("api.setup.about"), {
        onSuccess: () => {
            router.get("/setup/organisation-language");
        },
        onError: (err) => {
            console.log(err);
        },
    });
};

const back = () => {
    router.get("/setup/organisation-mission");
};
</script>
