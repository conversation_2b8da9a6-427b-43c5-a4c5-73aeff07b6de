<template>
    <Head title="About your organisation">
        <meta
            name="description"
            content="Please briefly describe your organization's mission.)"
        />
    </Head>

    <AuthLayout>
        <template v-slot:aside>
            <AuthAside :activePage="3"></AuthAside>
        </template>

        <div class="w-full max-w-[844px] md:px-24 lg:px-48">
            <h1 class="fs-2xl mb-40 font-bold">About you</h1>

            <p>
                Please briefly describe your organisation's mission (this helps
                us give you better results)
            </p>

            <form @submit.prevent="submit" class="my-40">
                <TextArea
                    v-model="form.about"
                    id="about"
                    name="about"
                    label="Let us know how you help and where you do it"
                    rows="6"
                    required
                    :rules="[rules.required]"
                    :serverError="form.errors.about"
                    maxlength="300"
                ></TextArea>

                <div class="mb-8 text-text-disabled">{{ charCount }} / 300</div>

                <div
                    class="border-t border-t-border-primary-light py-24 text-center md:text-right"
                >
                    <Button
                        type="submit"
                        class="h-[48px] w-full max-w-[110px]"
                        color="action"
                        size="md"
                        :disabled="form.processing"
                    >
                        Next
                    </Button>
                </div>
            </form>
        </div>
    </AuthLayout>
</template>

<script setup>
import { Head, useForm, router } from "@inertiajs/vue3";
import Button from "@/Components/Button/Button.vue";
import AuthLayout from "@/Layouts/AuthLayout.vue";
import AuthAside from "@/Components/AuthAside/AuthAside.vue";
import TextArea from "@/Components/TextArea/TextArea.vue";
import rules from "@/utilities/validation-rules";
import { computed, onMounted } from "vue";

const props = defineProps({
    about: String,
});

const form = useForm({
    about: props.about ?? "",
});

const charCount = computed(() => {
    return form.about.length;
});

const submit = () => {
    form.post(route("api.setup.about"), {
        onSuccess: () => {
            router.get("/setup/add-users");
        },
        onError: (err) => {
            console.log(err);
        },
    });
};
</script>
