<template>
    <Head title="Add users">
        <meta name="description" content="Add users to your Pravi account." />
    </Head>

    <AuthLayout>
        <template v-slot:aside>
            <AuthAside :activePage="4"></AuthAside>
        </template>

        <div class="w-full max-w-[844px] md:px-24 lg:px-48">
            <h1 class="fs-2xl font-bold">Add users</h1>

            <form @submit.prevent="submit" class="my-40">
                <div
                    v-if="showForm"
                    v-for="(user, index) in form.users"
                    :key="index"
                    class="relative mb-4 gap-x-40 md:grid md:grid-cols-2"
                    :class="{
                        'border-t border-t-border-primary-light pt-32':
                            index > 0,
                    }"
                >
                    <TextInput
                        :id="'first_name_' + index"
                        v-model="user.first_name"
                        name="first_name"
                        label="First name"
                        type="text"
                        required
                        :serverError="
                            form.errors['users.' + index + '.first_name']
                        "
                    ></TextInput>

                    <TextInput
                        :id="'last_name_' + index"
                        v-model="user.last_name"
                        name="last_name"
                        label="Last name"
                        type="text"
                        required
                        :serverError="
                            form.errors['users.' + index + '.last_name']
                        "
                    ></TextInput>

                    <TextInput
                        :id="'email_' + index"
                        v-model="user.email"
                        name="email"
                        label="Email"
                        type="text"
                        :rules="[rules.required, rules.email]"
                        required
                        :serverError="form.errors['users.' + index + '.email']"
                    ></TextInput>

                    <SelectInput
                        :id="'role_' + index"
                        class="mb-28 md:mb-32"
                        :options="roles"
                        label="Role"
                        v-model.number="user.role_id"
                        required
                    ></SelectInput>
                </div>

                <div v-else class="mb-40">
                    You have reached the maximum amount of users profiles for
                    your organisation.
                </div>

                <div
                    v-if="form.users.length > 1"
                    @click.prevent="form.users.pop()"
                    class="-mt-16 pb-32 text-right"
                >
                    <button
                        class="ml-auto rounded-full bg-surface-grey-2x-light p-4"
                    >
                        <span class="sr-only">Remove</span><IconCross />
                    </button>
                </div>

                <div class="flex justify-end gap-32 pb-24">
                    <Button
                        type="button"
                        class="min-h-[48px]"
                        color="default"
                        size="md"
                        @click="showProfilesModal = true"
                    >
                        Profiles list
                        <span v-if="users.length"
                            >&nbsp;({{ users.length }})</span
                        >
                    </Button>

                    <Button
                        type="button"
                        class="min-h-[48px]"
                        color="action"
                        size="md"
                        @click="addProfile"
                        :disabled="!canAddMoreUsers"
                    >
                        Add more profiles ({{ userSpaces }}/5)
                    </Button>
                </div>

                <div
                    class="flex justify-end gap-32 border-t border-t-border-primary-light py-24"
                >
                    <Link
                        href="/setup/thank-you"
                        class="btn btn--default btn--md h-[48px] min-w-[110px]"
                        >Skip</Link
                    >

                    <Button
                        type="submit"
                        class="h-[48px]"
                        color="action"
                        size="md"
                        :disabled="form.processing"
                    >
                        Send Invitations
                    </Button>
                </div>
            </form>

            <UsersModal
                v-if="showProfilesModal"
                :showProfilesModal="showProfilesModal"
                :tableData="tableData"
                :hasUsers="hasUsers"
                @close="showProfilesModal = false"
                @deleteUsers="deleteUsers"
            ></UsersModal>
        </div>
    </AuthLayout>
</template>

<script setup>
import { Head, useForm, Link, router } from "@inertiajs/vue3";
import TextInput from "@/Components/TextInput/TextInput.vue";
import Button from "@/Components/Button/Button.vue";
import AuthLayout from "@/Layouts/AuthLayout.vue";
import AuthAside from "@/Components/AuthAside/AuthAside.vue";
import SelectInput from "@/Components/SelectInput/SelectInput.vue";
import { ref, computed, onMounted } from "vue";
import UsersModal from "@/Components/UsersModal/UsersModal.vue";
import rules from "@/utilities/validation-rules";
import IconCross from "@/Components/Icons/IconCross.vue";

const props = defineProps({ roles: Array, users: Array });

const form = useForm({
    users: [
        {
            first_name: "",
            last_name: "",
            email: "",
            role_id: null,
        },
    ],
});

const showProfilesModal = ref(false);
const tableData = ref({
    headings: ["First name", "Last name", "Email", "Role", "Action"],
    rows: [],
});

onMounted(() => {
    transformUserData(props.users, props.roles);
});

const hasUsers = computed(() => {
    return props.users.length > 0;
});

const showForm = computed(() => {
    return props.users.length < 5;
});

const userSpaces = computed(() => {
    if (!showForm.value) return 5;
    return props.users.length + form.users.length;
});

const canAddMoreUsers = computed(() => {
    return userSpaces.value <= 4;
});

const transformUserData = (userData, roles) => {
    // Map users data to match the table structure
    tableData.value.rows = userData.map((user) => ({
        id: user.id,
        firstName: user.first_name,
        lastName: user.last_name,
        email: user.email,
        role: findRoleName(user.role_id, roles), // Use the roles prop to find the role name
        action: "delete", // Static value for action
    }));
};

// Helper function to find the role name using the roles prop
const findRoleName = (roleId, roles) => {
    const role = roles.find((role) => role.key === roleId);
    return role ? role.name : "Unknown"; // Return 'Unknown' if role_id is not found
};

const addProfile = () => {
    if (canAddMoreUsers.value) {
        form.users.push({
            first_name: "",
            last_name: "",
            email: "",
            role_id: null,
        });
    }
};

const submit = () => {
    form.post(route("api.setup.add_users"), {
        onSuccess: () => router.get("/setup/thank-you"),
    });
};

const deleteUsers = (id) => {
    router.post(
        route("api.setup.delete_users"),
        {
            ids: [id],
        },
        {
            only: ["users"],
            onSuccess: () => {
                console.log("User deleted");
                transformUserData(props.users, props.roles);
            },
        },
    );
};
</script>
