<template>
    <Head title="Organisation details">
        <meta
            name="description"
            content="Add your organisation details to your Pravi account."
        />
    </Head>

    <AuthLayout>
        <template v-slot:aside>
            <AuthAside :activePage="1"></AuthAside>
        </template>

        <div class="w-full max-w-[844px] md:px-24 lg:px-48">
            <h1 class="fs-2xl font-bold">Organisation info</h1>

            <form @submit.prevent="submit" class="my-40">
                <div class="gap-x-40 md:grid md:grid-cols-2">
                    <TextInput
                        id="name"
                        v-model="form.name"
                        name="name"
                        label="Organisation name"
                        type="text"
                        required
                        :serverError="form.errors.name"
                        class="col-span-2"
                    ></TextInput>

                    <TextInput
                        id="legal_name"
                        v-model="form.legal_name"
                        name="legal_name"
                        label="Legal name (if different)"
                        type="text"
                        :serverError="form.errors.legal_name"
                        class="col-span-2"
                    ></TextInput>

                    <TextInput
                        id="address"
                        v-model="form.address"
                        name="address"
                        label="Address"
                        type="text"
                        :serverError="form.errors.address"
                        class="col-span-2"
                    ></TextInput>

                    <TextInput
                        id="city"
                        v-model="form.city"
                        name="city"
                        label="Town / City"
                        type="text"
                        :serverError="form.errors.city"
                    ></TextInput>

                    <TextInput
                        id="region"
                        v-model="form.region"
                        name="region"
                        label="Region / State"
                        type="text"
                        :serverError="form.errors.region"
                    ></TextInput>

                    <TextInput
                        id="postcode"
                        v-model="form.postcode"
                        name="postcode"
                        label="Postcode / Zip code"
                        type="text"
                        :serverError="form.errors.postcode"
                    ></TextInput>

                    <SelectInput
                        id="country"
                        :options="countries"
                        label="Country"
                        v-model.number="form.country_id"
                        required
                    ></SelectInput>

                    <TextInput
                        id="vat"
                        v-model="form.vat"
                        name="vat"
                        label="VAT number (if applicable)"
                        type="text"
                        :serverError="form.errors.vat"
                        class="col-span-2"
                    ></TextInput>
                </div>

                <div
                    class="border-t border-t-border-primary-light py-24 text-center md:text-right"
                >
                    <Button
                        type="submit"
                        class="h-[48px] w-full max-w-[110px]"
                        color="action"
                        size="md"
                        :disabled="form.processing"
                    >
                        Next
                    </Button>
                </div>
            </form>
        </div>
    </AuthLayout>
</template>

<script setup>
import { Head, useForm, router } from "@inertiajs/vue3";
import TextInput from "@/Components/TextInput/TextInput.vue";
import Button from "@/Components/Button/Button.vue";
import AuthLayout from "@/Layouts/AuthLayout.vue";
import AuthAside from "@/Components/AuthAside/AuthAside.vue";
import SelectInput from "@/Components/SelectInput/SelectInput.vue";

const { countries, company } = defineProps({
    countries: Array,
    company: Object,
});

const form = useForm({
    name: company?.name ?? "",
    legal_name: company?.legal_name ?? "",
    address: company?.address ?? "",
    city: company?.city ?? "",
    region: company?.region ?? "",
    postcode: company?.postcode ?? "",
    country_id: company?.country_id ?? null,
    vat: company?.vat ?? "",
});

const submit = () => {
    form.post(route("api.setup.company"), {
        onSuccess: () => {
            router.get("/setup/subscription");
        },
    });
};
</script>
