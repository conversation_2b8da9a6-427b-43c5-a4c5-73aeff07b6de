<template>
    <Head title="Subscription plans">
        <meta
            name="description"
            content="Choose your Pravi subscription plan."
        />
    </Head>
    <div class="flex min-h-screen flex-col">
        <!-- Page Heading -->
        <header class="border-b border-border-primary-light p-16">
            <img
                src="../../../images/pravi-logo-text.png"
                alt="Pravi"
                width="129"
                height="36"
                class="mx-auto"
            />
        </header>

        <section class="container my-104">
            <h1 class="fs-2xl mb-32 text-center font-bold lg:mb-48">
                Confirm your plan to continue
            </h1>

            <div
                class="flex flex-col items-center justify-center gap-32 lg:flex-row 2xl:gap-40"
            >
                <PlanCard
                    v-for="plan of plans"
                    :key="plan.id"
                    :plan="plan"
                    class="max-w-[593px]"
                />
            </div>
        </section>

        <!-- Page Footer -->
        <Footer :navLinks="footerLinks" />

        <CookieConsent />
    </div>
</template>

<script setup>
import { Head } from "@inertiajs/vue3";
import PlanCard from "@/Components/PlanCard/PlanCard.vue";
import Footer from "@/Components/Footer/Footer.vue";
import CookieConsent from "@/Components/CookieConsent/CookieConsent.vue";

defineProps({
    plans: Array,
});

const footerLinks = [
    { text: "Privacy policy", url: "/privacy-policy" },
    { text: "Terms and conditions", url: "/terms-of-service" },
];
</script>
