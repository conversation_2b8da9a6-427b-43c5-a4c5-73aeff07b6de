<template>
    <Head title="Organisation assets">
        <meta
            name="description"
            content="Add your organisation's assets to your Pravi account."
        />
    </Head>

    <OnboardingLayout :currentStep="{ step: 3, sub: 1 }">
        <div>
            <p class="mb-48">To help us generate images</p>

            <h1 class="fs-2xl mb-8 font-bold">
                Upload any of these that you have
            </h1>
            <p class="fs-xs font-medium text-text-grey">
                You need to add at least one
            </p>

            <form @submit.prevent="submit" class="mt-32 space-y-32">
                <div>
                    <div class="mb-16 flex items-center gap-12">
                        <span class="badge">1</span>
                        Your logo
                    </div>
                    <FilePondComponent
                        v-model="logoFiles"
                        :files="logoFiles"
                        :server="serverOptions"
                        :allow-multiple="false"
                        :accepted-file-types="[
                            'image/png',
                            'image/jpeg',
                            'image/jpg',
                        ]"
                        :max-file-size="'2MB'"
                        label-idle="Drop your logo or <span class='filepond--label-action'>Browse</span>"
                        @processfile="(_, f) => (logoId = f.serverId)"
                        @removefile="() => (logoId = null)"
                    />
                </div>

                <div>
                    <div class="mb-16 flex items-center gap-12">
                        <span class="badge">2</span>
                        Reference or example images
                    </div>

                    <FilePondComponent
                        v-model="refFiles"
                        :files="refFiles"
                        :server="serverOptions"
                        :allow-multiple="true"
                        :accepted-file-types="[
                            'image/png',
                            'image/jpeg',
                            'image/jpg',
                        ]"
                        :max-file-size="'2MB'"
                        label-idle="Drop reference images or <span class='filepond--label-action'>Browse</span>"
                        @processfile="(_, f) => refIds.push(f.serverId)"
                        @removefile="
                            (_, f) =>
                                (refIds = refIds.filter(
                                    (i) => i !== f.serverId,
                                ))
                        "
                    />
                </div>

                <div>
                    <div class="mb-16 flex items-center gap-12">
                        <span class="badge">3</span>
                        Your brand guidelines
                    </div>
                    <FilePondComponent
                        v-model="guideFiles"
                        :files="guideFiles"
                        :server="serverOptions"
                        :allow-multiple="true"
                        :max-file-size="'2MB'"
                        :accepted-file-types="['application/pdf']"
                        label-idle="Drop guidelines or <span class='filepond--label-action'>Browse</span>"
                        @processfile="(_, f) => guideIds.push(f.serverId)"
                        @removefile="
                            (_, f) =>
                                (guideIds = guideIds.filter(
                                    (i) => i !== f.serverId,
                                ))
                        "
                    />
                </div>

                <div class="mt-24 flex justify-between">
                    <button @click.prevent="back" class="btn--secondary">
                        <IconArrowLeft />
                        Back
                    </button>

                    <button type="submit" class="btn--secondary">
                        Continue

                        <IconArrowRight
                            class="stroke-text-action-hover group-disabled:stroke-text-disabled"
                        />
                    </button>
                </div>
            </form>
        </div>

        <template v-slot:image>
            <img
                class="h-auto w-auto max-w-[450px] lg:max-h-full xl:max-w-[600px] xl:-translate-x-64"
                src="/images/onboarding/onboarding-5.png"
                alt=""
                width="600"
                height="800"
            />
        </template>
    </OnboardingLayout>
</template>

<script setup>
import { Head, router } from "@inertiajs/vue3";
import OnboardingLayout from "@/Layouts/OnboardingLayout.vue";
import IconArrowRight from "@/Components/Icons/IconArrowRight.vue";
import IconArrowLeft from "@/Components/Icons/IconArrowLeft.vue";
import { ref, onMounted } from "vue";

import vueFilePond from "vue-filepond";
// Import FilePond plugins
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginFileValidateSize from "filepond-plugin-file-validate-size";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";
import "filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css";

// Register plugins by passing them to vueFilePond
const FilePondComponent = vueFilePond(
    FilePondPluginFileValidateType,
    FilePondPluginFileValidateSize,
    FilePondPluginImagePreview,
);

const props = defineProps({
    initialLogo: {
        type: Object,
        default: null,
    },
    initialReferences: {
        type: Array,
        default: () => [],
    },
    initialGuidelines: {
        type: Array,
        default: () => [],
    },
});

// reactive state for FilePond instances
let logoFiles = ref([]);
let refFiles = ref([]);
let guideFiles = ref([]);

// storage of the returned server‐IDs
let logoId = null;
let refIds = [];
let guideIds = [];

// Filepond server.process & revert endpoints
const serverOptions = {
    process: {
        url: "/api/uploads/filepond",
        method: "POST",
        onload: (res) => JSON.parse(res).id,
        onerror: (res) => res,
    },
    revert: {
        url: "/api/uploads/revert",
        method: "DELETE",
        onload: () => {},
    },
};

// when the component mounts, seed FilePond
onMounted(() => {
    if (props.initialLogo) {
        logoFiles.value = [
            {
                source: props.initialLogo.url,
                options: {
                    type: "local",
                    file: {
                        name: props.initialLogo.name,
                        size: props.initialLogo.size,
                        type: props.initialLogo.mime,
                    },
                },
            },
        ];
        // prime the logoId with the storage path:
        logoId = props.initialLogo.path;
    }

    if (props.initialReferences.length) {
        refFiles.value = props.initialReferences.map((f) => ({
            source: f.url,
            options: {
                type: "local",
                file: {
                    name: f.name,
                    size: f.size,
                    type: f.mime,
                },
            },
        }));
        refIds = props.initialReferences.map((f) => f.path);
    }

    if (props.initialGuidelines.length) {
        guideFiles.value = props.initialGuidelines.map((f) => ({
            source: f.url,
            options: {
                type: "local",
                file: {
                    name: f.name,
                    size: f.size,
                    type: f.mime,
                },
            },
        }));
        guideIds = props.initialGuidelines.map((f) => f.path);
    }
});

// –– final submit: send only the IDs to your Laravel controller
function submit() {
    router.post(
        route("api.setup.store-assets"),
        {
            logo: logoId,
            references: refIds,
            guidelines: guideIds,
        },
        {
            onSuccess: () => {
                router.get("/build/campaign");
            },
            onError: (err) => {
                console.log(err);
            },
        },
    );
}

const back = () => {
    router.get("/setup/organisation-tone");
};
</script>
