<template>
    <DashboardLayout title="Organisation Facebook page data">
        <main
            class="w-full overflow-auto bg-surface-grey-2x-light p-16 pt-80 sm:p-16 md:px-32 md:py-24"
        >
            <div class="my-24 flex items-center justify-between gap-20">
                <h1 class="fs-2xl font-bold">
                    Your organisation Facebook page data
                </h1>

                <ButtonLink color="action" href="/settings/integrations">
                    Back
                </ButtonLink>
            </div>

            <p class="mb-24 max-w-5xl">
                This dashboard shows your Facebook analytics in one intuitive
                view. Here, you can review key performance indicators - such as
                reach, impressions, engagement, and more - across various time
                periods. You can use this to make informed decisions to better
                connect with your audience.
            </p>

            <div class="grid min-h-full gap-24 md:grid-cols-2 xl:grid-cols-3">
                <!-- ConfigData Cards -->
                <template v-if="configData">
                    <InfoCard
                        v-for="(card, index) in cards"
                        :key="'config-' + index"
                        :title="card.title"
                        :subtitle="card.subtitle"
                        :text="card.text"
                    >
                        <template #icon>
                            <component :is="card.component" />
                        </template>
                    </InfoCard>
                </template>

                <!-- PageData Cards -->
                <template v-if="pageData">
                    <InfoCard
                        v-for="(card, index) in pageCards"
                        :key="'page-' + index"
                        :title="card.title"
                        :subtitle="card.subtitle"
                        :lastValue="card.lastValue"
                        :text="card.text"
                    >
                        <template #icon>
                            <IconPieChart />
                        </template>
                    </InfoCard>
                </template>

                <!-- AdData Cards -->
                <template v-if="adData">
                    <InfoCard
                        v-for="(card, index) in adCards"
                        :key="'ad-' + index"
                        :title="card.title"
                        :subtitle="card.subtitle"
                        :lastValue="card.lastValue"
                        :text="card.text"
                    >
                        <template #icon>
                            <IconAd />
                        </template>
                    </InfoCard>
                </template>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { computed } from "vue";
import _ from "lodash";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import InfoCard from "@/Components/InfoCard/InfoCard.vue";
import IconFacebook from "@/Components/Icons/FacebookData/IconFacebook.vue";
import IconAd from "@/Components/Icons/FacebookData/IconAd.vue";
import IconCategory from "@/Components/Icons/FacebookData/IconCategory.vue";
import IconPieChart from "@/Components/Icons/FacebookData/IconPieChart.vue";
import IconId from "@/Components/Icons/FacebookData/IconId.vue";
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";

const props = defineProps({
    configData: {
        type: Object,
        required: false,
    },
    pageData: {
        type: Object,
        required: false,
    },
    adData: {
        type: Object,
        required: false,
    },
});

// Helper function to format title strings.
// For example, converts "page_fan_adds (days_28)" to "Page Fan Adds (days 28)".
function formatTitle(title) {
    // Guard against falsy values
    if (!title) return "Unknown Metric";
    const match = title.match(/^(.+?)\s*\((.+)\)$/);
    if (match) {
        const mainPart = match[1]; // e.g. "page_fan_adds"
        const parenPart = match[2]; // e.g. "days_28"
        return `${_.startCase(mainPart)} (${parenPart.replace(/_/g, " ")})`;
    } else {
        return _.startCase(title.replace(/_/g, " "));
    }
}

// Build an array of card objects for the configData
const cards = computed(() => [
    {
        subtitle: "Your Facebook Page",
        title: props.configData.page.name,
        text: "A public profile on Facebook representing your business, brand, or organization.",
        component: IconFacebook,
    },
    {
        subtitle: "Your Facebook Page Categories",
        title: props.configData.page.category,
        text: "Labels that describe the type of business or organization your page represents.",
        component: IconCategory,
    },
    {
        subtitle: "Your Facebook Page ID",
        title: props.configData.page.id,
        text: "A unique numerical identifier assigned to your Facebook Page.",
        component: IconId,
    },
    {
        subtitle: "Your Facebook Business Name",
        title: props.configData.business.name,
        text: "The official name of your business as registered on Facebook Business Manager.",
        component: IconId,
    },
    {
        subtitle: "Your Facebook Business ID",
        title: props.configData.business.id,
        text: "A unique numerical identifier for your Facebook Business.",
        component: IconId,
    },
    {
        subtitle: "Your Facebook Ad Account ID",
        title: props.configData.ad_account.account_id,
        text: "A unique number that identifies your advertising account on Facebook.",
        component: IconAd,
    },
    {
        subtitle: "Your Facebook Latest Ad Performance",
        title: props.configData.latestAdPerformance || "N/A",
        text: "A summary of key metrics from your most recent Facebook ad campaign.",
        component: IconAd,
    },
]);

// Build an array of card objects for the pageData insights
const pageCards = computed(() => {
    const result = [];
    // Loop over each metric array in pageData
    Object.values(props.pageData).forEach((metricArray) => {
        metricArray.forEach((insight) => {
            // Get the last value if available, otherwise null.
            // (We use null so that a value of 0 still renders.)
            const lastValue =
                insight.values && insight.values.length
                    ? insight.values[insight.values.length - 1].value
                    : null;

            // Safely determine the period text.
            const periodText = insight.period
                ? insight.period.replace(/_/g, " ")
                : "N/A";

            // Build a raw title string.
            // Use insight.title if available; otherwise, if insight.name exists, append the period if present.
            const rawTitle =
                insight.title ||
                (insight.name
                    ? insight.period
                        ? `${insight.name} (${insight.period})`
                        : insight.name
                    : "Unknown Metric");

            // Format the title with underscores removed and words capitalized.
            const formattedTitle = formatTitle(rawTitle);

            result.push({
                title: formattedTitle,
                subtitle: `Period: ${periodText}`,
                lastValue: lastValue, // This will be passed to the InfoCard as an optional prop.
                text: insight.description || "",
            });
        });
    });
    return result;
});

// Build an array of card objects for the adData
const adCards = computed(() => {
    const result = [];
    if (props.adData) {
        // Card for Cost Per Click (CPC)
        if (props.adData.cpc) {
            result.push({
                title: "Cost Per Click",
                subtitle: "CPC",
                lastValue: props.adData.cpc,
                text: "Cost per click for your ads.",
            });
        }
        // Card for Click Through Rate (CTR)
        if (props.adData.ctr) {
            result.push({
                title: "Click Through Rate",
                subtitle: "CTR",
                lastValue: props.adData.ctr,
                text: "Click through rate for your ads.",
            });
        }
        // Cards for each action in the actions array
        if (props.adData.actions && Array.isArray(props.adData.actions)) {
            props.adData.actions.forEach((action) => {
                result.push({
                    title: _.startCase(action.action_type),
                    subtitle: "Action Count",
                    lastValue: action.value,
                    text: `Number of ${_.startCase(action.action_type)} actions.`,
                });
            });
        }
        // Card for Ad Date Range
        if (props.adData.date_start && props.adData.date_stop) {
            result.push({
                title: "Ad Date Range",
                subtitle: "Date Range",
                text: `From ${props.adData.date_start} to ${props.adData.date_stop}.`,
            });
        }
    }
    return result;
});
</script>
