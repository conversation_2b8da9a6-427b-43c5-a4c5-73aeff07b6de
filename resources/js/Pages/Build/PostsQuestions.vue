<template>
    <DashboardLayout title="Post Information">
        <main class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24">
            <div
                class="container relative flex items-center justify-center min-h-full flex-col rounded-2xl bg-surface-page">
                <div class="w-full max-w-4xl p-32">

                    <!-- Header -->
                    <div class="mb-32 text-center">
                        <div class="mb-16 flex items-center justify-center">
                            <div
                                class="flex size-64 items-center text-pink-dark justify-center rounded-full bg-surface-action-2x-light">
                                <VsxIcon iconName="MessageQuestion" :size="32" type="linear" />
                            </div>
                        </div>
                        <h1 class="mb-8 font-header text-3xl font-bold text-text-body">
                            Tell us about your posts
                        </h1>
                        <p class="text-lg text-text-secondary">
                            Help us understand your business and goals to create better posts for you
                        </p>
                    </div>

                    <!-- Form -->
                    <form @submit.prevent="handleSubmit" class="space-y-32">

                        <!-- Business Description -->
                        <div>
                            <TextArea
                                v-model="form.business_description"
                                label="Describe your business or campaign"
                                helperText="Tell us what your business does, what you're promoting, or what your campaign is about"
                                :rules="[rules.required]"
                                :serverError="errors.business_description"
                                rows="4"
                            />
                        </div>

                        <!-- Target Audience -->
                        <div>
                            <TextArea
                                v-model="form.target_audience"
                                label="Who is your target audience?"
                                helperText="Describe who you want to reach with your posts (age, interests, demographics, etc.)"
                                :rules="[rules.required]"
                                :serverError="errors.target_audience"
                                rows="3"
                            />
                        </div>

                        <!-- Post Tone -->
                        <div>
                            <label class="mb-16 block font-header text-lg font-bold text-text-body">
                                What tone should your posts have?
                            </label>
                            <Radio
                                v-model="form.post_tone"
                                :options="toneOptions"
                                id="post_tone"
                                :rules="[rules.required]"
                                :serverError="errors.post_tone"
                            />
                        </div>

                        <!-- Key Messages -->
                        <div>
                            <TextArea
                                v-model="form.key_messages"
                                label="Key messages or topics (Optional)"
                                helperText="What specific messages, topics, or themes should we include in your posts?"
                                :serverError="errors.key_messages"
                                rows="3"
                            />
                        </div>

                        <!-- Call to Action -->
                        <div>
                            <TextInput
                                v-model="form.call_to_action"
                                label="Preferred call-to-action (Optional)"
                                helperText="What action do you want people to take? (e.g., 'Visit our website', 'Sign up today', 'Learn more')"
                                :serverError="errors.call_to_action"
                            />
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex justify-between pt-16">
                            <Button
                                type="button"
                                variant="outline"
                                class="space-x-8"
                                @click="router.get('/build')"
                                :disabled="isSubmitting"
                            >
                                <VsxIcon iconName="ArrowLeft" :size="20" type="linear" />
                                <span>Back</span>
                            </Button>

                            <Button
                                size="lg"
                                type="submit"
                                color="action"
                                class="space-x-8"
                                :disabled="!isFormValid || isSubmitting"
                            >
                                <span>{{ isSubmitting ? 'Processing...' : 'Continue to Platform Selection' }}</span>
                                <VsxIcon iconName="ArrowRight" :size="20" type="linear" />
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { router, useForm } from "@inertiajs/vue3"
import DashboardLayout from '@/Layouts/DashboardLayout.vue'
import TextInput from '@/Components/TextInput/TextInput.vue'
import TextArea from '@/Components/TextArea/TextArea.vue'
import Radio from '@/Components/Radio/Radio.vue'
import Button from '@/Components/Button/Button.vue'
import rules from '@/utilities/validation-rules.ts'

defineProps({
    auth: Object,
    errors: {
        type: Object,
        default: () => ({})
    }
})

// Form data
const form = useForm({
    business_description: '',
    target_audience: '',
    post_tone: '',
    key_messages: '',
    call_to_action: ''
})

const isSubmitting = ref(false)

// Tone options
const toneOptions = [
    { id: 'professional', value: 'professional', name: 'Professional' },
    { id: 'casual', value: 'casual', name: 'Casual' },
    { id: 'friendly', value: 'friendly', name: 'Friendly' },
    { id: 'authoritative', value: 'authoritative', name: 'Authoritative' },
    { id: 'playful', value: 'playful', name: 'Playful' }
]

// Computed
const isFormValid = computed(() => {
    return form.business_description.trim() !== '' &&
           form.target_audience.trim() !== '' &&
           form.post_tone !== ''
})

// Methods
const handleSubmit = () => {
    if (!isFormValid.value) return

    isSubmitting.value = true

    form.post('/build/posts/questions', {
        onSuccess: () => {
            console.log('Questions submitted successfully')
        },
        onError: (errors) => {
            console.error('Error submitting questions:', errors)
        },
        onFinish: () => {
            isSubmitting.value = false
        }
    })
}
</script>
