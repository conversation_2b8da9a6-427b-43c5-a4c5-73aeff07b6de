<template>
    <DashboardLayout title="Manage Calendar">
        <main class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24">
            <div
                class="container relative flex items-center justify-center min-h-full flex-col rounded-2xl bg-surface-page">
                <div class="w-full max-w-6xl p-32">
                    <!-- Header -->
                    <div class="mb-32 text-center">
                        <div class="mb-16 flex items-center justify-center">
                            <div
                                class="flex size-80 items-center justify-center rounded-full text-pink-dark bg-surface-action-3x-light">
                                <VsxIcon iconName="Calendar" :size="32" type="linear" />
                            </div>
                        </div>
                        <h1 class="mb-8 font-header text-3xl font-bold text-text-body">
                            Manage Calendar
                        </h1>
                        <p class="text-lg text-text-secondary">
                            Schedule and organize your campaign activities
                        </p>
                    </div>

                    <!-- Calendar Controls -->
                    <div class="mb-24 flex items-center justify-between">
                        <div class="flex items-center space-x-16">
                            <Button @click="previousMonth">
                                <VsxIcon iconName="ArrowLeft" :size="20" type="linear" />
                            </Button>
                            <h2 class="font-header text-xl font-bold text-text-body">
                                {{ currentMonthYear }}
                            </h2>
                            <Button @click="nextMonth">
                                <VsxIcon iconName="ArrowRight" :size="20" type="linear" />
                            </Button>
                        </div>

                        <Button color="action" @click="showAddEventModal = true">
                            <VsxIcon iconName="Add" :size="20" type="linear" />
                            <span>Add Event</span>
                        </Button>
                    </div>

                    <!-- Calendar Grid -->
                    <div class="rounded-[20px] border border-border-primary bg-surface-page shadow-sm overflow-hidden">
                        <!-- Calendar Header -->
                        <div class="grid grid-cols-7 bg-surface-neutral-lightest-grey">
                            <div v-for="day in weekDays" :key="day"
                                class="p-16 text-center font-medium text-text-body border-r border-border-primary last:border-r-0">
                                {{ day }}
                            </div>
                        </div>

                        <!-- Calendar Body -->
                        <div class="grid grid-cols-7">
                            <div v-for="date in calendarDates" :key="date.key"
                                class="min-h-[120px] border-r border-b border-border-primary last:border-r-0 p-8"
                                :class="{
                                    'bg-surface-neutral-lightest-grey': !date.isCurrentMonth,
                                    'bg-surface-action-2x-light': date.isToday
                                }">
                                <div class="flex items-center justify-between mb-8">
                                    <span class="text-sm font-medium" :class="{
                                        'text-text-secondary': !date.isCurrentMonth,
                                        'text-text-body': date.isCurrentMonth,
                                        'text-text-action font-bold': date.isToday
                                    }">
                                        {{ date.day }}
                                    </span>
                                </div>

                                <!-- Events for this date -->
                                <div class="space-y-4">
                                    <div v-for="event in getEventsForDate(date.date)" :key="event.id"
                                        class="rounded px-8 py-4 text-xs cursor-pointer"
                                        :class="getEventClass(event.type)" @click="editEvent(event)">
                                        {{ event.title }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- Back Button -->
                    <div class="mt-32 flex justify-start">
                        <Button class="space-x-12" color="default" @click="router.get('/build')">
                            <VsxIcon iconName="ArrowLeft" :size="20" type="linear" />
                            <span>Back to Build</span>
                        </Button>
                    </div>
                </div>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { ref, computed} from 'vue'
import { router } from "@inertiajs/vue3"
import DashboardLayout from '@/Layouts/DashboardLayout.vue'
import Button from '@/Components/Button/Button.vue'

defineProps({
    auth: Object,
})

// Calendar state
const currentDate = ref(new Date())
const showAddEventModal = ref(false)

// Sample events data
const events = ref([
    {
        id: 1,
        title: 'Campaign Launch',
        date: new Date(2024, 11, 15),
        type: 'campaign'
    },
    {
        id: 2,
        title: 'Social Media Post',
        date: new Date(2024, 11, 18),
        type: 'social'
    },
    {
        id: 3,
        title: 'Email Campaign',
        date: new Date(2024, 11, 22),
        type: 'email'
    }
])

// Calendar computed properties
const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

const currentMonthYear = computed(() => {
    return currentDate.value.toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
    })
})

const calendarDates = computed(() => {
    const year = currentDate.value.getFullYear()
    const month = currentDate.value.getMonth()

    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())

    const dates = []
    const today = new Date()

    for (let i = 0; i < 42; i++) {
        const date = new Date(startDate)
        date.setDate(startDate.getDate() + i)

        dates.push({
            key: `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`,
            date: new Date(date),
            day: date.getDate(),
            isCurrentMonth: date.getMonth() === month,
            isToday: date.toDateString() === today.toDateString()
        })
    }

    return dates
})

const upcomingEvents = computed(() => {
    const today = new Date()
    return events.value
        .filter(event => event.date >= today)
        .sort((a, b) => a.date - b.date)
        .slice(0, 5)
})

// Methods
const previousMonth = () => {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
}

const nextMonth = () => {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
}

const getEventsForDate = (date) => {
    return events.value.filter(event =>
        event.date.toDateString() === date.toDateString()
    )
}

const getEventClass = (type) => {
    const classes = {
        campaign: 'bg-blue-500 text-white',
        social: 'bg-green-500 text-white',
        email: 'bg-purple-500 text-white',
        event: 'bg-orange-500 text-white'
    }
    return classes[type] || 'bg-gray-500 text-white'
}

const editEvent = (event) => {
    // TODO: Implement event editing
    console.log('Editing event:', event)
}

const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}
</script>
