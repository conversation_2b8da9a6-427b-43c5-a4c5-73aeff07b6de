<template>
    <BuildLayout title="Audience results" :auth="auth" :currentStep="1">
        <div class="container">
            <section
                class="overflow-hidden rounded-[20px] border border-border-primary shadow-md"
            >
                <header
                    class="flex justify-between bg-surface-neutral-lightest-grey p-20"
                >
                    <button
                        @click.prevent="handleBack"
                        class="flex items-center gap-4 py-8 font-header text-lg font-bold text-text-body"
                    >
                        <IconArrowLeftBold />
                        Back to donor type
                    </button>

                    <button
                        @click.prevent="handleNext"
                        class="flex items-center gap-4 py-8 font-header text-lg font-bold text-text-body"
                    >
                        Create content
                        <IconArrowRightBold class="stroke-text-body" />
                    </button>
                </header>

                <FindDonorsResults
                    :socialMediaChannels="props.social_media_channels"
                    :communicationChannels="props.communication_channels"
                    :fundraisingCampaigns="props.fundraising_campaigns"
                    hasSaveButton
                />
            </section>
        </div>
    </BuildLayout>
</template>

<script setup>
import Radio from "@/Components/Radio/Radio.vue";
import BuildLayout from "@/Layouts/BuildLayout.vue";
import { onMounted, ref } from "vue";
import IconArrowRightBold from "@/Components/Icons/IconArrowRightBold.vue";
import Button from "@/Components/Button/Button.vue";
import { submitFindDonors, saveCampaign } from "@/utilities/api";
import FindDonorsResults from "@/Components/FindDonorsResults/FindDonorsResults.vue";
import { router } from "@inertiajs/vue3";
import { useFindDonorsStore } from "@/stores/findDonors";
import { storeToRefs } from "pinia";
import IconArrowLeftBold from "@/Components/Icons/IconArrowLeftBold.vue";
import Spinner from "@/Components/Spinner/Spinner.vue";

const findDonors = useFindDonorsStore();
const { form, isLoading } = storeToRefs(findDonors);
const { updateForm, setIsLoading } = findDonors;

const props = defineProps([
    "auth",
    "campaign",
    "social_media_channels",
    "communication_channels",
    "fundraising_campaigns",
    "general_category",
    "sub_category",
]);

// If loaded from a saved campaign, restore the store with campaign data and initialize the conversation steps.
onMounted(() => {
    if (props.campaign?.data_array) {
        findDonors.$patch(props.campaign.data_array);
    }
});

const handleBack = () => {
    router.get("/build/audience");
};

const handleNext = () => {
    router.get("/build/campaign/platforms");
};
</script>
