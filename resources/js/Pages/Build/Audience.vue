<template>
    <BuildLayout title="Build your audience" :auth="auth" :currentStep="1">
        <div class="container">
            <section
                class="overflow-hidden rounded-[20px] border border-border-primary shadow-md"
            >
                <header
                    class="flex justify-between bg-surface-neutral-lightest-grey p-20"
                >
                    <button
                        @click.prevent="router.get('/build/campaign')"
                        class="flex items-center gap-4 py-8 font-header text-lg font-bold text-text-body"
                        :disabled="isLoading"
                    >
                        <IconArrowLeftBold />
                        Back to campaign
                    </button>

                    <button
                        @click.prevent="handleSkip"
                        class="ml-auto flex items-center gap-4 py-8 font-header text-lg font-bold text-text-body"
                        :disabled="isLoading"
                    >
                        Skip and create audience
                        <IconArrowRightBold class="stroke-text-body" />
                    </button>
                </header>

                <div
                    class="min-h-[502px] p-24 py-104 text-center xl:py-[164px]"
                >
                    <Spinner v-if="isLoading" class="mx-auto size-32" />

                    <template v-else>
                        <label for="donorType" class="mb-32 block"
                            >What type of donor are you looking for</label
                        >
                        <Radio
                            id="donorType"
                            v-model="selectedType"
                            :options="donorTypeOptions"
                        />

                        <Button
                            @click.prevent="handleDone"
                            color="action"
                            :disabled="!selectedType"
                            class="h-48 px-48"
                            >Done</Button
                        >
                    </template>
                </div>
            </section>
        </div>
    </BuildLayout>
</template>

<script setup>
import Radio from "@/Components/Radio/Radio.vue";
import BuildLayout from "@/Layouts/BuildLayout.vue";
import { ref } from "vue";
import IconArrowRightBold from "@/Components/Icons/IconArrowRightBold.vue";
import Button from "@/Components/Button/Button.vue";
import { submitFindDonors, saveCampaign } from "@/utilities/api";
import FindDonorsResults from "@/Components/FindDonorsResults/FindDonorsResults.vue";
import { router } from "@inertiajs/vue3";
import { useFindDonorsStore } from "@/stores/findDonors";
import { storeToRefs } from "pinia";
import IconArrowLeftBold from "@/Components/Icons/IconArrowLeftBold.vue";
import Spinner from "@/Components/Spinner/Spinner.vue";

const findDonors = useFindDonorsStore();
const { form, isLoading } = storeToRefs(findDonors);
const { updateForm, setIsLoading } = findDonors;

const props = defineProps([
    "auth",
    "social_media_channels",
    "communication_channels",
    "fundraising_campaigns",
    "general_category",
    "sub_category",
]);

const donorTypeOptions = [
    {
        name: "Most likely to offer a large donation",
        value: "MAXIMIZE_AVERAGE_DONATION",
        id: 1,
    },
    {
        name: "Most likely to convert to a new donor",
        value: "MAXIMIZE_DONORS",
        id: 2,
    },
];

const selectedType = ref("");
const errors = ref(null);

const handleSkip = () => {
    form.value.STRATEGY_TYPE = "BALANCED";
    submit();
};

const handleDone = () => {
    form.value.STRATEGY_TYPE = selectedType.value;
    submit();
};

// Final action submit handlers
const handleSuccess = () => {
    setIsLoading(false);
};

const handleError = (errorData) => {
    errors.value = errorData;
    console.error("Error finding donors:", errorData);
    setIsLoading(false);
};

const submit = async () => {
    setIsLoading(true);

    updateForm({
        REGION_CATEGORY: "ALL",
        GENERAL_CATEGORY: props.general_category,
        SUB_CATEGORY: props.sub_category,
    });

    await submitFindDonors(form.value, handleSuccess, handleError);

    router.get("/build/audience-result");
};
</script>
