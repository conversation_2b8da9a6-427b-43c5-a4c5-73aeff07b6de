<!--
PostsSources.vue - Social Media Platform Selection & Source Input Interface

This component provides a complete frontend interface for:
1. Adding optional source content (Text, URL, PDF) via tabbed interface
2. Selecting social media platforms for content generation
3. Configuring post counts per platform
4. Form validation and user feedback
5. Navigation to content creation page

Frontend-only implementation with TODO comments for backend integration points.
Uses FilePond for PDF uploads and follows the design system patterns.
Source content is optional - users can generate content with platforms only.
-->
<template>
    <DashboardLayout title="Choose Social Platforms">
        <main class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24">
            <div
                class="container relative flex items-center justify-center min-h-full flex-col rounded-2xl bg-surface-page">
                <div class="w-full max-w-6xl p-32">
                    <div class="flex flex-col md:flex-row gap-32">
                        <!-- Left: Source Input -->
                        <div class="w-full md:w-1/2 bg-surface-page p-24 relative">
                            <!-- Visual separator for desktop -->
                            <div class="hidden md:block absolute -right-16 top-8 bottom-8 w-px visual-separator"></div>
                            <div class="flex items-center space-x-12 mb-24">
                                <div class="flex size-32 items-center justify-center rounded-full bg-surface-action-2x-light">
                                    <VsxIcon iconName="DocumentText" :size="20" color="#f13997" type="linear" />
                                </div>
                                <div>
                                    <h2 class="font-header text-2xl font-bold text-text-body">Add Source</h2>
                                    <p class="text-sm text-text-secondary">Optional - helps create better posts</p>
                                </div>
                            </div>

                            <div>
                                <!-- Enhanced Tab Navigation -->
                                <div class="border-b border-border-primary mb-24">
                                    <div class="flex">
                                        <button
                                            v-for="tab in sourceTabs"
                                            :key="tab"
                                            @click="activeTab = tab"
                                            :class="[
                                                'flex-1 flex items-center justify-center space-x-8 px-16 py-12 border-b-2 transition-all duration-200 font-medium',
                                                activeTab === tab
                                                    ? 'border-border-action text-text-action-hover bg-surface-action-2x-light rounded-t-lg'
                                                    : 'border-transparent text-text-secondary hover:text-text-body hover:border-border-action-hover'
                                            ]"
                                        >
                                            <VsxIcon
                                                :iconName="getTabIcon(tab)"
                                                :size="16"
                                                type="linear"
                                                :class="activeTab === tab ? 'text-text-action' : 'text-text-secondary'"
                                            />
                                            <span>{{ tab }}</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- Content Area with consistent height -->
                                <div class="min-h-[200px] relative">
                                    <Transition name="tab-content" mode="out-in">
                                        <!-- Text Input -->
                                        <div v-if="activeTab === 'Text'" key="text">
                                            <TextArea
                                                v-model="sourceInputs.text"
                                                id="source-text"
                                                name="source-text"
                                                label="Paste or write your text here..."
                                                :rows="6"
                                                class="w-full"
                                                helper-text="Enter the text content you want to transform into social media posts"
                                            />
                                            <span v-if="sourceInputs.text" class="text-xs text-text-secondary">
                                                {{ sourceInputs.text.length }} characters
                                            </span>
                                        </div>

                                        <!-- URL Input -->
                                        <div v-else-if="activeTab === 'URL'" key="url">
                                            <TextInput
                                                v-model="sourceInputs.url"
                                                id="source-url"
                                                name="source-url"
                                                label="Paste the URL here..."
                                                type="url"
                                                class="w-full"
                                                helper-text="Paste a link to an article, blog post, or webpage to generate posts from"
                                            />
                                            <div v-if="sourceInputs.url" class="mt-8 text-xs text-text-secondary">
                                                <span>URL: </span>
                                                <a :href="sourceInputs.url" target="_blank" class="text-text-action hover:underline break-all">
                                                    {{ sourceInputs.url }}
                                                </a>
                                            </div>
                                            <!-- TODO: Backend integration - Add URL preview/validation -->
                                        </div>

                                        <!-- PDF Upload -->
                                        <div v-else-if="activeTab === 'PDF'" key="pdf" class="space-y-16">
                                            <div class="w-full">
                                                <FilePondComponent
                                                    v-model="pdfFiles"
                                                    :files="pdfFiles"
                                                    :server="serverOptions"
                                                    :allow-multiple="false"
                                                    :accepted-file-types="['application/pdf']"
                                                    :max-file-size="'10MB'"
                                                    label-idle="Drop your PDF file here or <span class='filepond--label-action'>Browse</span>"
                                                    @processfile="handlePdfProcessed"
                                                    @removefile="handlePdfRemoved"
                                                    class="filepond-custom"
                                                />
                                            </div>
                                            <div v-if="sourceInputs.pdfName" class="text-xs text-text-secondary">
                                                <span class="flex items-center space-x-8">
                                                    <VsxIcon iconName="DocumentDownload" :size="14" type="linear" />
                                                    <span>{{ sourceInputs.pdfName }}</span>
                                                </span>
                                            </div>
                                            <!-- TODO: Backend integration - Add PDF content preview/analysis -->
                                        </div>
                                    </Transition>
                                </div>
                            </div>
                        </div>
                        <!-- Right: Platform Selection (existing) -->
                        <div class="w-full md:w-1/2">
                            <!-- Instructions -->
                            <div class="mb-32 space-y-20">
                                <h1 class="mb-8 font-header text-3xl font-bold text-text-body text-center">
                                    Choose Social Platforms
                                </h1>
                                <p class="text-lg text-text-body text-center">
                                    Select your platforms, click generate and I'll handle the rest!
                                </p>
                            </div>
                            <!-- Platform Selection -->
                            <div class="space-y-8 mb-28">
                                <div v-for="platform in platforms" :key="platform.id"
                                    class="flex items-center justify-between rounded-lg border border-border-primary p-12 hover:border-border-action transition-all cursor-pointer"
                                    @click="togglePlatform(platform.id)">
                                    <div class="flex items-center space-x-12">
                                        <input type="checkbox" :id="platform.id" v-model="selectedPlatforms[platform.id]"
                                            class="size-18 rounded border-border-primary text-surface-action focus:ring-surface-action pointer-events-none" />
                                        <div class="flex items-center space-x-8">
                                            <div class="flex size-32 items-center justify-center rounded-full"
                                                :style="{ backgroundColor: platform.color + '20' }">
                                                <VsxIcon :iconName="platform.iconName" :size="20" :color="platform.color"
                                                    type="linear" />
                                            </div>
                                            <span class="font-medium text-text-body">{{ platform.name }}</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-10">
                                        <span class="text-md text-text-secondary min-w-[60px] text-right mr-8">
                                            {{ postCounts[platform.id] || 1 }} post(s)
                                        </span>
                                        <div class="flex items-center space-x-6">
                                            <Button @click.stop="decrementCount(platform.id)"
                                                :disabled="!selectedPlatforms[platform.id] || postCounts[platform.id] <= 1">
                                                <VsxIcon iconName="Minus" :size="16" type="linear" />
                                            </Button>
                                            <Button @click.stop="incrementCount(platform.id)"
                                                :disabled="!selectedPlatforms[platform.id] || postCounts[platform.id] >= 5">
                                                <VsxIcon iconName="Add" :size="16" type="linear" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Action Buttons -->
                            <div class="flex flex-col space-y-12">
                                <!-- Source Status Indicator -->
                                <div v-if="!hasSourceContent" class="flex items-center space-x-8 text-text-warning text-sm">
                                    <VsxIcon iconName="InfoCircle" :size="16" type="linear" />
                                    <span>No source content added - will generate posts using platform selection only</span>
                                </div>
                                <div v-else-if="!isValidSourceContent" class="flex items-center space-x-8 text-text-warning text-sm">
                                    <VsxIcon iconName="Warning2" :size="16" type="linear" />
                                    <span v-if="activeTab === 'Text'">Text must be at least 10 characters long</span>
                                    <span v-else-if="activeTab === 'URL'">Please enter a valid URL</span>
                                    <span v-else-if="activeTab === 'PDF'">PDF upload is still processing</span>
                                </div>
                                <div v-else class="flex items-center space-x-8 text-text-success text-sm">
                                    <VsxIcon iconName="TickCircle" :size="16" type="linear" />
                                    <span>Source ready for content generation</span>
                                </div>

                                <!-- Navigation Error Display -->
                                <div v-if="navigationError" class="flex items-center space-x-8 text-text-error text-sm">
                                    <VsxIcon iconName="CloseCircle" :size="16" type="linear" />
                                    <span>{{ navigationError }}</span>
                                </div>

                                <div class="flex justify-end items-center">
                                    <Button size="lg" class="space-x-8" @click="continueToContent"
                                        :disabled="!canGenerateWithValidation || isSubmitting" color="action">
                                        <span>{{ isSubmitting ? 'Generating Posts...' : 'Generate Posts' }}</span>
                                        <VsxIcon iconName="ArrowRight" :size="20" type="linear" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { router } from "@inertiajs/vue3"
import DashboardLayout from '@/Layouts/DashboardLayout.vue'
import Button from '@/Components/Button/Button.vue'
import TextInput from '@/Components/TextInput/TextInput.vue'
import TextArea from '@/Components/TextArea/TextArea.vue'
import { VsxIcon } from 'vue-iconsax'

// FilePond imports
import vueFilePond from "vue-filepond"
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type"
import FilePondPluginFileValidateSize from "filepond-plugin-file-validate-size"

// Create FilePond component
const FilePondComponent = vueFilePond(
    FilePondPluginFileValidateType,
    FilePondPluginFileValidateSize,
)

const props = defineProps({
    auth: Object
})

// Source input state
const sourceTabs = ['Text', 'URL', 'PDF']
const activeTab = ref('Text')
const sourceInputs = ref({
    text: '',
    url: '',
    pdf: null,
    pdfName: ''
})

// FilePond state and configuration
const pdfFiles = ref([])
const pdfServerId = ref(null)

// FilePond server configuration
// TODO: Backend integration - Ensure these endpoints are implemented for PDF upload processing
const serverOptions = {
    process: {
        url: "/api/uploads/filepond",
        method: "POST",
        onload: (res) => JSON.parse(res).id,
        onerror: (res) => res,
    },
    revert: {
        url: "/api/uploads/revert",
        method: "DELETE",
        onload: () => {},
    },
}

// FilePond event handlers
// TODO: Backend integration - These handlers manage PDF upload state but don't process content
function handlePdfProcessed(_, file) {
    pdfServerId.value = file.serverId
    sourceInputs.value.pdf = file
    sourceInputs.value.pdfName = file.filename
    // TODO: Backend integration - Add PDF content extraction logic here
}

function handlePdfRemoved() {
    pdfServerId.value = null
    sourceInputs.value.pdf = null
    sourceInputs.value.pdfName = ''
}

// Helper function for tab icons
function getTabIcon(tab) {
    const icons = {
        'Text': 'DocumentText1',
        'URL': 'Link',
        'PDF': 'DocumentDownload'
    }
    return icons[tab] || 'DocumentText1'
}

// Platform options with colors matching the design
const platforms = ref([
    { id: 'twitter', name: 'Twitter', iconName: 'Hashtag', color: '#1DA1F2' },
    { id: 'instagram', name: 'Instagram', iconName: 'Instagram', color: '#E4405F' },
    { id: 'linkedin', name: 'LinkedIn', iconName: 'UserSquare', color: '#0077B5' },
    { id: 'facebook', name: 'Facebook', iconName: 'Facebook', color: '#1877F2' },
    { id: 'pinterest', name: 'Pinterest', iconName: 'Heart', color: '#BD081C' },
    { id: 'tiktok', name: 'TikTok', iconName: 'VideoPlay', color: '#000000' },
    { id: 'threads', name: 'Threads', iconName: 'MessageText', color: '#000000' },
    { id: 'bluesky', name: 'Bluesky', iconName: 'Cloud', color: '#00A8E8' },
    { id: 'youtube', name: 'YouTube', iconName: 'Youtube', color: '#FF0000' },
    { id: 'blog', name: 'Blog', iconName: 'DocumentText1', color: '#6B7280' },
])

// Form data
const selectedPlatforms = ref({})
const postCounts = ref({})
const isSubmitting = ref(false)
const navigationError = ref('')

// Initialize post counts for all platforms
platforms.value.forEach(platform => {
    postCounts.value[platform.id] = 1
})

// Computed
const hasSelectedPlatforms = computed(() => {
    return Object.values(selectedPlatforms.value).some(selected => selected)
})

const hasSourceContent = computed(() => {
    return sourceInputs.value.text.trim() ||
           sourceInputs.value.url.trim() ||
           sourceInputs.value.pdf
})

const canGenerate = computed(() => {
    return hasSelectedPlatforms.value
})

// Additional validation for source content quality
// TODO: Backend integration - Enhance validation with server-side content analysis
const isValidSourceContent = computed(() => {
    if (activeTab.value === 'Text') {
        return sourceInputs.value.text.trim().length >= 10 // Minimum 10 characters
    } else if (activeTab.value === 'URL') {
        const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
        return urlPattern.test(sourceInputs.value.url.trim())
    } else if (activeTab.value === 'PDF') {
        return sourceInputs.value.pdf && pdfServerId.value
    }
    return false
})

const canGenerateWithValidation = computed(() => {
    // If no source content is provided, only require platform selection
    if (!hasSourceContent.value) {
        return hasSelectedPlatforms.value
    }
    // If source content is provided, validate it
    return hasSelectedPlatforms.value && isValidSourceContent.value
})

// Methods
const togglePlatform = (platformId) => {
    selectedPlatforms.value[platformId] = !selectedPlatforms.value[platformId]
    // Clear navigation error when user makes changes
    if (navigationError.value) {
        navigationError.value = ''
    }
}

const incrementCount = (platformId) => {
    if (postCounts.value[platformId] < 5) {
        postCounts.value[platformId]++
    }
}

const decrementCount = (platformId) => {
    if (postCounts.value[platformId] > 1) {
        postCounts.value[platformId]--
    }
}

const continueToContent = async () => {
    if (!canGenerateWithValidation.value) return

    isSubmitting.value = true
    try {
        // Prepare selected platforms data
        const selectedData = {}
        Object.keys(selectedPlatforms.value).forEach(platformId => {
            if (selectedPlatforms.value[platformId]) {
                selectedData[platformId] = postCounts.value[platformId]
            }
        })

        // Prepare source data based on active tab (only if source content exists)
        // TODO: Backend integration - Source data structure for content processing
        let sourceData = null
        if (hasSourceContent.value) {
            sourceData = {
                type: activeTab.value.toLowerCase(),
                content: activeTab.value === 'Text' ? sourceInputs.value.text.trim() :
                        activeTab.value === 'URL' ? sourceInputs.value.url.trim() :
                        pdfServerId.value,
                // Include additional metadata for better processing
                ...(activeTab.value === 'PDF' && sourceInputs.value.pdfName && {
                    fileName: sourceInputs.value.pdfName
                })
            }
        }

        // Clear any previous navigation errors
        navigationError.value = ''

        // Navigate to content generation page with data
        // POST to the correct route that handles platform selection and redirects to content page
        router.post('/build/posts/sources', {
            platforms: selectedData,
            source: sourceData
            // Removed questionsData as requested - no longer needed
        }, {
            onSuccess: () => {
                // Navigation successful - isSubmitting will be reset by page change
                console.log('Successfully navigated to content generation page')
            },
            onError: (errors) => {
                console.error('Navigation failed:', errors)
                navigationError.value = 'Failed to navigate to content generation. Please try again.'
                isSubmitting.value = false
            },
            onFinish: () => {
                // This runs regardless of success/error, but only if we stay on the same page
                // If navigation is successful, this component will be unmounted
            }
        })
    } catch (error) {
        console.error('Error preparing data for navigation:', error)
        isSubmitting.value = false
        // Handle unexpected errors
        // You could show an error message to the user here
    }
}
</script>

<style scoped>
/* Custom FilePond styling to match design system */
:deep(.filepond-custom .filepond--root) {
    border-radius: 8px;
    border: 2px dashed theme('colors.border.primary');
    background: theme('colors.surface.page');
    transition: all 0.2s ease;
}

:deep(.filepond-custom .filepond--root:hover) {
    border-color: theme('colors.border.action-hover');
}

:deep(.filepond-custom .filepond--drop-label) {
    color: theme('colors.text.body');
    font-family: theme('fontFamily.body');
}

:deep(.filepond-custom .filepond--label-action) {
    color: theme('colors.text.action');
    font-weight: 600;
    text-decoration: underline;
}

:deep(.filepond-custom .filepond--panel-root) {
    background: transparent;
    border: none;
}

:deep(.filepond-custom .filepond--item) {
    border-radius: 6px;
}

/* Enhanced tab animations */
.tab-content-enter-active,
.tab-content-leave-active {
    transition: all 0.3s ease;
}

.tab-content-enter-from {
    opacity: 0;
    transform: translateY(10px);
}

.tab-content-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

/* Visual separator styling */
.visual-separator {
    background: linear-gradient(to bottom, transparent, theme('colors.border.primary'), transparent);
}
</style>
