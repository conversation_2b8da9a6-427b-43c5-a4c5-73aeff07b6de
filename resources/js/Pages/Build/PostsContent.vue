<template>
    <DashboardLayout title="Create Posts Content">
        <main class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24">

            <!-- Enhanced Platform Content Tabs -->
            <UnifiedContentTabs
                v-if="hasPlatformsData"
                mode="posts"
                :platformsData="props.platformsData"
                :sourceData="props.sourceData"
                :socialUrl="props.social_url"
                headerTitle="Posts Information"
                backButtonTitle="Back to platform selection"
                scheduleButtonText="Schedule Post"
                scheduleButtonTitle="Schedule your posts"
                backRoute="/build/posts"
                inputPlaceholder="Ask for specific content adjustments..."
                @content-generated="handleContentGenerated"
            />

            <!-- Fallback for no platforms -->
            <div v-else class="flex justify-center items-center h-[400px] bg-white rounded-xl border border-[#DCDDDD]">
                <div class="text-center">
                    <p class="text-lg text-text-secondary mb-16">No platforms selected</p>
                    <Button variant="primary" @click="router.visit('/build/posts')">
                        Select Platforms
                    </Button>
                </div>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { router } from "@inertiajs/vue3"
import DashboardLayout from '@/Layouts/DashboardLayout.vue'
import Button from '@/Components/Button/Button.vue'
import UnifiedContentTabs from '@/Components/ContentSection/UnifiedContentTabs.vue'

const props = defineProps({
    auth: Object,
    platformsData: {
        type: Object,
        default: () => ({})
    },
    sourceData: {
        type: Object,
        default: () => ({})
    },
    social_media_channels: {
        type: [Array, Object],
        default: () => ([])
    },
    social_url: String
})

// Platform data mapping
const platformsMap = {
    'twitter': { name: 'Twitter', icon: 'Hashtag', color: '#1DA1F2' },
    'instagram': { name: 'Instagram', icon: 'Instagram', color: '#E4405F' },
    'linkedin': { name: 'LinkedIn', icon: 'UserSquare', color: '#0077B5' },
    'facebook': { name: 'Facebook', icon: 'Facebook', color: '#1877F2' },
    'pinterest': { name: 'Pinterest', icon: 'Heart', color: '#BD081C' },
    'tiktok': { name: 'TikTok', icon: 'VideoPlay', color: '#000000' },
    'threads': { name: 'Threads', icon: 'MessageText', color: '#000000' },
    'bluesky': { name: 'Bluesky', icon: 'Cloud', color: '#00A8E8' },
    'youtube': { name: 'YouTube', icon: 'Youtube', color: '#FF0000' },
    'blog': { name: 'Blog', icon: 'DocumentText1', color: '#6B7280' },
}



// Reactive data
const isSaving = ref(false)

// Computed properties
const hasPlatformsData = computed(() => {
    return props.platformsData && Object.keys(props.platformsData).length > 0
})

const hasSourceData = computed(() => {
    return props.sourceData && Object.keys(props.sourceData).length > 0
})

// Methods
const handleContentGenerated = (data) => {
    console.log('Content generated for platform:', data.platform, data.content)
    // You can add additional logic here if needed
    // For example, tracking analytics, saving to state, etc.
}
</script>
