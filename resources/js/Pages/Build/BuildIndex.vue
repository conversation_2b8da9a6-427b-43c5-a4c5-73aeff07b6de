<template>
    <DashboardLayout :title="title">
        <main class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24">
            <div
                class="container relative flex items-center justify-center min-h-full flex-col rounded-2xl bg-surface-page">

                <div class="grid grid-cols-1 gap-24 md:grid-cols-3">
                    <!-- Create Posts -->
                    <div class="flex flex-col justify-between group cursor-pointer overflow-hidden rounded-[20px] border border-border-primary bg-surface-page shadow-md transition-all hover:shadow-lg"
                        @click="router.get('/build/posts/sources')">
                        <div class="p-24">
                            <div class="mb-16 flex items-center justify-center">
                                <div
                                    class="flex size-64 items-center text-pink-dark justify-center rounded-full bg-surface-action-2x-light">
                                    <VsxIcon iconName="DocumentText" :size="32" type="linear" />
                                </div>
                            </div>
                            <h3 class="mb-8 text-center font-header text-xl font-bold text-text-body">
                                Create Posts
                            </h3>
                            <p class="text-center text-text-secondary">
                                Create engaging social media posts for your campaigns
                            </p>
                        </div>
                        <div
                            class="p-16 bg-surface-action-3x-light text-center transition-colors group-hover:bg-surface-action-2x-light">
                            <span class="font-medium text-text-action">Create Posts</span>
                        </div>
                    </div>

                    <!-- Create Campaign -->
                    <div class="flex flex-col justify-between group cursor-pointer overflow-hidden rounded-[20px] border border-border-primary bg-surface-page shadow-md transition-all hover:shadow-lg"
                        @click="router.get('/build/campaign')">
                        <div class="p-24">
                            <div class="mb-16 flex items-center justify-center">
                                <div
                                    class="flex size-64 text-pink-dark items-center justify-center rounded-full bg-surface-action-2x-light">
                                    <VsxIcon iconName="Activity" :size="32" type="linear" />
                                </div>
                            </div>
                            <h3 class="mb-8 text-center font-header text-xl font-bold text-text-body">
                                Create Campaign
                            </h3>
                            <p class="text-center text-text-secondary">
                                Define your campaign type, duration, and goals
                            </p>
                        </div>
                        <div
                            class="bg-surface-action-3x-light p-16 text-center transition-colors group-hover:bg-surface-action-2x-light">
                            <span class="font-medium text-text-action">Get Started</span>
                        </div>
                    </div>

                    <!-- Manage Calendar -->
                    <div class="flex flex-col justify-between group cursor-pointer overflow-hidden rounded-[20px] border border-border-primary bg-surface-page shadow-md transition-all hover:shadow-lg"
                        @click="router.get('/build/calendar')">
                        <div class="p-24">
                            <div class="mb-16 flex items-center justify-center">
                                <div
                                    class="flex size-64 items-center text-pink-dark justify-center rounded-full bg-surface-action-2x-light">
                                    <VsxIcon iconName="Calendar" :size="32" type="linear" />
                                </div>
                            </div>
                            <h3 class="mb-8 text-center font-header text-xl font-bold text-text-body">
                                Manage Calendar
                            </h3>
                            <p class="text-center text-text-secondary">
                                Schedule and organize your campaign activities
                            </p>
                        </div>
                        <div
                            class="bg-surface-action-3x-light p-16 text-center transition-colors group-hover:bg-surface-action-2x-light">
                            <span class="font-medium text-text-action">Manage Calendar</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { router } from "@inertiajs/vue3";
import DashboardLayout from '@/Layouts/DashboardLayout.vue'


defineProps({
    auth: Object,
    title: {
        type: String,
        default: 'Build'
    }
});
</script>
