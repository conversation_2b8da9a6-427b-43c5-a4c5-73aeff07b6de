<template>
    <DashboardLayout title="Integrations settings">
        <SettingsPanel>
            <div class="my-48">
                <h2 class="fs-md mb-20 font-bold text-text-headings">
                    Social media connections
                </h2>

                <template v-if="auth.user.is_company_owner">
                    <p class="my-40">
                        <a
                            class="font-medium text-surface-action"
                            href="/settings/integrations"
                            >Refresh this page</a
                        >
                        after connecting an account to update connection status.
                    </p>

                    <IntegrationRow
                        v-for="service in integrations"
                        :key="service.name"
                        :service="service"
                        :isConnected="company[service.integrationCheck]"
                        :company="company"
                    />

                    <div
                        v-if="flash.message"
                        class="mt-24 text-left font-medium text-text-success"
                    >
                        {{ flash.message }}
                    </div>
                </template>

                <template v-else>
                    <p class="my-40">
                        Only the organisation admin can modify integration
                        settings. Please reach out to the owner of your
                        organisation's admin Pravi account for assistance.
                    </p>
                </template>
            </div>
        </SettingsPanel>
    </DashboardLayout>
</template>

<script setup>
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import SettingsPanel from "@/Components/Dashboard/Settings/SettingsPanel.vue";
import { usePage } from "@inertiajs/vue3";
import { ref, computed } from "vue";
import IntegrationRow from "@/Components/IntegrationRow/IntegrationRow.vue";
import { useIntegrationsStore } from "@/stores/integrations";

const { integrations } = useIntegrationsStore();

const { auth, company } = defineProps({ auth: Object, company: Object });

const page = usePage();
const flash = computed(() => page.props.jetstream.flash);
</script>
