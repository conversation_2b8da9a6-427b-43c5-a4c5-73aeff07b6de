<template>
    <DashboardLayout title="Subscription settings">
        <SettingsPanel>
            <div class="my-48 max-w-6xl">
                <div v-if="auth.user.stripe_id" class="space-y-24">
                    <p class="font-semibold">Your subscription is active.</p>
                    
                    <div v-if="auth.user.is_company_owner">
                        <p>Go to the billing portal to manage your subscription.</p>
                        <br>
                        <a
                            :href="stripeCustomerPortal"
                            target="_blank"
                            class="btn btn--md btn--action h-48"
                        >
                            Mange my subscription
                        </a>
                    </div>
                </div>

                <div v-else class="space-y-24">
                    <p>You do not currently have an active subscription.</p>
                    <p>
                        View our plans to activate a subscription, or go to the
                        billing portal to reactivate a previous subscription.
                    </p>
                    <div class="flex flex-col gap-24 md:flex-row">
                        <ButtonLink
                            href="/setup/subscription"
                            color="action"
                            class="h-48 min-w-176"
                            >View plans</ButtonLink
                        >

                        <a
                            :href="stripeCustomerPortal"
                            target="_blank"
                            class="btn btn--md btn--default h-48 min-w-176"
                        >
                            Billing portal
                        </a>
                    </div>
                </div>
            </div>
        </SettingsPanel>
    </DashboardLayout>
</template>

<script setup>
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import SettingsPanel from "@/Components/Dashboard/Settings/SettingsPanel.vue";
import { computed } from "vue";
import ButtonLink from "@/Components/ButtonLink/ButtonLink.vue";

const { auth } = defineProps({ auth: Object });

const stripeCustomerPortal = computed(
    () => import.meta.env.VITE_STRIPE_CUSTOMER_PORTAL_LINK,
);
</script>
