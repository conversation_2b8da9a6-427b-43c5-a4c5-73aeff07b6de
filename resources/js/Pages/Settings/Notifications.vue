<template>
    <DashboardLayout title="Notification settings">
        <SettingsPanel>
            <div class="my-48 max-w-6xl">
                <form>
                    <Toggle
                        id="marketing_checkbox"
                        label="Receive marketing updates and special offers"
                        v-model:checked="form.marketing"
                    />
                </form>

                <div
                    v-if="flash.message"
                    class="mt-24 text-left font-medium text-text-success"
                >
                    {{ flash.message }}
                </div>
            </div>
        </SettingsPanel>
    </DashboardLayout>
</template>

<script setup>
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import SettingsPanel from "@/Components/Dashboard/Settings/SettingsPanel.vue";
import { useForm, usePage } from "@inertiajs/vue3";
import { ref, computed, watch } from "vue";
import Toggle from "@/Components/Toggle/Toggle.vue";

const { auth } = defineProps({ auth: Object });

// Convert backend value (0 or 1) to boolean true/false
const form = useForm({
    marketing: auth.user.marketing === 1,
});

const submit = () => {
    // Adjust form.marketing before submitting to match the controller's expected values
    form.marketing = form.marketing ? true : null;
    form.post(route("settings.notifications.update"), {
        onFinish: (res) => console.log("Form submission finished"),
    });
};

// Watcher to automatically submit the form when the toggle changes
watch(
    () => form.marketing,
    () => {
        submit();
    },
);

const page = usePage();
const flash = computed(() => page.props.jetstream.flash);
</script>
