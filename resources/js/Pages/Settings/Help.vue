<template>
    <DashboardLayout title="Help">
        <SettingsPanel>
            <div class="my-48 max-w-6xl">
                <h1 class="fs-lg font-bold text-black">Connect with us</h1>

                <form @submit.prevent="submit" class="my-40">
                    <SelectInput
                        id="subject"
                        :options="contactFormSubjects"
                        label="Reason for contact"
                        v-model.number="form.subject"
                        required
                        :serverError="form.errors.subject"
                    ></SelectInput>

                    <TextArea
                        id="message"
                        v-model="form.message"
                        name="message"
                        label="Your message"
                        rows="7"
                        required
                        :rules="[rules.required]"
                        :serverError="form.errors.company"
                    ></TextArea>

                    <div class="flex justify-between">
                        <p v-if="successMessage" class="mt-8 text-text-success">
                            {{ successMessage }}
                        </p>

                        <Button
                            type="submit"
                            class="order-2 ml-auto h-[48px] w-full max-w-[160px] shrink"
                            color="action"
                            size="md"
                            :disabled="form.processing"
                        >
                            Send
                        </Button>
                    </div>
                </form>
            </div>
        </SettingsPanel>
    </DashboardLayout>
</template>

<script setup>
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import SettingsPanel from "@/Components/Dashboard/Settings/SettingsPanel.vue";
import { ref, computed, onMounted } from "vue";
import { Head, useForm } from "@inertiajs/vue3";
import Button from "@/Components/Button/Button.vue";
import TextArea from "@/Components/TextArea/TextArea.vue";
import rules from "@/utilities/validation-rules";
import SelectInput from "@/Components/SelectInput/SelectInput.vue";
import { contactFormSubjects } from "@/constants";

const { auth } = defineProps({ auth: Object });

const successMessage = ref("");
const recaptchaSiteKey = computed(
    () => import.meta.env.VITE_RECAPTCHA_SITE_KEY,
);
const recaptchaResponseToken = ref("");

// Load reCAPTCHA v3 script dynamically
const loadReCaptchaScript = () => {
    return new Promise((resolve, reject) => {
        if (typeof grecaptcha !== "undefined") {
            resolve(grecaptcha);
            return;
        }

        const script = document.createElement("script");
        script.src = `https://www.google.com/recaptcha/api.js?render=${recaptchaSiteKey.value}`;
        script.async = true;
        script.defer = true;

        script.onload = () => resolve(grecaptcha);
        script.onerror = reject;

        document.head.appendChild(script);
    });
};

onMounted(async () => {
    try {
        const grecaptcha = await loadReCaptchaScript();
        grecaptcha.ready(() => {
            grecaptcha
                .execute(recaptchaSiteKey.value, { action: "submit" })
                .then((token) => {
                    recaptchaResponseToken.value = token;
                });
        });
    } catch (error) {
        console.error("Failed to load reCAPTCHA v3 script:", error);
    }
});

// Form data and submission
const form = useForm({
    email: auth.user.email,
    name: `${auth.user.first_name} ${auth.user.last_name}`,
    company: "",
    message: "",
    subject: null,
    "g-recaptcha-response": "",
});

const submit = () => {
    if (!recaptchaResponseToken.value) {
        successMessage.value = "Please complete the reCAPTCHA.";
        return;
    }

    form["g-recaptcha-response"] = recaptchaResponseToken.value;

    form.post(route("api.contact_us"), {
        onSuccess: () => {
            successMessage.value =
                "Thank you, your message was successfully submitted.";
            form.message = "";
            form.subject = null;
        },
    });
};
</script>
