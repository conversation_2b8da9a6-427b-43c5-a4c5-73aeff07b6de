<template>
    <DashboardLayout :title="title">
        <main
            class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24"
        >
            <NoSubCard v-if="!auth.user.has_stripe_subscription"></NoSubCard>

            <div
                v-else
                class="relative flex min-h-full flex-col rounded-2xl bg-surface-page"
            >
                <StepperPills
                    :steps="['Campaign', 'Audience', 'Content']"
                    :currentStep="currentStep"
                    class="mx-auto my-56"
                />

                <slot />
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import NoSubCard from "@/Components/NoSubCard/NoSubCard.vue";
import StepperPills from "@/Components/StepperPills/StepperPills.vue";

const props = defineProps({
    title: String,
    auth: Object,
    currentStep: Number,
});
</script>
