<template>
    <main class="flex h-screen w-full">
        <!-- Left Side ~856px on 1440px -->
        <div class="flex flex-col items-center justify-center w-3/5">
            <div class="flex flex-col h-full w-full relative space-y-48 max-w-3xl py-104">
                <!-- Logo with consistent spacing -->
                <div class="space-y-88">
                    <div class="flex w-full ">
                        <img src="/images/logo.png" height="68px" width="60px" alt="Pravi Logo" />
                    </div>
                    <!-- Stepper with spacing -->
                    <div v-if="currentStep" class="mb-12">
                        <Stepper :steps="[1, 1, 1, 1, 1, 1]" :currentStep="currentStep" />
                    </div>
                </div>

                <!-- Content slot with proper spacing -->
                <div class="flex flex-col flex-1">
                    <slot />
                </div>
            </div>
        </div>

        <!-- Right Side ~584px on 1440px -->
        <div class="relative hidden bg-surface-information-light lg:flex w-2/5 items-end justify-start">
            <slot name="image"></slot>
        </div>
    </main>
    <CookieConsent />
</template>

<script setup>
import Stepper from "@/Components/Stepper/Stepper.vue";
import CookieConsent from "@/Components/CookieConsent/CookieConsent.vue";

defineProps({
    title: String,
    currentStep: Object,
});
</script>
