<template>
    <!-- Page Content -->
    <main class="flex flex-row h-screen w-full">
        <div class="hidden flex-1 w-1/2 bg-surface-information-light lg:flex lg:items-end lg:justify-end">
            <AuthVectorImage />
        </div>

        <div class="flex flex-col flex-1 items-center w-1/2">
            <Logo class="mb-48 mt-64" />
            <slot />
        </div>
    </main>
    <CookieConsent />
</template>

<script setup>
import CookieConsent from "@/Components/CookieConsent/CookieConsent.vue";

import Logo from "@/Components/VectorAssets/Logo.vue";
import AuthVectorImage from "@/Components/VectorAssets/AuthVectorImage.vue";

defineProps({
    title: String,
});
</script>
