<template>
    <Head :title="title" />
    <div class="flex h-full max-h-screen overflow-hidden">
        <NavbarDashboard :user="$page.props.auth.user" />

        <slot />
    </div>
    <CookieConsent />
</template>

<script setup>
import { Head } from "@inertiajs/vue3";
import CookieConsent from "@/Components/CookieConsent/CookieConsent.vue";
import NavbarDashboard from "@/Components/Dashboard/NavbarDashboard/NavbarDashboard.vue";

defineProps({
    title: String,
});
</script>
