<template>
    <div class="flex min-h-screen flex-col">
        <NavbarLanding @handleJoinWaitlist="$emit('handleJoinWaitlist')" />

        <main>
            <slot />
        </main>

        <Footer :navLinks="footerLinks" />

        <CookieConsent />
    </div>
</template>

<script setup>
import NavbarLanding from "@/Components/NavbarLanding/NavbarLanding.vue";
import Footer from "@/Components/Footer/Footer.vue";
import CookieConsent from "@/Components/CookieConsent/CookieConsent.vue";

defineProps({
    title: String,
});

defineEmits(["handleJoinWaitlist"]);

const footerLinks = [
    { text: "Privacy policy", url: "/privacy-policy" },
    { text: "Terms and conditions", url: "/terms-of-service" },
    { text: "Price", url: "/#plans" },
    { text: "FAQ", url: "/faq" },
    { text: "Blog", url: "/blog" },
    { text: "Contact us", url: "/contact-us" },
];
</script>
