import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./persona";

export type DonorResults = DonorPersona[];

export interface GenderResult {
    COUNT: number;
    DERIVED_GENDER: string;
}

export interface AgeResult {
    AGE_BAND: string;
    COUNT: number;
}

export interface SalaryResult {
    SALARY_BAND: string;
    COUNT: number;
}

export interface AffinityResult {
    social_media: string;
    communication: string;
    other_causes: string;
    brands: string;
    fundraising: string;
}
