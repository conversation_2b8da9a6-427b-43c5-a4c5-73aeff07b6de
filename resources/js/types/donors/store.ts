import type { Don<PERSON>F<PERSON> } from "./form";
import type { SelectOption, DashboardTab } from "../ui/general";
import type {
    <PERSON><PERSON><PERSON><PERSON><PERSON>s,
    GenderResult,
    AgeResult,
    SalaryResult,
    AffinityResult,
} from "./results";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./persona";

/**
 * Interface defining the full shape of the Find Donors store state.
 */
export interface FindDonorsState {
    isLoading: boolean;
    generalCategoryOptions: SelectOption[];
    subCategoryOptions: SelectOption[];
    form: DonorForm;
    isCustomPrediction: boolean;
    donorsResults: DonorResults | null;
    resultsGender: GenderResult[] | null;
    resultsAge: AgeResult[] | null;
    resultsSalary: SalaryResult[] | null;
    resultsAffinity: AffinityResult | null;
    selectedDonor: DonorPersona | null;
    shownResultIndex: number;
    dashboardTabs: DashboardTab[];
    selectedDashboardTab: string;
    chatSessions: Record<string, any>;
    uuid: string | null;
    selectedCampaignType: string | null;
    targetRaiseAmount: number | null;
    campaignDescription: string;
    campaignStartDate: string;
    campaignEndDate: string;
}
