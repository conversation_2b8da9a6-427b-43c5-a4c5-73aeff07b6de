export interface DonorPersona {
    AFFINITY: string;
    AGE: string;
    AVG_DONATIONS_PER_YEAR: string;
    BRAND_INTEREST: string;
    CROSS_INTEREST: string;
    FREQUENCY: number;
    GENDER: string;
    LIKELIHOOD_TO_GIVE_REGULARLY: string;
    LOCATION: string;
    MAX_AMOUNT: string;
    POPULATION: number;
    PRAVI_SCORE: number;
    PREDICTED_AVERAGE_DONATION: string;
    SALARY: string;
    TOTAL_DONATION_AMOUNT: string;
    TOTAL_NUMBER_OF_DONORS: number;
    TRANSACTIONS_TO_USER_RATIO: string;
}
