export interface FindDonorsForm {
    REGION_CATEGORY: string | null;
    GENERAL_CATEGORY: string | null;
    SUB_CATEGORY: string | null;
    AREA_OF_FOCUS: string | null;
    GENDER: string | null;
    AGE: string | null;
    LOCATION: string | null;
    SALARY: string | null;
    STRATEGY_TYPE: string | null;
}

export interface FindDonorsStoreType {
    form: FindDonorsForm;
    uuid?: string | null;
    donorsResults: any[];
    setDonorsResults: (data: any[]) => void;
    setResultsGender: (data: any[]) => void;
    setResultsAge: (data: any[]) => void;
    setResultsSalary: (data: any[]) => void;
    setUuid: (uuid: string) => void;
}
