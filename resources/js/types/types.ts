export interface DonorForm {
    REGION_CATEGORY: string;
    GENERAL_CATEGORY: string;
    SUB_CATEGORY: string | null;
    AREA_OF_FOCUS: string | null;
    STRATEGY_TYPE: string;
    FREQUENCY_WEIGHT: number;
    DONATION_WEIGHT: number;
    AVERAGE_DONATION_WEIGHT: number;
    NUMBER_OF_DONORS_WEIGHT: number;
    GENDER: string | null;
    AGE: number | null;
    LOCATION: string;
    SALARY: number | null;
}

export interface DonorPersona {
    AFFINITY: string;
    AGE: string; // e.g. "25-29"
    AVG_DONATIONS_PER_YEAR: string;
    BRAND_INTEREST: string; // comma-separated values
    CROSS_INTEREST: string; // comma-separated values
    FREQUENCY: number;
    GENDER: string;
    LIKELIHOOD_TO_GIVE_REGULARLY: string;
    LOCATION: string;
    MAX_AMOUNT: string; // e.g. "240.00"
    POPULATION: number;
    PRAVI_SCORE: number;
    PREDICTED_AVERAGE_DONATION: string; // e.g. "£8.02 - £20.90"
    SALARY: string; // e.g. "£20K to £30K"
    TOTAL_DONATION_AMOUNT: string; // e.g. "129834.31"
    TOTAL_NUMBER_OF_DONORS: number;
    TRANSACTIONS_TO_USER_RATIO: string;
}

export type DonorResults = DonorPersona[];

export interface DashboardTab {
    name: string;
    value: string;
    disabled: boolean;
    allowClose: boolean;
    feature?: string;
    platform_channel?: string;
    chatKey?: string;
}

export interface SelectOption {
    id: string;
    name: string;
}

export interface GenderResult {
    COUNT: number;
    DERIVED_GENDER: string;
}

export interface AgeResult {
    AGE_BAND: string;
    COUNT: number;
}

export interface SalaryResult {
    SALARY_BAND: string;
    COUNT: number;
}

export interface AffinityResult {
    social_media: string;
    communication: string;
    other_causes: string;
    brands: string;
    fundraising: string;
}
