/**
 * Pinia store for the Find Donors feature.
 *
 * Manages state and logic for donor form controls, donor persona results, dashboard page, saved campaigns,
 * dashboard tab navigation, and ChatGPT frontend sessions.
 */

import { defineStore } from "pinia";
import { ref, computed } from "vue";
import type {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>Result,
    AgeResult,
    SalaryResult,
    AffinityResult,
    FindDonorsState,
} from "../types/donors";
import type { DashboardTab, SelectOption } from "../types/ui/general";

export const useFindDonorsStore = defineStore("findDonors", () => {
    /** Initial state used for resetting values */
    const initialState: FindDonorsState = {
        isLoading: false,
        generalCategoryOptions: [],
        subCategoryOptions: [],
        form: {
            REGION_CATEGORY: "ALL",
            GENERAL_CATEGORY: null,
            SUB_CATEGORY: null,
            AREA_OF_FOCUS: null,
            STRATEGY_TYPE: "<PERSON>LANCED",
            FREQUENCY_WEIGHT: 0.5,
            DONATION_WEIGHT: 0.5,
            AVE<PERSON>GE_DONATION_WEIGHT: 0.5,
            NUMBER_OF_DONORS_WEIGHT: 0.5,
            GENDER: null,
            AGE: null,
            LOCATION: "National",
            SALARY: null,
        },
        isCustomPrediction: false,
        donorsResults: null,
        resultsGender: null,
        resultsAge: null,
        resultsSalary: null,
        resultsAffinity: null,
        selectedDonor: null,
        shownResultIndex: 0,
        dashboardTabs: [
            {
                name: "Current chat",
                value: "current-chat",
                disabled: false,
                allowClose: false,
            },
        ],
        selectedDashboardTab: "current-chat",
        chatSessions: {},
        uuid: null,

        selectedCampaignType: null,
        targetRaiseAmount: null,
        campaignDescription: "",
        campaignStartDate: "",
        campaignEndDate: "",
    };

    // Reactive state
    const isLoading = ref<boolean>(initialState.isLoading);
    const generalCategoryOptions = ref<SelectOption[]>(
        initialState.generalCategoryOptions,
    );
    const subCategoryOptions = ref<SelectOption[]>(
        initialState.subCategoryOptions,
    );
    const form = ref<DonorForm>({ ...initialState.form });
    const isCustomPrediction = ref<boolean>(initialState.isCustomPrediction);
    const donorsResults = ref<DonorResults | null>(initialState.donorsResults);
    const resultsGender = ref<GenderResult[] | null>(
        initialState.resultsGender,
    );
    const resultsAge = ref<AgeResult[] | null>(initialState.resultsAge);
    const resultsSalary = ref<SalaryResult[] | null>(
        initialState.resultsSalary,
    );
    const resultsAffinity = ref<AffinityResult | null>(
        initialState.resultsAffinity,
    );
    const selectedDonor = ref<DonorPersona | null>(initialState.selectedDonor);
    const shownResultIndex = ref<number>(initialState.shownResultIndex);
    const dashboardTabs = ref<DashboardTab[]>([...initialState.dashboardTabs]);
    const selectedDashboardTab = ref<string>(initialState.selectedDashboardTab);
    const chatSessions = ref<Record<string, any>>({
        ...initialState.chatSessions,
    });
    const uuid = ref<string | null>(initialState.uuid);

    const selectedCampaignType = ref<string | null>(
        initialState.selectedCampaignType,
    );
    const targetRaiseAmount = ref<number | null>(
        initialState.targetRaiseAmount,
    );
    const campaignDescription = ref<string>(initialState.campaignDescription);
    const campaignStartDate = ref<string>(initialState.campaignStartDate);
    const campaignEndDate = ref<string>(initialState.campaignEndDate);

    /** Reset all store state to initial values */
    function resetStore(): void {
        isLoading.value = initialState.isLoading;
        generalCategoryOptions.value = [...initialState.generalCategoryOptions];
        subCategoryOptions.value = [...initialState.subCategoryOptions];
        form.value = { ...initialState.form };
        isCustomPrediction.value = initialState.isCustomPrediction;
        donorsResults.value = initialState.donorsResults;
        resultsGender.value = initialState.resultsGender;
        resultsAge.value = initialState.resultsAge;
        resultsSalary.value = initialState.resultsSalary;
        resultsAffinity.value = initialState.resultsAffinity;
        selectedDonor.value = initialState.selectedDonor;
        shownResultIndex.value = initialState.shownResultIndex;
        dashboardTabs.value = [...initialState.dashboardTabs];
        selectedDashboardTab.value = initialState.selectedDashboardTab;
        chatSessions.value = { ...initialState.chatSessions };
        uuid.value = initialState.uuid;
        selectedCampaignType.value = initialState.selectedCampaignType;
        targetRaiseAmount.value = initialState.targetRaiseAmount;
        campaignDescription.value = initialState.campaignDescription;
        campaignStartDate.value = initialState.campaignStartDate;
        campaignEndDate.value = initialState.campaignEndDate;
    }

    // Global
    function setIsLoading(val: boolean): void {
        isLoading.value = val;
    }

    // Controls
    function setGeneralCategoryOptions(options: SelectOption[]): void {
        generalCategoryOptions.value = [...options];
    }

    function setSubCategoryOptions(options: SelectOption[]): void {
        subCategoryOptions.value = [...options];
    }

    function updateForm(updates: Partial<DonorForm>): void {
        form.value = { ...form.value, ...updates };
    }

    function clearForm(): void {
        form.value = { ...initialState.form };
        subCategoryOptions.value = [...initialState.subCategoryOptions];
    }

    function clearFormPersonaData(): void {
        form.value.GENDER = initialState.form.GENDER;
        form.value.AGE = initialState.form.AGE;
        form.value.SALARY = initialState.form.SALARY;
    }

    // Results
    function setIsCustomPrediction(val: boolean): void {
        isCustomPrediction.value = val;
    }

    function setDonorsResults(results: DonorResults | null): void {
        donorsResults.value = results;
    }

    function setSingleDonorResult(result: DonorPersona): void {
        if (donorsResults.value) {
            donorsResults.value[shownResultIndex.value] = result;
        }
    }

    function setResultsGender(results: GenderResult[] | null): void {
        resultsGender.value = results;
    }

    function setResultsAge(results: AgeResult[] | null): void {
        resultsAge.value = results;
    }

    function setResultsSalary(results: SalaryResult[] | null): void {
        resultsSalary.value = results;
    }

    function setResultsAffinity(value: AffinityResult | null): void {
        resultsAffinity.value = value;
    }

    function setSelectedDonor(donor: DonorPersona | null): void {
        selectedDonor.value = donor;
    }

    function setShownResultIndex(index: number): void {
        shownResultIndex.value = index;
    }

    // Dashboard Tabs
    function addDashboardTab(
        title: string,
        feature?: string,
        platform_channel?: string,
        chatKey?: string,
    ): void {
        const value = title.toLowerCase().replace(/\s+/g, "_");
        if (!dashboardTabs.value.some((tab) => tab.value === value)) {
            dashboardTabs.value.push({
                name: title,
                value,
                disabled: false,
                allowClose: true,
                feature,
                platform_channel,
                chatKey,
            });
        }
        selectedDashboardTab.value = value;
    }

    function removeDashboardTab(value: string): void {
        dashboardTabs.value = dashboardTabs.value.filter(
            (tab) => tab.value !== value,
        );
        if (chatSessions.value[value]) delete chatSessions.value[value];
        if (selectedDashboardTab.value === value) {
            const defaultTab =
                dashboardTabs.value.find(
                    (tab) => tab.value === "current-chat",
                ) || dashboardTabs.value[0];
            selectedDashboardTab.value = defaultTab ? defaultTab.value : "";
        }
    }

    function setDashboardActiveTab(value: string): void {
        selectedDashboardTab.value = value;
    }

    // Chat Sessions
    function clearChatSessions(): void {
        chatSessions.value = { ...initialState.chatSessions };
    }

    function setChatSession(chatKey: string, messages: any): void {
        chatSessions.value[chatKey] = messages;
    }

    function getChatSession(chatKey: string): any[] {
        return chatSessions.value[chatKey] || [];
    }

    // UUID
    function setUuid(newUuid: string): void {
        uuid.value = newUuid;
    }

    function setSelectedCampaignType(newCampaignType: string): void {
        selectedCampaignType.value = newCampaignType;
    }

    function setTargetRaiseAmount(newTargetRaiseAmount: number): void {
        targetRaiseAmount.value = newTargetRaiseAmount;
    }

    function setCampaignDescription(newCampaignDescription: string): void {
        campaignDescription.value = newCampaignDescription;
    }

    function setCampaignStartDate(date: string) {
        campaignStartDate.value = date;
    }

    function setCampaignEndDate(date: string) {
        campaignEndDate.value = date;
    }

    // Computed
    const hasPersonaData = computed<boolean>(() => {
        return (
            form.value.GENDER !== null ||
            form.value.AGE !== null ||
            form.value.SALARY !== null
        );
    });

    const activeDonorResult = computed<DonorPersona | undefined>(() => {
        return donorsResults.value
            ? donorsResults.value[shownResultIndex.value]
            : undefined;
    });

    return {
        form,
        generalCategoryOptions,
        subCategoryOptions,
        donorsResults,
        isLoading,
        shownResultIndex,
        selectedDonor,
        resultsGender,
        resultsAge,
        resultsSalary,
        resultsAffinity,
        dashboardTabs,
        selectedDashboardTab,
        chatSessions,
        isCustomPrediction,
        uuid,
        selectedCampaignType,
        targetRaiseAmount,
        campaignDescription,
        campaignStartDate,
        campaignEndDate,

        resetStore,
        setDonorsResults,
        setGeneralCategoryOptions,
        setSubCategoryOptions,
        clearForm,
        updateForm,
        setIsLoading,
        clearFormPersonaData,
        setSingleDonorResult,
        setShownResultIndex,
        setSelectedDonor,
        setResultsGender,
        setResultsAge,
        setResultsSalary,
        setResultsAffinity,
        addDashboardTab,
        setDashboardActiveTab,
        clearChatSessions,
        setChatSession,
        getChatSession,
        removeDashboardTab,
        setIsCustomPrediction,
        setUuid,
        setSelectedCampaignType,
        setTargetRaiseAmount,
        setCampaignDescription,
        setCampaignStartDate,
        setCampaignEndDate,

        hasPersonaData,
        activeDonorResult,
    };
});
