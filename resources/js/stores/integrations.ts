import axios from "axios";
import { defineStore } from "pinia";
import { ref } from "vue";

interface Integration {
    id: string;
    name: string;
    description: string;
    handleConnect: () => void;
    handleDisconnect: () => void;
    integrationCheck: string;
}

export const useIntegrationsStore = defineStore("integrations", () => {
    const integrations = ref<Integration[]>([
        {
            id: "meta",
            name: "Meta (Facebook, Instagram)",
            description:
                "Enables benchmarker analysis of your organisation's Facebook and Instagram account if this option is connected. For a successful connection, please make sure you have an active Facebook page and business asset, and instagram business account. Please only select your primary page and organisation asset. If you have multiple pages, make sure to select the only one you need to integrate.",
            handleConnect: () => {
                window.open("/auth/meta", "_blank");
            },
            handleDisconnect: () => {
                axios
                    .post("/companies/integrations/delete", {
                        type: "meta",
                    })
                    .then((response) => {
                        console.log(
                            "Successfully disconnected:",
                            response.data,
                            window.location.reload(),
                        );
                    })
                    .catch((error) => {
                        console.error(
                            "Error while disconnecting:",
                            error.response?.data || error.message,
                        );
                    });
            },
            integrationCheck: "is_meta_integrated",
        },
        {
            id: "google",
            name: "Google Analytics",
            description:
                "Enables benchmarker analysis of your organisation's Google Analytics account if this option is connected. Please make sure you have a Property set in your Google Analytics administrator panel.",
            handleConnect: () => {
                window.open("/auth/google", "_blank");
            },
            handleDisconnect: () => {
                axios
                    .post("/companies/integrations/delete", {
                        type: "google_analytics",
                    })
                    .then((response) => {
                        console.log(
                            "Successfully disconnected:",
                            response.data,
                            window.location.reload(),
                        );
                    })
                    .catch((error) => {
                        console.error(
                            "Error while disconnecting:",
                            error.response?.data || error.message,
                        );
                    });
            },
            integrationCheck: "is_google_analytics_integrated",
        },
    ]);

    return { integrations };
});
