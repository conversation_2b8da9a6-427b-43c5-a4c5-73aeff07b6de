import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useCampaignBuilderStore = defineStore('campaignBuilder', () => {
  // Campaign step data
  const campaignType = ref('');
  const campaignDuration = ref('');
  const targetAmount = ref(null);
  const campaignDescription = ref('');
  
  // Audience step data
  const audienceData = ref({
    targetAudience: '',
    ageRange: [],
    gender: '',
    location: '',
    interests: []
  });
  
  // Content step data
  const contentData = ref({
    chatHistory: [],
    scheduledDate: null,
    isScheduled: false,
    selectedChannels: [],
    generatedContent: '',
    // Channel-specific content storage
    channelContent: {}
  });
  
  // Helper functions
  const setCampaignData = (data) => {
    campaignType.value = data.campaignType || campaignType.value;
    campaignDuration.value = data.campaignDuration || campaignDuration.value;
    targetAmount.value = data.targetAmount || targetAmount.value;
    campaignDescription.value = data.campaignDescription || campaignDescription.value;
  };
  
  const setAudienceData = (data) => {
    audienceData.value = { ...audienceData.value, ...data };
  };
  
  const setContentData = (data) => {
    contentData.value = { ...contentData.value, ...data };
    
    // Handle channel-specific content
    Object.keys(data).forEach(key => {
      if (key.endsWith('Content') || key.endsWith('HtmlContent')) {
        if (!contentData.value.channelContent) {
          contentData.value.channelContent = {};
        }
        contentData.value.channelContent[key] = data[key];
      }
    });
  };
  
  const addChatMessage = (message) => {
    contentData.value.chatHistory.push(message);
  };
  
  const getChatHistory = () => {
    return contentData.value.chatHistory;
  };
  
  return {
    // State
    campaignType,
    campaignDuration,
    targetAmount,
    campaignDescription,
    audienceData,
    contentData,
    
    // Actions
    setCampaignData,
    setAudienceData,
    setContentData,
    addChatMessage,
    getChatHistory
  };
}); 