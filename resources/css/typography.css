@layer base {
    .fs-xxs {
        @apply text-xxs lg:text-xxs-L xl:text-xxs-XL;
    }

    .fs-xs {
        @apply text-xs lg:text-xs-L xl:text-xs-XL;
    }

    .fs-sm {
        @apply text-sm lg:text-sm-L xl:text-sm-XL;
    }

    .fs-md {
        @apply text-md lg:text-md-L xl:text-md-XL;
    }

    .fs-lg {
        @apply text-lg lg:text-lg-L xl:text-lg-XL;
    }

    .fs-xl {
        @apply text-xl lg:text-xl-L xl:text-xl-XL;
    }

    .fs-2xl {
        @apply text-2xl lg:text-2xl-L xl:text-2xl-XL;
    }

    .fs-3xl {
        @apply text-3xl lg:text-3xl-L xl:text-3xl-XL;
    }

    .fs-4xl {
        @apply text-4xl lg:text-4xl-L xl:text-4xl-XL;
    }

    .fs-5xl {
        @apply text-5xl lg:text-5xl-L xl:text-5xl-XL;
    }

    .fs-6xl {
        @apply text-6xl lg:text-6xl-L xl:text-6xl-XL;
    }

    .fs-7xl {
        @apply text-7xl lg:text-7xl-L xl:text-7xl-XL;
    }

    .fs-8xl {
        @apply text-8xl lg:text-8xl-L xl:text-8xl-XL;
    }

    .fs-9xl {
        @apply text-9xl lg:text-9xl-L xl:text-9xl-XL;
    }
}
