@layer components {
    .tag--default {
        background: theme("colors.surface.page");
        border-color: theme("colors.border.primary");
        color: theme("colors.text.body");
    }

    .tag--black {
        background: theme("colors.surface.page-inv");
        border-color: theme("colors.border.dark-black");
        color: theme("colors.text.body-white");
    }

    .tag--action {
        background: theme("colors.surface.action");
        border-color: theme("colors.border.focus");
        color: theme("colors.text.body-white");
    }

    .tag--information {
        background: theme("colors.surface.information");
        border-color: theme("colors.border.information");
        color: theme("colors.text.body");
    }

    .tag--success {
        background: theme("colors.surface.success");
        border-color: theme("colors.icon.success-light");
        color: theme("colors.text.success");
    }

    .tag--warning {
        background: theme("colors.surface.warning");
        border-color: theme("colors.border.warning");
        color: theme("colors.text.warning");
    }

    .tag--error {
        background: theme("colors.surface.error");
        border-color: theme("colors.border.error");
        color: theme("colors.text.error");
    }
}
