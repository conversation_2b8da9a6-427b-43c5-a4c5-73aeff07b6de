<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel</title>
    <!-- Import Materialize CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.css">
<script src="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js"></script>
    <style>
        body {
            font-family: 'Raleway', sans-serif;
        }
        .section-heading {
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 5px;
        }
    </style>
    
    
</head>
<body class="grey lighten-4">

    <!-- Navbar -->
    <nav class="blue">
        <div class="nav-wrapper container">
            <a href="#" class="brand-logo">Admin Panel</a>
            <ul id="nav-mobile" class="right hide-on-med-and-down">
                <li><a href="/admin">Dashboard</a></li>
                <li><a href="/">Site</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        <!-- Statistics Section -->
        <section class="section">
            <h5 class="section-heading">Dashboard Statistics</h5>
            <div class="row">
                <div class="col s12 m4">
                    <div class="card">
                        <div class="card-content center">
                            <span class="card-title blue-text">Total Users</span>
                            <h4 class="blue-text">{{\App\Models\User::count()}}</h4>
                        </div>
                    </div>
                </div>
                <div class="col s12 m4">
                    <div class="card">
                        <div class="card-content center">
                            <span class="card-title green-text">Active Sessions</span>
                            <h4 class="green-text">TBD</h4>
                        </div>
                    </div>
                </div>
                <div class="col s12 m4">
                    <div class="card">
                        <div class="card-content center">
                            <span class="card-title amber-text">Revenue</span>
                            <h4 class="amber-text">TBD</h4>
                        </div>
                    </div>
                </div>
            </div>
        </section>

         <section class="section">
            <h5 class="section-heading">Reports</h5>
            <div class="row">
                <div class="col s4 m3">
                    <form method="POST" action="{{route('admin.reports.feedbacks')}}">
                        @csrf
                        <button class="btn green waves-effect waves-light" type="submit">Feedbacks</button>
                    </form>
                </div>
                <div class="col s4 m3">
                
                    <form method="POST" action="{{route('admin.reports.logins')}}">
                        @csrf
                        <button class="btn green waves-effect waves-light" type="submit">Logins per day</button>
                    </form>
                </div>

                <div class="col s4 m3">
                
                    <form method="POST" action="{{route('admin.reports.signups')}}">
                        @csrf
                        <button class="btn green waves-effect waves-light" type="submit">Signups per day</button>
                    </form>
                </div>
                
                <div class="col s4 m3">
                
                    <form method="POST" action="{{route('admin.reports.affinity-searches')}}">
                        @csrf
                        <button class="btn green waves-effect waves-light" type="submit">Affinity searches</button>
                    </form>
                </div>
            </div>
        </section>

        <section class="section">
            <h5 class="section-heading">Email Section</h5>
            <div class="row">
                <div class="col s12 m6">
                    <div class="card">
                        <div class="card-content">
                            <span class="card-title">Sign up email content </span>
                            <form method="POST" action="{{route('admin.settings.save')}}">
                                @csrf
                                <div class="input-field">
                                    <input type="hidden" required name="key" value="sign_up_email" />
                                    <textarea class="tinymce editor" id="signupemail" name="value">{{\App\Models\Setting::where('key', 'sign_up_email')->firstOrNew()->value}}</textarea>
                                    <script>
                                        var simplemde = new SimpleMDE({ element: document.getElementById("signupemail") });
                                        // Sync content to the textarea on form submit
            document.querySelector('form').addEventListener('submit', function () {
                document.getElementById('signupemail').value = simplemde.value();
            });
                                    </script>
                                </div>
                                <button class="btn blue waves-effect waves-light" type="submit">Update</button>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- <div class="col s12 m6">
                    <div class="card">
                        <div class="card-content">
                            <span class="card-title">Send Notification</span>
                            <form>
                                <div class="input-field">
                                    <textarea id="message" class="materialize-textarea"></textarea>
                                    <label for="message">Message</label>
                                </div>
                                <button class="btn green waves-effect waves-light" type="submit">Send</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div> -->
        </section>

        <!-- Table Section 
        <section class="section">
            <h5 class="section-heading">Recent Activities</h5>
            <div class="card">
                <div class="card-content">
                    <table class="highlight responsive-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Action</th>
                                <th>User</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Login</td>
                                <td>John Doe</td>
                                <td>2024-11-25</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>Update Profile</td>
                                <td>Jane Smith</td>
                                <td>2024-11-24</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>Logout</td>
                                <td>Bob Brown</td>
                                <td>2024-11-23</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>-->
    </div>

    <!-- Import Materialize JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
</body>
</html>
