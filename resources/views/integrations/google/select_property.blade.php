@extends('layouts.app')

@section('content')
    <div class="container">
        <h2 class="fs-xl font-bold my-24">Select Your Google Analytics Property</h2>

        @if (session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <form action="{{ route('auth.google.select-property') }}" method="POST">
            @csrf
            <input type="hidden" name="token" value="{{ json_encode($token) }}">

            <div class="mb-24">
                <label for="property_id" class="form-label block mb-4">Choose a GA4 Property:</label>
                <select name="property_id" id="property_id" class="form-select" required>
                    <option value="">-- Select Property --</option>
                    @foreach ($propertyList as $property)
                        <option value='{{ json_encode(
                        ['account' => $property['account'], 'property' => $property['property']]
                    ) }}'>{{ $property['name'] }} ({{ $property['id'] }})</option>
                    @endforeach
                </select>
            </div>

            <button type="submit" class="btn btn--action btn">Save Property</button>
        </form>
    </div>
@endsection
