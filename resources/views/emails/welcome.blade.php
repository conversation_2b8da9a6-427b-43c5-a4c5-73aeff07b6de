<x-mail::message>


@php
$email = \App\Models\Setting::where('key', 'sign_up_email')->first()->value ?? null;
@endphp
<div>
<div>

<p>Hi {{$user->first_name}},</p>

@if ($email)
{!!$email!!}
@else
<p>Welcome to <PERSON><PERSON><PERSON>! I’m so glad you’ve joined us.</p>
<p><PERSON>ravi is here to help you find the right donors and grow your charity - without needing a big team or a donor list.</p>
<p>You can log in to your account at <a href="https://Pravi.ai" target="_blank">Pravi.ai</a>. Just click the “Login” button in the top right corner and use the details you signed up with.</p>
<p>If you have any questions just reply to this email - I’m always happy to help.
</p>
<br/>
<p>Regards</p>
<p><PERSON><PERSON></p>
<p>Co-Founder</p>
<p><a href="https://Pravi.ai" target="_blank">Pravi.ai</a></p>
@endif
</div>
</div>
</x-mail::message>
