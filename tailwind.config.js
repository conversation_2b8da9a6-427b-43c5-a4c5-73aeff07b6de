import forms from "@tailwindcss/forms";
import typography from "@tailwindcss/typography";
import { colorTokens, themeLight } from "./resources/js/colors.js";

/** @type {import('tailwindcss').Config} */
export default {
    darkMode: "selector",

    content: [
        "./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
        "./vendor/laravel/jetstream/**/*.blade.php",
        "./storage/framework/views/*.php",
        "./resources/views/**/*.blade.php",
        "./resources/js/**/*.vue",
    ],

    theme: {
        container: {
            center: true,
            padding: "1.5rem",
        },

        extend: {
            backgroundImage: {
                "home-banner": "url('/resources/images/home-banner-bg.png')",
                "line-dotted": "url('/resources/images/line-dotted.png')",
                "shapes-1": "url('/resources/images/shapes-1.png')",
                "lines-1": "url('/resources/images/bg-lines-1.png')",
            },
            boxShadow: {
                DEFAULT: "1px 2px 8px rgba(0, 0, 0, 0.12)",
                card: "1px 6px 12px 4px rgba(0, 0, 0, 0.12)",
            },
            // Figma Typography Variables
            fontFamily: {
                header: ["Manrope", "sans-serif"],
                body: ["Open Sans", "sans-serif"],
            },
            fontWeight: {
                light: "300",
                normal: "400",
                medium: "500",
                bold: "700",
                extrabold: "800",
            },
            fontSize: {
                // MD-XS values
                xxs: ["9px", { lineHeight: "12px" }],
                xs: ["11px", { lineHeight: "12px" }],
                sm: ["12px", { lineHeight: "16px" }],
                md: ["14px", { lineHeight: "16px" }],
                lg: ["16px", { lineHeight: "18px" }],
                xl: ["18px", { lineHeight: "20px" }],
                "2xl": ["22px", { lineHeight: "24px" }],
                "3xl": ["24px", { lineHeight: "24px" }],
                "4xl": ["28px", { lineHeight: "28px" }],
                "5xl": ["32px", { lineHeight: "32px" }],
                "6xl": ["36px", { lineHeight: "36px" }],
                "7xl": ["40px", { lineHeight: "40px" }],
                "8xl": ["48px", { lineHeight: "48px" }],
                "9xl": ["56px", { lineHeight: "56px" }],

                // L values
                "xxs-L": ["10px", { lineHeight: "12px" }],
                "xs-L": ["12px", { lineHeight: "12px" }],
                "sm-L": ["14px", { lineHeight: "16px" }],
                "md-L": ["16px", { lineHeight: "18px" }],
                "lg-L": ["18px", { lineHeight: "20px" }],
                "xl-L": ["20px", { lineHeight: "22px" }],
                "2xl-L": ["24px", { lineHeight: "24px" }],
                "3xl-L": ["30px", { lineHeight: "24px" }],
                "4xl-L": ["36px", { lineHeight: "28px" }],
                "5xl-L": ["40px", { lineHeight: "32px" }],
                "6xl-L": ["44px", { lineHeight: "36px" }],
                "7xl-L": ["48px", { lineHeight: "40px" }],
                "8xl-L": ["56px", { lineHeight: "48px" }],
                "9xl-L": ["62px", { lineHeight: "56px" }],

                // XL values
                "xxs-XL": ["12px", { lineHeight: "16px" }],
                "xs-XL": ["14px", { lineHeight: "16px" }],
                "sm-XL": ["16px", { lineHeight: "22px" }],
                "md-XL": ["18px", { lineHeight: "24px" }],
                "lg-XL": ["20px", { lineHeight: "28px" }],
                "xl-XL": ["22px", { lineHeight: "32px" }],
                "2xl-XL": ["26px", { lineHeight: "36px" }],
                "3xl-XL": ["32px", { lineHeight: "40px" }],
                "4xl-XL": ["40px", { lineHeight: "48px" }],
                "5xl-XL": ["44px", { lineHeight: "56px" }],
                "6xl-XL": ["48px", { lineHeight: "60px" }],
                "7xl-XL": ["52px", { lineHeight: "66px" }],
                "8xl-XL": ["60px", { lineHeight: "72px" }],
                "9xl-XL": ["66px", { lineHeight: "80px" }],
            },
            spacing: {
                // Paragraph spacing MD-XS, L XL values
                "xxs-ps": "8px",
                "xs-ps": "8px",
                "sm-ps": "12px",
                "md-ps": "12px",
                "lg-ps": "16px",
                "xl-ps": "24px",
                "2xl-ps": "24px",
                "3xl-ps": "24px",
                "4xl-ps": "24px",
                "5xl-ps": "32px",
                "6xl-ps": "36px",
                "7xl-ps": "36px",
                "8xl-ps": "36px",
                "9xl-ps": "36px",
            },

            listStyleImage: {
                check: 'url("/images/check.svg")',
            },
        },
        // Figma Golden Proportion Variables
        spacing: {
            0: "0rem",
            1: "0.0625rem",
            2: "0.125rem",
            3: "0.1875rem",
            4: "0.25rem",
            5: "0.3125rem",
            6: "0.375rem",
            8: "0.5rem",
            12: "0.75rem",
            16: "1rem",
            20: "1.25rem",
            24: "1.5rem",
            28: "1.75rem",
            32: "2rem",
            40: "2.5rem",
            48: "3rem",
            56: "3.5rem",
            64: "4rem",
            72: "4.5rem",
            80: "5rem",
            88: "5.5rem",
            104: "6.5rem",
            128: "8rem",
            152: "9.5rem",
            176: "11rem",
            200: "12.5rem",
        },
        // Figma Color Tokens Variables
        colors: {
            transparent: "transparent",
            current: "currentColor",
            ...colorTokens,
            ...themeLight,
        },
    },

    plugins: [forms, typography],
};
