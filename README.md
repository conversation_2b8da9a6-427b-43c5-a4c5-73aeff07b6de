<p align="center">
<svg class="w-[106px] md:w-[136px]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 430.73 85.04"><g data-name="Layer 2"><g data-name="Layer 1"><path d="M29.18 42.63c0 7.43-4.46 15-14.57 15S0 50.43 0 43c0-9 6.45-15 14.73-15 9.58.1 14.45 7.37 14.45 14.63ZM14.61 52.7c6.86 0 9.3-5.24 9.3-10s-2.76-9.7-9.18-9.85c-5.84 0-9.45 4.26-9.45 9.74 0 4.79 2.47 10.11 9.33 10.11ZM61.36 57l-8.24-9.5h-5.44V57h-5.36V28.51h13.52c6.7 0 10.23 4.55 10.23 9.42 0 3.89-1.79 7.83-7.19 8.93l8.61 9.7V57ZM47.68 42.63h8.16c3.41 0 4.87-2.27 4.87-4.54s-1.5-4.55-4.87-4.55h-8.16ZM103.24 36.79a5.75 5.75 0 0 1-3.7 5.48c3.33 1 4.55 4.63 4.55 6.33 0 6.4-4.75 8.4-10.63 8.4H80V28.55h13.46c5.6 0 9.78 2.52 9.78 8.24ZM93.46 40c3.36 0 4.46-1.42 4.46-3.05 0-1.3-1-3.49-4.46-3.49h-8.2V40Zm0 12c2.15 0 5.31-.73 5.31-3.41s-3.16-3.9-5.31-3.9h-8.2V52ZM117.39 28.55h5.32V57h-5.32ZM134.88 33.42v-4.87h23.38v4.87h-9V57h-5.36V33.42ZM175.42 51.6 173 57h-5.81l12.75-28.41h5.84L198.52 57h-5.85l-2.39-5.36Zm2.15-5h10.56l-5.28-12.09ZM215.47 52h14.62v5h-20V28.55h5.35ZM279.33 49h-1.05l-9.06-12.38v20.7h-5.35V28.9H270l8.89 12.22 8.88-12.22h6.13v28.42h-5.36V36.7ZM308.79 57.28V28.86h21.27v5.2h-15.95v6.53h15.38v5h-15.38V52h15.95v5.28ZM369.26 43.11c0 7.1-4.39 14.17-14.21 14.17h-11.16V28.86h11.16c9.86 0 14.21 7.14 14.21 14.25Zm-14.21 9c6.33 0 8.89-4.5 8.89-9s-2.6-9.17-8.89-9.17h-5.84v18.18ZM382.48 28.86h5.32v28.42h-5.32ZM407.64 51.92l-2.44 5.36h-5.8l12.75-28.42H418l12.74 28.42h-5.84l-2.39-5.36Zm2.15-5h10.55l-5.27-12.1ZM245.33 0h3.47v85.04h-3.47z"></path></g></g></svg>
</p>

## About Pravi

Tech stack used in development:

* Backend

  * PHP Laravel
  * MySql
* Frontend

  * HTML/CSS/JavaScript
  * Tailwind
  * Vue.js with Inertia
  * Storybook

## How to set up

`composer install`

Install composer libraries

`cp .env.example .env`

Update your env file with database credentials

`php artisan key:generate`

Generate new key for your app

`php artisan migrate`

Migrate your database

`php artisan db:seed`

Run seeder if needed

`npm install && npm run build`

Install node modules and build them

## Developers

If you need any help, please reach out to the developers


- **[Ali Rasheed](mailto:<EMAIL>)**
- **[Tom Dolton](<EMAIL>)**
