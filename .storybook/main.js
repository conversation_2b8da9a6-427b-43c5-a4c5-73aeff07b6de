const path = require("path");

/** @type { import('@storybook/vue3-vite').StorybookConfig } */
const config = {
    stories: [
        "../stories/**/*.mdx",
        "../stories/**/*.stories.@(js|jsx|mjs|ts|tsx)",
        "../resources/js/Components/**/*.stories.@(js|jsx|mjs|ts|tsx)",
    ],
    addons: [
        "@storybook/addon-onboarding",
        "@storybook/addon-links",
        "@storybook/addon-essentials",
        "@chromatic-com/storybook",
        "@storybook/addon-interactions",
    ],
    framework: {
        name: "@storybook/vue3-vite",
        options: {},
    },
    staticDirs: ["../public"],
    viteFinal: (config) => {
        config.resolve.alias["/public"] = path.resolve(__dirname, "../public");
        return config;
    },
};
export default config;
