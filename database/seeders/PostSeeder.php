<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Models\Post;
use App\Models\PostCategory;

class PostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Don't run in production
        if (app()->environment() !== 'production') {
            // Create 10 dummy posts
            Post::factory(10)->create()->each(function ($post) {
                // Attach categories to each post
                $categories = PostCategory::inRandomOrder()->take(1)->pluck('id'); // Adjust the number if needed
                $post->categories()->attach($categories);
            });
        }
    }
}
