<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Database\Seeders\LocationSeeder;
use Database\Seeders\SalaryBandSeeder;
use Database\Seeders\AgeBandSeeder;
use Database\Seeders\GenderSeeder;
use Database\Seeders\CountriesSeeder;
use Database\Seeders\PostSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run()
    {
        $this->call([
            LocationSeeder::class,
            SalaryBandSeeder::class,
            AgeBandSeeder::class,
            GenderSeeder::class,
            CountriesSeeder::class,
            PostSeeder::class,
        ]);
    }
}
