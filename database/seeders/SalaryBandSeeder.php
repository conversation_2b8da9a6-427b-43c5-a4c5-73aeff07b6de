<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SalaryBand;

class SalaryBandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $salaryBands = [
            '10K to 20K', '20K to 30K', '30K to 40K', '40K to 50K', 
            '50K to 60K', '60K to 70K', '70K to 80K', '< 10K', '> 80K', 'All'
        ];

        foreach ($salaryBands as $band) {
            SalaryBand::create(['range' => $band]);
        }
    }
}
