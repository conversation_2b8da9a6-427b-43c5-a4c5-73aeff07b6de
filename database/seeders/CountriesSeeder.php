<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Country;

class CountriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
    	foreach ($this->countries() as $data) {
    		$country = Country::updateOrCreate([
    			'code' => $data['countryCode'],
    		], [
    			'name' => $data['name'],
    			'dial_code' => $data['phone'],
    			'currency' => $data['currency'],
    		]);

    		echo "\nCountry imported: " . $country->name;
    	}
    }

    private function countries()
    {
    	$json = file_get_contents('database/seeders/country_state.json');
		return json_decode($json,true);
    }

}
