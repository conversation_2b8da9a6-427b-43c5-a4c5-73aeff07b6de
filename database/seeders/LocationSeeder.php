<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Location;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing locations
        Location::truncate();

        $locations = [
            "National",
            "Crown Dependencies",
            "East Midlands",
            "East of England",
            "London",
            "North East England",
            "North West England",
            "Northern Ireland",
            "Scotland",
            "South East England",
            "South West England",
            "Wales",
            "West Midlands",
            "Yorkshire and the Humber"
        ];

        foreach ($locations as $location) {
            Location::create(['name' => $location]);
        }
    }
}
