<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('personas', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('name')->nullable();
            $table->string('general_category')->nullable();
            $table->string('sub_category')->nullable();
            $table->string('frequency_weight')->nullable();
            $table->string('donation_weight')->nullable();
            $table->string('average_donation_weight')->nullable();
            $table->string('number_of_donors_weight')->nullable();
            $table->string('gender')->nullable();
            $table->string('age')->nullable();
            $table->string('location')->nullable();
            $table->string('area_of_focus')->nullable();
            $table->string('salary')->nullable();
            $table->timestamps();
        });

        Schema::create('persona_results', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('persona_id');
            $table->string('affinity')->nullable();
            $table->string('frequency')->nullable();
            $table->string('likelihood_to_give_regularly')->nullable();
            $table->string('max_amount')->nullable();
            $table->string('pravi_score')->nullable();
            $table->string('predicted_average_donation')->nullable();
            $table->string('total_donation_amount')->nullable();
            $table->string('total_number_of_donors')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('personas');
        Schema::dropIfExists('persona_results');
    }
};
