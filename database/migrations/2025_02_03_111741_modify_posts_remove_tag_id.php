<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['tag_id']);
            // Now drop the column
            $table->dropColumn('tag_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            // Re-add the column and foreign key for rollback
            $table->foreignId('tag_id')->nullable()->constrained('post_categories')->onDelete('cascade');
        });
    }
};
