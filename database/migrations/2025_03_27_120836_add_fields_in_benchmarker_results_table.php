<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('benchmarker_results', function (Blueprint $table) {
            $table->string('gender')->nullable();
            $table->string('age')->nullable();
            $table->string('category')->nullable();
            $table->string('subcategory')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('benchmarker_results', function (Blueprint $table) {
            $table->dropColumn('gender');
            $table->dropColumn('age');
            $table->dropColumn('category');
            $table->dropColumn('subcategory');
            
        });
    }
};
