<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

use App\Models\Plan;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('stripe_price_id');
            $table->string('title');
            $table->string('subtitle')->nullable();
            $table->text('description')->nullable();
            $table->timestamps();
        });

        Plan::create([
            'stripe_price_id' => 'dummy',
            'title' => 'Monthly Price',
            'subtitle' => '£40 / per month',
            'description' => "Build unlimited campaigns---Live benchmarking---Make data driven decisions---Save time and money building your fundraising campaigns",
        ]);

        Plan::create([
            'stripe_price_id' => 'dummy',
            'title' => 'Best Value per 1 year',
            'subtitle' => '£33 / per month',
            'description' => "Save 20% on your monthly subscription---Build unlimited campaigns---Live benchmarking---Make data driven decisions---Save time and money building your fundraising campaigns"
        ]);


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
