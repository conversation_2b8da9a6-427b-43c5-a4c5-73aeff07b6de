<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_integrations', function (Blueprint $table) {
            $table->dropColumn('instagram_business_account_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_integrations', function (Blueprint $table) {
            $table->string('instagram_business_account_id')->nullable()->after('entity_id');
        });
    }
};
