<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function(Blueprint $table) {
            $table->string('first_name')->after('id');
        });

        Schema::table('users', function(Blueprint $table) {
            $table->string('last_name')->after('first_name');
        });

        Schema::table('users', function(Blueprint $table) {
            $table->dropColumn('name');
        });

        Schema::table('users', function(Blueprint $table) {
            $table->integer('role_id')->after('password');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function(Blueprint $table) {
            $table->dropColumn('first_name');
        });

        Schema::table('users', function(Blueprint $table) {
            $table->dropColumn('last_name');
        });

        Schema::table('users', function(Blueprint $table) {
            $table->string('name');
        });

        Schema::table('users', function(Blueprint $table) {
            $table->dropColumn('role_id');
        });
    }
};
