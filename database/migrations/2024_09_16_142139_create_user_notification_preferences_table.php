<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->boolean('sms')->nullable();
            $table->boolean('email')->nullable();
            $table->boolean('marketing')->nullable();
            $table->boolean('account_alerts')->nullable();
            $table->boolean('news_updates')->nullable();
            $table->boolean('surveys_feedback')->nullable();
            $table->boolean('support_service')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_notification_preferences');
    }
};
