<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::rename('social_media_integrations', 'company_integrations');

        Schema::table('company_integrations', function(Blueprint $table) {
            $table->renameColumn('page_id', 'entity_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_integrations', function(Blueprint $table) {
            $table->renameColumn('entity_id', 'page_id');
        });

        Schema::rename('company_integrations', 'social_media_integrations');
    }
};
