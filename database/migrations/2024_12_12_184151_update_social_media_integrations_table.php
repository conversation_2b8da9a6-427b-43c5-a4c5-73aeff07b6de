<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('social_media_integrations', function (Blueprint $table) {
            $table->unsignedBigInteger('company_id')->after('id');
        });
        Schema::table('social_media_integrations', function (Blueprint $table) {
            $table->dropColumn('user_id');
        });
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('social_media_integrations', function (Blueprint $table) {
            $table->dropColumn('company_id');
        
            $table->unsignedBigInteger('user_id')->after('id');
        });
        
    }
};
