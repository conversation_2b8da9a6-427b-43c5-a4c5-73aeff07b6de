<?php

namespace App\Services;

use Carbon\Carbon;
use App\Custom\Facebook\Facebook;

class InstagramService
{
    protected $integration;
    protected $api;

    public function __construct($integration)
    {
        $this->integration = $integration;

        $this->api = new Facebook([
            'app_id' => config('services.meta.app_id'),
            'app_secret' => config('services.meta.app_secret'),
            'default_graph_version' => config('services.meta.default_graph_version'),
        ]);

        
    }

    /**
     * Fetch Instagram Account Insights
     */
    public function fetchInstagramInsights()
    {
        $companyId = auth()->user()->getCompany()->id;

        if (!isset($this->integration->access_token)) {
            return [];
        }

        $cacheKey = "instagram_insights_{$companyId}";

        $integration = $this->integration;
        $insights = [];

        $configuration = json_decode($integration->data, true);
        $instagramAccountId = $configuration['instagram']['id'] ?? null;
        $pageAccessToken = $configuration['page']['access_token'] ?? null;

        if ($instagramAccountId && $pageAccessToken) {

            $metricsArray = [
                [
                    'metrics' => config('services.meta.instagram.insight_metrics.daily'),
                    'period' => 'day',
                    'group' => false,
                ],
                [
                    'metrics' => config('services.meta.instagram.insight_metrics.daily_grouped'),
                    'period' => 'day',
                    'group' => true,
                ],
                [
                    'metrics' => config('services.meta.instagram.insight_metrics.lifetime'),
                    'period' => 'lifetime',
                    'group' => false,
                ],
                [
                    'metrics' => config('services.meta.instagram.insight_metrics.lifetime_grouped'),
                    'period' => 'lifetime',
                    'group' => true,
                ],
            ];

            $data = [];

            foreach ($metricsArray as $metrics) {

                $key = $metrics['period'].($metrics['group'] ? "_grouped" : '');
                $data[$key] = [];

                if ($metrics['metrics']) {
                    $data[$key] = $this->getInsights($instagramAccountId, $pageAccessToken, $metrics['metrics'], $metrics['period'], $metrics['group']);
                }

            }
        }

        return $data;
    }

    private function getInsights($instagramAccountId, $pageAccessToken, $metrics, $period, $addMetricType = false)
    {
        $metricsUrl = "/$instagramAccountId/insights?period=$period&metric=" . $metrics;

        if ($addMetricType) {
            $metricsUrl .= "&metric_type=total_value";
        }

        $metricsResponse = $this->api->get($metricsUrl, $pageAccessToken);
        $metricsResponseData = $metricsResponse->getDecodedBody()['data'] ?? [];

        return $metricsResponseData;
    }

    /**
     * Fetch Instagram Ad Insights
     */
    public function fetchInstagramAdInsights()
    {
        //return [];
        if (!isset($this->integration->access_token)) {
            return [];    
        }

        $data = json_decode($this->integration->data, true);
        $adAccountId = $data['ad_account']['id'] ?? null;
        $insights = [];

        if ($adAccountId) {
            $fields = config('services.meta.instagram.insight_ad_metrics');
            // Get list of ads for the Instagram Ad Account
            $response = $this->api->get("/{$adAccountId}/insights?level=ad&breakdowns=publisher_platform&fields={$fields}", $this->integration->access_token);
            $ads = $response->getDecodedBody();
            $collection = collect($ads['data'] ?? []);

            $insights = $collection
                ->where('adset_name', 'Instagram Post')
                ->filter(function($ad) {
                    return str_contains($ad['publisher_platform'] ?? '', 'instagram');
                })
                ->first()
            ;
        }

        $actions = collect($insights['actions'] ?? []);
        $pageEngagement = $actions->where('action_type', 'page_engagement')->first()['value'] ?? 0;
        $postEngagement = $actions->where('action_type', 'post_engagement')->first()['value'] ?? 0;
        if (isset($insights['impressions'])) {
            $insights['oganicEngagementRate'] = (($pageEngagement + $postEngagement) / $insights['impressions']) * 100;
        }

        return $insights;
    }
}
