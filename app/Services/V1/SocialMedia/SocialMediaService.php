<?php

namespace App\Services\V1\SocialMedia;

use App\Contracts\Services\SocialMediaServiceInterface;
use App\Contracts\SocialMedia\SocialMediaAdapterInterface;
use App\Enums\SocialPlatformType;
use App\Events\CreateSocialMediaAccountEvent;
use App\Models\User;
use App\Models\UserSocialAccount;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class SocialMediaService implements SocialMediaServiceInterface
{
    protected array $adapters = [];

    protected ?string $defaultPlatform = null;

    public function __construct()
    {
        $this->defaultPlatform = Config::get('socialmedia.default');
    }

    public function registerAdapter(string $platform, SocialMediaAdapterInterface $adapter): self
    {
        $this->adapters[$platform] = $adapter;

        return $this;
    }

    public function getAdapter(?string $platform = null): ?SocialMediaAdapterInterface
    {
        $platform = $platform ?? $this->defaultPlatform;

        if (! $platform || ! isset($this->adapters[$platform])) {
            Log::error('Social media adapter not found', [
                'platform' => $platform ?? 'null',
                'available_adapters' => array_keys($this->adapters),
            ]);

            return null;
        }

        return $this->adapters[$platform];
    }

    public function generateToken(User $user, ?string $platform = null): ?string
    {
        $platform = $platform ?? $this->defaultPlatform;
        $adapter = $this->getAdapter($platform);

        if (! $adapter) {
            return null;
        }

        $userSocialAccount = $user->socialAccounts()
            ->where('platform', $platform)
            ->first();

        if (! $userSocialAccount || ! $userSocialAccount->platform_user_id) {
            Log::error("Cannot generate token - no {$platform} integration found", [
                'user_id' => $user->id,
                'platform' => $platform,
            ]);

            Event::dispatch(new CreateSocialMediaAccountEvent($user, $platform));

            return null;
        }

        $token = $adapter->generateToken($userSocialAccount->platform_user_id);

        if ($token) {
            $userSocialAccount->access_token = $token;
            $userSocialAccount->token_expires_at = now()->addHours(12);
            $userSocialAccount->save();

            return $token;
        }

        return null;
    }

    public function createUserSocialAccount(
        User $user,
        SocialPlatformType $platform,
        array $accountData
    ): ?UserSocialAccount {
        try {

            $adapter = $this->getAdapter($platform->value);

            $userData = $adapter->createUser([
                'email' => $user->email,
                'firstName' => $user->first_name,
                'lastName' => $user->last_name,
            ]);

            if (isset($userData['user_id'])) {
                $accountData['platform_user_id'] = $userData['user_id'];

                $userSocialAccounts = userSocialAccountService()->linkAccount($user, $platform->value, $accountData);
            }

            return $userSocialAccounts;
        } catch (\Exception $e) {
            Log::error('Failed to create/update social media account', [
                'user_id' => $user->id,
                'platform' => $platform->value,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    public function getSocialUrl(?string $token = null): ?string
    {
        $platform = $platform ?? $this->defaultPlatform;
        $adapter = $this->getAdapter($platform);

        return $adapter->getSocialUrl($token);
    }
}
