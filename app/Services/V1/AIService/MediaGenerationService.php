<?php

namespace App\Services\V1\AIService;

use App\Contracts\Services\MediaGenerationServiceInterface;
use App\Drivers\AIDrivers\PiAPIDriver;
use App\Enums\AIProvider;
use Exception;

class MediaGenerationService implements MediaGenerationServiceInterface
{
    protected AIServiceFactory $factory;

    protected ?PiAPIDriver $piApiDriver = null;

    public function __construct(AIServiceFactory $factory)
    {
        $this->factory = $factory;
    }

    /**
     * Get PiAPI driver instance
     */
    protected function getPiApiDriver(): PiAPIDriver
    {
        if (! $this->piApiDriver) {
            $driver = $this->factory->make(AIProvider::PiAPI->value);

            if (! $driver instanceof PiAPIDriver) {
                throw new Exception('PiAPI driver not properly configured');
            }

            $this->piApiDriver = $driver;
        }

        return $this->piApiDriver;
    }

    /**
     * Generate image using various models
     */
    public function generateImage(array $params): array
    {
        $driver = $this->getPiApiDriver();

        return $driver->generateImage($params);
    }

    /**
     * Generate video using various models
     */
    public function generateVideo(array $params): array
    {
        $driver = $this->getPiApiDriver();

        return $driver->generateVideo($params);
    }

    /**
     * Get task status and result
     */
    public function getTaskStatus(string $taskId): array
    {
        $driver = $this->getPiApiDriver();

        return $driver->getTask($taskId);
    }

    /**
     * Cancel a running task
     */
    public function cancelTask(string $taskId): bool
    {
        $driver = $this->getPiApiDriver();

        return $driver->cancelTask($taskId);
    }

    /**
     * Generate image with Midjourney
     */
    public function generateMidjourneyImage(string $prompt, array $options = []): array
    {
        $params = array_merge([
            'model' => 'midjourney',
            'prompt' => $prompt,
        ], $options);

        return $this->generateImage($params);
    }

    /**
     * Generate image with Flux
     */
    public function generateFluxImage(string $prompt, array $options = []): array
    {
        $params = array_merge([
            'model' => 'flux',
            'prompt' => $prompt,
        ], $options);

        return $this->generateImage($params);
    }

    /**
     * Generate video with Kling
     */
    public function generateKlingVideo(string $prompt, array $options = []): array
    {
        $params = array_merge([
            'model' => 'kling',
            'prompt' => $prompt,
        ], $options);

        return $this->generateVideo($params);
    }

    /**
     * Generate video with Luma Dream Machine
     */
    public function generateLumaVideo(string $prompt, array $options = []): array
    {
        $params = array_merge([
            'model' => 'luma',
            'prompt' => $prompt,
        ], $options);

        return $this->generateVideo($params);
    }

    /**
     * Generate video with Hailuo
     */
    public function generateHailuoVideo(string $prompt, array $options = []): array
    {
        $params = array_merge([
            'model' => 'hailuo',
            'prompt' => $prompt,
        ], $options);

        return $this->generateVideo($params);
    }

    /**
     * Get available image models
     */
    public function getAvailableImageModels(): array
    {
        return [
            'midjourney' => [
                'name' => 'Midjourney',
                'description' => 'High-quality artistic image generation',
                'supports' => ['text_to_image', 'variations', 'upscaling'],
            ],
            'flux' => [
                'name' => 'Flux',
                'description' => 'Fast and flexible image generation',
                'supports' => ['text_to_image', 'image_to_image', 'inpainting'],
            ],
            'wanx' => [
                'name' => 'WanX',
                'description' => 'Advanced image generation with LoRA support',
                'supports' => ['text_to_image', 'lora', 'controlnet'],
            ],
        ];
    }

    /**
     * Get available video models
     */
    public function getAvailableVideoModels(): array
    {
        return [
            'kling' => [
                'name' => 'Kling',
                'description' => 'High-quality video generation',
                'supports' => ['text_to_video', 'image_to_video', 'motion_brush'],
            ],
            'luma' => [
                'name' => 'Luma Dream Machine',
                'description' => 'Cinematic video generation',
                'supports' => ['text_to_video', 'image_to_video'],
            ],
            'hailuo' => [
                'name' => 'Hailuo',
                'description' => 'Professional video generation',
                'supports' => ['text_to_video', 'director_mode'],
            ],
            'hunyuan' => [
                'name' => 'Hunyuan Video',
                'description' => 'Advanced video generation',
                'supports' => ['text_to_video'],
            ],
        ];
    }

    /**
     * Validate image generation parameters
     */
    public function validateImageParams(array $params): array
    {
        $errors = [];

        if (empty($params['prompt'])) {
            $errors[] = 'Prompt is required for image generation';
        }

        $validModels = array_keys($this->getAvailableImageModels());
        if (isset($params['model']) && ! in_array($params['model'], $validModels)) {
            $errors[] = 'Invalid image model. Available models: '.implode(', ', $validModels);
        }

        return $errors;
    }

    /**
     * Validate video generation parameters
     */
    public function validateVideoParams(array $params): array
    {
        $errors = [];

        if (empty($params['prompt'])) {
            $errors[] = 'Prompt is required for video generation';
        }

        $validModels = array_keys($this->getAvailableVideoModels());
        if (isset($params['model']) && ! in_array($params['model'], $validModels)) {
            $errors[] = 'Invalid video model. Available models: '.implode(', ', $validModels);
        }

        return $errors;
    }
}
