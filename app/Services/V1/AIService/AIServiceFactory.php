<?php

namespace App\Services\V1\AIService;

use App\Contracts\AIDriver\AIDriverInterface;
use Exception;
use InvalidArgumentException;

class AIServiceFactory
{
    protected static array $instances = [];

    protected array $config;

    public function __construct()
    {
        $this->config = config('ai');
    }

    /**
     * Create or get an AI driver instance
     */
    public function make(?string $provider = null): AIDriverInterface
    {
        $provider = $provider ?? $this->config['default'];

 
        if (isset(static::$instances[$provider])) {
            return static::$instances[$provider];
        }


        if (! isset($this->config['providers'][$provider])) {
            throw new InvalidArgumentException("AI provider '{$provider}' is not configured.");
        }

        $providerConfig = $this->config['providers'][$provider];

  
        if (! isset($providerConfig['driver'])) {
            throw new InvalidArgumentException("Driver class not specified for provider '{$provider}'.");
        }

        $driverClass = $providerConfig['driver'];


        if (! class_exists($driverClass)) {
            throw new InvalidArgumentException("Driver class '{$driverClass}' does not exist.");
        }


        if (! in_array(AIDriverInterface::class, class_implements($driverClass))) {
            throw new InvalidArgumentException("Driver class '{$driverClass}' must implement AIDriverInterface.");
        }


        $config = array_merge($this->config['defaults'] ?? [], $providerConfig);

        try {

            $driver = new $driverClass($config);

            static::$instances[$provider] = $driver;

            return $driver;
        } catch (Exception $e) {
            throw new Exception("Failed to create AI driver for provider '{$provider}': ".$e->getMessage());
        }
    }

    /**
     * Get available providers
     */
    public function getAvailableProviders(): array
    {
        return array_keys($this->config['providers']);
    }

    /**
     * Get default provider
     */
    public function getDefaultProvider(): string
    {
        return $this->config['default'];
    }

    /**
     * Check if a provider is available
     */
    public function hasProvider(string $provider): bool
    {
        return isset($this->config['providers'][$provider]);
    }

    /**
     * Clear cached instances (useful for testing)
     */
    public function clearInstances(): void
    {
        static::$instances = [];
    }

    /**
     * Get provider configuration
     */
    public function getProviderConfig(string $provider): array
    {
        if (! $this->hasProvider($provider)) {
            throw new InvalidArgumentException("Provider '{$provider}' is not configured.");
        }

        return $this->config['providers'][$provider];
    }
}
