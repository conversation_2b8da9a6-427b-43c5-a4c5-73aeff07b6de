<?php

namespace App\Services\V1\UserSocial;

use App\Contracts\Services\UserSocialAccountServiceInterface;
use App\Models\User;
use App\Models\UserSocialAccount;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class UserSocialAccountService implements UserSocialAccountServiceInterface
{
    public function linkAccount(User $user, string $platform, array $accountData): UserSocialAccount
    {
        try {

            $existingAccount = $user->socialAccounts()
                ->where('platform', $platform)
                ->where('platform_user_id', $accountData['platform_user_id'] ?? null)
                ->first();

            if ($existingAccount) {
                $existingAccount->fill(array_merge(
                    $accountData,
                    ['is_active' => true]
                ));
                $existingAccount->save();

                return $existingAccount;
            }

            $account = new UserSocialAccount(array_merge(
                $accountData,
                [
                    'user_id' => $user->id,
                    'platform' => $platform,
                    'is_active' => true,
                ]
            ));

            $account->save();

            return $account;
        } catch (\Exception $e) {
            Log::error('Failed to link social account', [
                'user_id' => $user->id,
                'platform' => $platform,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    public function unlinkAccount(User $user, string $platform, ?string $platformUserId = null): bool
    {
        try {
            $query = $user->socialAccounts()
                ->where('platform', $platform);

            if ($platformUserId) {
                $query->where('platform_user_id', $platformUserId);
            }

            $accounts = $query->get();

            if ($accounts->isEmpty()) {
                return false;
            }

            foreach ($accounts as $account) {
                $account->is_active = false;
                $account->save();
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to unlink social account', [
                'user_id' => $user->id,
                'platform' => $platform,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    public function updateAccountData(UserSocialAccount $account, array $data): bool
    {
        try {
            if (isset($data['profile_data'])) {
                $account->profile_data = $data['profile_data'];
            }

            if (isset($data['account_stats'])) {
                $account->account_stats = $data['account_stats'];
            }

            if (isset($data['username'])) {
                $account->username = $data['username'];
            }

            $account->save();

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update social account data', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    public function getUsersByPlatform(string $platform): Collection
    {
        $accountsWithUsers = UserSocialAccount::where('platform', $platform)
            ->where('is_active', true)
            ->with('user')
            ->get();

        return $accountsWithUsers->map(function ($account) {
            return $account->user;
        })->unique('id');
    }
}
