<?php

namespace App\Services;

use Carbon\Carbon;
use App\Custom\Facebook\Facebook;

class FacebookService
{
    protected $integration;
    protected $api;

    public function __construct($integration)
    {
        $this->integration = $integration;

        $this->api = new Facebook([
            'app_id' => config('services.meta.app_id'),
            'app_secret' => config('services.meta.app_secret'),
            'default_graph_version' => config('services.meta.default_graph_version'),
        ]);
    }

    public function fetchFacebookPageInsights()
    {
        $companyId = auth()->user()->getCompany()->id;

        if (!isset($this->integration->access_token)) {
            return [];    
        }

        // Check and use cache
        $cacheKey = "facebook_insights_{$companyId}";

        //return Cache::remember($cacheKey, now()->addMinutes(1), function () use ($companyId) {
            $integration = $this->integration;
            $insights = [];


            $configuration = json_decode($integration->data, true);

            $pageId = $configuration['page']['id'] ?? null;
            $pageAccessToken = $configuration['page']['access_token'] ?? null;

            if ($pageId && $pageAccessToken) {

                
                $metrics = config('services.meta.facebook.insight_metrics');
                $nextPageUrl = "/$pageId/insights?metric=" . $metrics;
                    $nextPageUrl = str_replace("https://graph.facebook.com/v21.0", "", $nextPageUrl);
                    
                    // Paginate through all pages for the current metric
                    $response = $this->api->get($nextPageUrl, $pageAccessToken);
                    $data = $response->getDecodedBody();
                    
                    $insights = collect($data['data']);
                    $insights = $insights->groupBy('name')->toArray();

                    //POST PER WEEK
                    $postsPerWeekRequest = "/$pageId/posts";
                    $postsPerWeekRequest .= "?since=" . now()->startOfWeek()->format('Y-m-d') . "&until=" . now()->endOfWeek()->format('Y-m-d');
                        
                    // Paginate through all pages for the current metric
                    $postsPerWeekResponse = $this->api->get($postsPerWeekRequest, $pageAccessToken);
                    $postsPerWeek = $postsPerWeekResponse->getDecodedBody()['data'] ?? [];

                    $insights = array_merge($insights, ['posts_per_week' => $postsPerWeek]);
            }

            return $insights;
        //});
    }

    public function fetchFacebookAdInsights()
    {
        if (!isset($this->integration->access_token)) {
            return [];    
        }

        $data = json_decode($this->integration->data, true);
        $adAccountId = $data['ad_account']['id'] ?? null;
        $insights = [];
        
        if ($adAccountId) {
            $fields =  config('services.meta.facebook.insight_ad_metrics');

            $response = $this->api->get("/{$adAccountId}/insights?level=ad&breakdowns=publisher_platform&fields={$fields}", $this->integration->access_token);
            $ads = $response->getDecodedBody();
            $collection = collect($ads['data'] ?? []);
            $insights = $collection
                ->where('adset_name', 'Facebook Post')
                ->filter(function($ad) {
                    return str_contains($ad['publisher_platform'] ?? '', 'facebook');
                })
                ->first()
            ;
        }
        
        $actions = collect($insights['actions'] ?? []);
        $pageEngagement = $actions->where('action_type', 'page_engagement')->first()['value'] ?? 0;
        $postEngagement = $actions->where('action_type', 'post_engagement')->first()['value'] ?? 0;
        if (isset($insights['impressions'])) {
            $insights['oganicEngagementRate'] = (($pageEngagement + $postEngagement) / $insights['impressions']) * 100;
        }

        return $insights;
    }

}
