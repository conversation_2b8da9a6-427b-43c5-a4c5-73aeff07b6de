<?php

namespace App\Services;

use Google\Analytics\Data\V1beta\BetaAnalyticsDataClient;
use Google\Analytics\Data\V1beta\RunReportRequest;
use Google\Analytics\Data\V1beta\DateRange;
use Google\Analytics\Data\V1beta\Metric;
use Google\Analytics\Data\V1beta\Dimension;

class GoogleAnalyticsService
{
    protected $client;
    protected $propertyId;

    public function __construct()
    {
        $this->client = new BetaAnalyticsDataClient([
            'credentials' => storage_path(config('services.google_analytics.credentials')),
        ]);
        $this->propertyId = config('services.google_analytics.property_id');
    }

    public function getReport($startDate, $endDate)
    {
        $response = $this->client->runReport([
            'property' => 'properties/' . $this->propertyId,
            'dateRanges' => [
                new DateRange([
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                ])
            ],
            'dimensions' => [
                new Dimension(['name' => 'pageTitle']),
                new Dimension(['name' => 'pagePath'])
            ],
            'metrics' => [
                new Metric(['name' => 'activeUsers']),
                new Metric(['name' => 'sessions']),
                new Metric(['name' => 'keyEvents']),
                new Metric(['name' => 'newUsers']),
                new Metric(['name' => 'engagedSessions']),
                new Metric(['name' => 'averageSessionDuration']),
                new Metric(['name' => 'bounceRate']),
                new Metric(['name' => 'engagementRate']),
                new Metric(['name' => 'sessionsPerUser']),
                new Metric(['name' => 'scrolledUsers']),
            ],
        ]);

        $data = [];
        foreach ($response->getRows() as $row) {
            $dimensions = $row->getDimensionValues();
            $metrics = $row->getMetricValues();
            $data[] = [
                'pageTitle' => $dimensions[0]->getValue(),
                'pagePath' => $dimensions[1]->getValue(),
                'activeUsers' => $metrics[0]->getValue(),
                'sessions' => $metrics[1]->getValue(),
                'keyEvents' => $metrics[2]->getValue(),
                'newUsers' => $metrics[3]->getValue(),
                'engagedSessions' => $metrics[4]->getValue(),
                'averageSessionDuration' => $metrics[5]->getValue(),
                'bounceRate' => $metrics[6]->getValue(),
                'engagementRate' => $metrics[7]->getValue(),
                'sessionsPerUser' => $metrics[8]->getValue(),
                'scrolledUsers' => $metrics[9]->getValue(),
            ];
        }

        return $data;
    }
}
