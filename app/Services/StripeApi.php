<?php

namespace App\Services;

use Carbon\Carbon;
use Stripe;

class StripeApi
{
	protected $api;

	public function __construct()
	{
        $this->api = new Stripe\StripeClient(config('services.stripe.secret'));
        Stripe\Stripe::setApiKey(config('services.stripe.secret'));
	}

    public function instance()
    {
        return $this->api;
    }

	public function findCustomerById($stripeCustomerId)
	{
		return $this->api->customers->retrieve($stripeCustomerId, []);
	}

	public function findCustomerByEmail($email)
	{
		$customers = $this->api->customers->all(['email' => $email]);

		foreach ($customers as $customer) {
			if ($customer->email == $email) {
				return $customer;
			}
		}

		return null;
	}

	public function getCustomerSubscriptions($stripeCustomerId)
	{
		return $this->api->subscriptions->all(['customer' => $stripeCustomerId]);
	}
	
	public function getSubscription($subscriptionId)
	{
		return $this->api->subscriptions->retrieve($subscriptionId, []);
	}

	public function getCustomerActiveSubscription($customerId)
	{
		$subscriptions = $this->getCustomerSubscriptions($customerId);
		$subscription = null;
		foreach ($subscriptions as $sub) {
			if ($sub->status == 'active' || $sub->status == 'trialing') {
				$subscription = $sub;
			}
		}

		return $subscription;
	}

    public function hasActiveSubscription($stripeCustomerId = null, $email = null)
    {
        if ($stripeCustomerId) {
            $customer = $this->findCustomerById($stripeCustomerId);
        }

        if (!$stripeCustomerId && $email) {
            $customer = $this->findCustomerByEmail($email);

			if ($customer) {
				\App\Models\User::where('email', $email)->update(['stripe_id' => $customer->id]);
			}
        }

        if ($customer) {
            $activeSubscription = $this->getCustomerActiveSubscription($customer->id);
            if ($activeSubscription) {
                return true;
            }
        }

        return false;
    }

	public function cancelSubscriptionById($subscriptionId)
	{
		return $this->api->subscriptions->cancel(
			$subscriptionId,
			[]
		);
	}

	public function getProductByPriceId($productId, $priceId = null)
	{
		$price = $this->api->prices->retrieve($priceId, []);

		if (!$price) {
			throw new \Exception("Invalid product parameters", 1);
		}

		if ($price && $price->product != $productId) {
			throw new \Exception("Invalid product parameters", 1);
		}

		$product = $this->api->products->retrieve($productId, []);

		return [
			'product' => $product,
			'price' => $price,
		];
	}

	public function createPaymentIntent($customerId, $amount, $metadata = [])
	{
		$intent = Stripe\PaymentIntent::create([
	        'amount' => ($amount)*100,
	        'currency' => 'GBP',
	        'metadata' => $metadata,
	        'customer' => $customerId,
	    ]);

	    return $intent;
	}

	public function createSetupIntent($customerId, $metadata = [])
	{
		$intent = $this->api->setupIntents->create([
			'customer' => $customerId,
	        'metadata' => $metadata,
	        'automatic_payment_methods' => [
                'enabled' => true
            ],
		]);

	    return $intent;
	}

	public function createCheckoutSession($priceId, $data = [], $mode = 'subscription')
	{
		$data = [
			'success_url' => config('app.url') . "/?stripe=success",
			'line_items' => [
				[
					'price' => $priceId,
					'quantity' => 1,
				],
			],
			'mode' => $mode,
			'customer_email' => $data['email'] ?? null,
			'customer_details' => [
				'name' => ($data['first_name'] ?? '') . " " . ($data['last_name'] ?? ''),
				'email' => $data['email'] ?? null,
			],
		];

		$session = $this->api->checkout->sessions->create($data);

		return $session;
	}

	public function getPaymentIntent($intentId)
	{
		return $this->api->paymentIntents->retrieve($intentId, []);
	}

	public function getPrice($priceId)
	{
		$price = $this->api->prices->retrieve($priceId, []);

		if (!$price) {
			throw new \Exception("Invalid product parameters", 1);
		}

		return $price;
	}

	public function getCustomer($customerId = null, $data = [])
	{
		if ($customerId) {
			return $this->api->customers->retrieve($customerId, []);
		}

		if (!$customerId && isset($data['email'])) {
			$customer = $this->api->customers->all(['limit' => 1, 'email' => $data['email']])->data[0] ?? null;

			if ($customer) {
				return $customer;
			}
		}

		$customer = $this->api->customers->create([
			'email' => $data['email'],
			'name' => $data['name'],
		]);

		return $customer;
	}

	public function getBillingPortal($customerId, $redirectUrl = null)
	{
		$billing = $this->api->billingPortal->sessions->create([
        	'customer' => $customerId,
        	'return_url' => $redirectUrl ?: config('app.url').'/admin/customer',
        ]);

        return $billing;
	}

	public function getSubscriptionPortal($customerId, $redirectUrl = null)
	{
		$activeSub = $this->getCustomerActiveSubscription($customerId);

		if ($activeSub->status == "trialing") {
			$time = Carbon::now()->format("Y-m-d H:i:s");
			$key = encrypt("$customerId:$activeSub->id:$time");
			return route('api.cancel-trial-subscription', ['key' => $key]);
		}

		$billing = $this->api->billingPortal->sessions->create([
        	'customer' => $customerId,
        	'return_url' => $redirectUrl ?: config('app.url').'/admin/customer',
        	'flow_data' => [
        		'type' => 'subscription_cancel',
        		'subscription_cancel' => ['subscription' => $activeSub->id]
        	],
        ]);

        return $billing;
	}

    public function products()
    {
        return $this->api->products->all([]);
    }

}
