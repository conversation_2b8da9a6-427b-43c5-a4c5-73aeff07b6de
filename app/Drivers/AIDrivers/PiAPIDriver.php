<?php

namespace App\Drivers\AIDrivers;

use App\Contracts\AIDriver\AIDriverInterface;
use Closure;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PiAPIDriver implements AIDriverInterface
{
    protected array $config;

    protected string $baseUrl = 'https://api.piapi.ai';

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->validateConfig();
    }

    /**
     * Prepare messages for PiAPI (simple formatting/validation)
     */
    public function prepareMessages(array $messages): array
    {
        foreach ($messages as $message) {
            if (! isset($message['role']) || ! isset($message['content'])) {
                throw new Exception('Each message must have "role" and "content" fields');
            }

            if (! in_array($message['role'], ['system', 'user', 'assistant'])) {
                throw new Exception('Message role must be one of: system, user, assistant');
            }
        }

        return $messages;
    }

    /**
     * Stream response from PiAPI (not typically used for image/video generation)
     */
    public function streamResponse(array $messages, Closure $callback): void
    {
        throw new Exception('PiAPI does not support streaming for image/video generation tasks');
    }

    /**
     * Get non-streaming response from PiAPI (for LLM tasks)
     */
    public function getNonStreamResponse(array $messages): array
    {
        try {
            $response = Http::withHeaders([
                'X-API-Key' => $this->config['api_key'],
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl.'/api/v1/task', [
                'model' => $this->config['llm_model'] ?? 'gpt-4o',
                'task_type' => 'completion',
                'input' => [
                    'messages' => $messages,
                ],
            ]);

            if (! $response->successful()) {
                throw new Exception('PiAPI request failed: '.$response->body());
            }

            $data = $response->json();

            Log::info('PiAPI LLM response structure', ['response' => $data]);

            $taskId = null;
            if (isset($data['task_id'])) {
                $taskId = $data['task_id'];
            } elseif (isset($data['id'])) {
                $taskId = $data['id'];
            } elseif (isset($data['data']['task_id'])) {
                $taskId = $data['data']['task_id'];
            } elseif (isset($data['data']['id'])) {
                $taskId = $data['data']['id'];
            } else {
                throw new Exception('No task ID found in response: '.json_encode($data));
            }

            $result = $this->waitForTaskCompletion($taskId);

            return [
                'role' => 'assistant',
                'content' => $result['output']['choices'][0]['message']['content'] ?? 'No response generated',
            ];

        } catch (Exception $e) {
            Log::error('PiAPI LLM request failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Generate image using various PiAPI models
     */
    public function generateImage(array $params): array
    {
        $model = $params['model'] ?? 'midjourney';
        $prompt = $params['prompt'] ?? '';

        if (empty($prompt)) {
            throw new Exception('Prompt is required for image generation');
        }

        try {
            $taskData = $this->prepareImageTaskData($model, $params);

            $response = Http::withHeaders([
                'X-API-Key' => $this->config['api_key'],
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl.'/api/v1/task', $taskData);

            if (! $response->successful()) {
                throw new Exception('PiAPI image generation request failed: '.$response->body());
            }

            $data = $response->json();

            Log::info('PiAPI response structure', ['response' => $data]);

            $taskId = null;
            if (isset($data['task_id'])) {
                $taskId = $data['task_id'];
            } elseif (isset($data['id'])) {
                $taskId = $data['id'];
            } elseif (isset($data['data']['task_id'])) {
                $taskId = $data['data']['task_id'];
            } elseif (isset($data['data']['id'])) {
                $taskId = $data['data']['id'];
            } else {
                throw new Exception('No task ID found in response: '.json_encode($data));
            }

            if ($params['async'] ?? false) {
                return [
                    'task_id' => $taskId,
                    'status' => 'processing',
                    'model' => $model,
                ];
            }

            $result = $this->waitForTaskCompletion($taskId);

            return [
                'task_id' => $taskId,
                'status' => 'completed',
                'model' => $model,
                'images' => $this->extractImageUrls($result['output']),
                'prompt' => $prompt,
            ];

        } catch (Exception $e) {
            Log::error('PiAPI image generation failed', [
                'model' => $model,
                'prompt' => $prompt,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Generate video using various PiAPI models
     */
    public function generateVideo(array $params): array
    {
        $model = $params['model'] ?? 'kling';
        $prompt = $params['prompt'] ?? '';

        if (empty($prompt)) {
            throw new Exception('Prompt is required for video generation');
        }

        try {
            $taskData = $this->prepareVideoTaskData($model, $params);

            $response = Http::withHeaders([
                'X-API-Key' => $this->config['api_key'],
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl.'/api/v1/task', $taskData);

            if (! $response->successful()) {
                throw new Exception('PiAPI video generation request failed: '.$response->body());
            }

            $data = $response->json();

            Log::info('PiAPI video response structure', ['response' => $data]);

            $taskId = null;
            if (isset($data['task_id'])) {
                $taskId = $data['task_id'];
            } elseif (isset($data['id'])) {
                $taskId = $data['id'];
            } elseif (isset($data['data']['task_id'])) {
                $taskId = $data['data']['task_id'];
            } elseif (isset($data['data']['id'])) {
                $taskId = $data['data']['id'];
            } else {
                throw new Exception('No task ID found in response: '.json_encode($data));
            }

            if ($params['async'] ?? false) {
                return [
                    'task_id' => $taskId,
                    'status' => 'processing',
                    'model' => $model,
                ];
            }

            $result = $this->waitForTaskCompletion($taskId);

            return [
                'task_id' => $taskId,
                'status' => 'completed',
                'model' => $model,
                'videos' => $result['output']['video_urls'] ?? $result['output']['video_url'] ?? [],
                'prompt' => $prompt,
            ];

        } catch (Exception $e) {
            Log::error('PiAPI video generation failed', [
                'model' => $model,
                'prompt' => $prompt,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Get task status and result
     */
    public function getTask(string $taskId): array
    {
        try {
            $response = Http::withHeaders([
                'X-API-Key' => $this->config['api_key'],
            ])->get($this->baseUrl.'/api/v1/task/'.$taskId);

            if (! $response->successful()) {
                throw new Exception('Failed to get task status: '.$response->body());
            }

            $data = $response->json();

            Log::info('PiAPI task status response', ['task_id' => $taskId, 'response' => $data]);

            return $data['data'] ?? $data;

        } catch (Exception $e) {
            Log::error('PiAPI get task failed', ['task_id' => $taskId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Cancel a task
     */
    public function cancelTask(string $taskId): bool
    {
        try {
            $response = Http::withHeaders([
                'X-API-Key' => $this->config['api_key'],
            ])->delete($this->baseUrl.'/api/v1/task/'.$taskId);

            return $response->successful();

        } catch (Exception $e) {
            Log::error('PiAPI cancel task failed', ['task_id' => $taskId, 'error' => $e->getMessage()]);

            return false;
        }
    }

    /**
     * Get the provider name
     */
    public function getProviderName(): string
    {
        return 'piapi';
    }

    /**
     * Check if the driver supports streaming
     */
    public function supportsStreaming(): bool
    {
        return false;
    }

    /**
     * Validate the configuration
     */
    public function validateConfig(): bool
    {
        $requiredKeys = ['api_key'];

        foreach ($requiredKeys as $key) {
            if (! isset($this->config[$key])) {
                throw new Exception("Missing required configuration key: {$key}");
            }
        }

        if (empty($this->config['api_key'])) {
            throw new Exception('PiAPI API key is required');
        }

        return true;
    }

    /**
     * Wait for task completion with polling
     */
    protected function waitForTaskCompletion(string $taskId, int $maxWaitTime = 300): array
    {
        $startTime = time();
        $pollInterval = 5; // Increase interval to 5 seconds for better API usage

        while (time() - $startTime < $maxWaitTime) {
            $taskStatus = $this->getTask($taskId);

            Log::info('PiAPI task polling', [
                'task_id' => $taskId,
                'status' => $taskStatus['status'] ?? 'unknown',
                'progress' => $taskStatus['output']['progress'] ?? 0,
            ]);

            if ($taskStatus['status'] === 'completed') {
                return $taskStatus;
            }

            if ($taskStatus['status'] === 'failed') {
                $errorMessage = $taskStatus['error']['message'] ?? 'Unknown error';
                throw new Exception('Task failed: '.$errorMessage);
            }

            sleep($pollInterval);
        }

        throw new Exception('Task timeout after '.$maxWaitTime.' seconds');
    }

    /**
     * Prepare task data for image generation using unified API schema
     */
    protected function prepareImageTaskData(string $model, array $params): array
    {
        switch ($model) {
            case 'flux':
                return [
                    'model' => 'Qubico/flux1-dev-advanced',
                    'task_type' => 'txt2img-lora',
                    'input' => [
                        'prompt' => $params['prompt'],
                        'steps' => $params['steps'] ?? 28,
                        'guidance_scale' => $params['guidance_scale'] ?? 2.5,
                        'width' => $params['width'] ?? 1024,
                        'height' => $params['height'] ?? 1024,
                        'lora_settings' => [
                            [
                                'lora_type' => $params['lora_type'] ?? 'mjv6',
                                'lora_strength' => $params['lora_strength'] ?? 1.0,
                            ],
                        ],
                    ],
                    'config' => [
                        'webhook_config' => [
                            'endpoint' => '',
                            'secret' => '',
                        ],
                    ],
                ];

            case 'midjourney':
                return [
                    'model' => 'midjourney',
                    'task_type' => 'imagine',
                    'input' => [
                        'prompt' => $params['prompt'],
                        'aspect_ratio' => $params['aspect_ratio'] ?? '1:1',
                        'version' => $params['version'] ?? '6.1',
                    ],
                ];

            default:
                return [
                    'model' => $model,
                    'task_type' => 'txt2img',
                    'input' => [
                        'prompt' => $params['prompt'],
                        'width' => $params['width'] ?? 1024,
                        'height' => $params['height'] ?? 1024,
                    ],
                ];
        }
    }

    /**
     * Prepare task data for video generation using unified API schema
     */
    protected function prepareVideoTaskData(string $model, array $params): array
    {
        switch ($model) {
            case 'kling':
                return [
                    'model' => 'kling',
                    'task_type' => 'video_generation',
                    'input' => [
                        'prompt' => $params['prompt'],
                        'duration' => $params['duration'] ?? 5,
                        'aspect_ratio' => $params['aspect_ratio'] ?? '1:1',
                        'camera_movement' => $params['camera_movement'] ?? 'none',
                    ],
                ];

            case 'luma':
                return [
                    'model' => 'luma',
                    'task_type' => 'video_generation',
                    'input' => [
                        'prompt' => $params['prompt'],
                        'loop' => $params['loop'] ?? false,
                        'aspect_ratio' => $params['aspect_ratio'] ?? '1:1',
                    ],
                ];

            default:
                return [
                    'model' => $model,
                    'task_type' => 'video_generation',
                    'input' => [
                        'prompt' => $params['prompt'],
                        'duration' => $params['duration'] ?? 5,
                        'aspect_ratio' => $params['aspect_ratio'] ?? '1:1',
                    ],
                ];
        }
    }

    /**
     * Extract image URLs from PiAPI response output
     */
    protected function extractImageUrls(array $output): array
    {
        $imageUrls = [];

        if (! empty($output['image_url'])) {
            $imageUrls[] = $output['image_url'];
        }

        if (! empty($output['image_urls']) && is_array($output['image_urls'])) {
            $imageUrls = array_merge($imageUrls, $output['image_urls']);
        }

        if (! empty($output['temporary_image_urls']) && is_array($output['temporary_image_urls'])) {
            $imageUrls = array_merge($imageUrls, $output['temporary_image_urls']);
        }

        return array_values(array_filter(array_unique($imageUrls)));
    }
}
