<?php

namespace App\Drivers\AIDrivers;

use App\Contracts\AIDriver\AIDriverInterface;
use Closure;
use Exception;
use OpenAI;

class OpenAIDriver implements AIDriverInterface
{
    protected $client;

    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->validateConfig();
        $this->client = OpenAI::client($this->config['token']);
    }

    /**
     * Prepare messages for OpenAI (simple formatting/validation)
     */
    public function prepareMessages(array $messages): array
    {
        // Validate message structure for OpenAI API
        foreach ($messages as $message) {
            if (! isset($message['role']) || ! isset($message['content'])) {
                throw new Exception('Each message must have "role" and "content" fields');
            }

            if (! in_array($message['role'], ['system', 'user', 'assistant'])) {
                throw new Exception('Message role must be one of: system, user, assistant');
            }
        }

        return $messages;
    }

    /**
     * Stream response from OpenAI
     */
    public function streamResponse(array $messages, Closure $callback): void
    {
        $stream = $this->client->chat()->createStreamed([
            'model' => $this->config['model'],
            'messages' => $messages,
            'stream' => true,
            'max_tokens' => $this->config['max_tokens'],
            'temperature' => $this->config['temperature'],
        ]);

        foreach ($stream as $response) {
            if (isset($response->choices[0]->delta->content)) {
                $content = $response->choices[0]->delta->content;
                if (! empty($content)) {
                    $callback($content);
                }
            }
        }
    }

    /**
     * Get non-streaming response from OpenAI (fallback)
     */
    public function getNonStreamResponse(array $messages): array
    {
        $response = $this->client->chat()->create([
            'model' => $this->config['model'],
            'messages' => $messages,
            'max_tokens' => $this->config['max_tokens'],
            'temperature' => $this->config['temperature'],
        ]);

        return [
            'role' => $response->choices[0]->message->role,
            'content' => $response->choices[0]->message->content,
        ];
    }

    /**
     * Get the provider name
     */
    public function getProviderName(): string
    {
        return 'openai';
    }

    /**
     * Check if the driver supports streaming
     */
    public function supportsStreaming(): bool
    {
        return true;
    }

    /**
     * Validate the configuration
     */
    public function validateConfig(): bool
    {
        $requiredKeys = ['token', 'model', 'max_tokens', 'temperature'];

        foreach ($requiredKeys as $key) {
            if (! isset($this->config[$key])) {
                throw new Exception("Missing required configuration key: {$key}");
            }
        }

        if (empty($this->config['token'])) {
            throw new Exception('OpenAI API token is required');
        }

        return true;
    }
}
