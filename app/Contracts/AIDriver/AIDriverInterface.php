<?php

namespace App\Contracts\AIDriver;

use Closure;

interface AIDriverInterface
{
    /**
     * Initialize the driver with configuration
     */
    public function __construct(array $config);

    /**
     * Prepare messages for AI provider (simple formatting/validation)
     */
    public function prepareMessages(array $messages): array;

    /**
     * Stream response from AI provider
     */
    public function streamResponse(array $messages, Closure $callback): void;

    /**
     * Get non-streaming response from AI provider (fallback)
     */
    public function getNonStreamResponse(array $messages): array;

    /**
     * Get the provider name
     */
    public function getProviderName(): string;

    /**
     * Check if the driver supports streaming
     */
    public function supportsStreaming(): bool;

    /**
     * Validate the configuration
     */
    public function validateConfig(): bool;
}
