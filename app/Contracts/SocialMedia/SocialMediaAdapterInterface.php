<?php

namespace App\Contracts\SocialMedia;

interface SocialMediaAdapterInterface
{
    /**
     * Create a user account on the platform.
     *
     * @return array|null Response data or null on failure
     */
    public function createUser(array $userData): ?array;

    /**
     * Generate an authentication token for a user.
     *
     * @return string|null Token or null on failure
     */
    public function generateToken(string $userId): ?string;

    /**
     * Check if a user exists on the platform.
     */
    public function userExists(string $userId): bool;

    /**
     * Schedule a post on the platform.
     */
    public function schedulePost(array $postData): bool;

    /**
     * Get analytics for a post.
     */
    public function getAnalytics(string $postId): array;

    /**
     * Get the social media URL for a user.
     */
    public function getSocialUrl(?string $token = null): ?string;
}
