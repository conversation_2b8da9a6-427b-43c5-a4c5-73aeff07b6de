<?php

namespace App\Contracts\Services;

use App\Models\User;
use App\Models\UserSocialAccount;
use Illuminate\Database\Eloquent\Collection;

interface UserSocialAccountServiceInterface
{
    /**
     * Link a social account to a user.
     */
    public function linkAccount(User $user, string $platform, array $accountData): UserSocialAccount;

    /**
     * Unlink a social account from a user.
     */
    public function unlinkAccount(User $user, string $platform, ?string $platformUserId = null): bool;

    /**
     * Get user accounts by platform.
     */
    public function getUsersByPlatform(string $platform): Collection;

    /**
     * Update account stats and profile data.
     */
    public function updateAccountData(UserSocialAccount $account, array $data): bool;
}
