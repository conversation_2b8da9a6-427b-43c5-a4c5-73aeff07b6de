<?php

namespace App\Contracts\Services;

use App\Enums\SocialPlatformType;
use App\Models\User;
use App\Models\UserSocialAccount;

interface SocialMediaServiceInterface
{
    /**
     * Generate a token for a specific platform
     */
    public function generateToken(User $user, ?string $platform = null): ?string;

    /**
     * Create or update a social media account for a user
     */
    public function createUserSocialAccount(
        User $user,
        SocialPlatformType $platform,
        array $accountData
    ): ?UserSocialAccount;

    /**
     * Get the social media URL for a user
     */
    public function getSocialUrl(?string $token = null): ?string;
}
