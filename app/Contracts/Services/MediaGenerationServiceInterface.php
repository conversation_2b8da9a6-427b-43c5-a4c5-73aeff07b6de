<?php

namespace App\Contracts\Services;

interface MediaGenerationServiceInterface
{
    /**
     * Generate image using various models
     */
    public function generateImage(array $params): array;

    /**
     * Generate video using various models
     */
    public function generateVideo(array $params): array;

    /**
     * Get task status and result
     */
    public function getTaskStatus(string $taskId): array;

    /**
     * Cancel a running task
     */
    public function cancelTask(string $taskId): bool;

    /**
     * Generate image with Midjourney
     */
    public function generateMidjourneyImage(string $prompt, array $options = []): array;

    /**
     * Generate image with Flux
     */
    public function generateFluxImage(string $prompt, array $options = []): array;

    /**
     * Generate video with Kling
     */
    public function generateKlingVideo(string $prompt, array $options = []): array;

    /**
     * Generate video with Luma Dream Machine
     */
    public function generateLumaVideo(string $prompt, array $options = []): array;

    /**
     * Generate video with Hailuo
     */
    public function generateHailuoVideo(string $prompt, array $options = []): array;

    /**
     * Get available image models
     */
    public function getAvailableImageModels(): array;

    /**
     * Get available video models
     */
    public function getAvailableVideoModels(): array;

    /**
     * Validate image generation parameters
     */
    public function validateImageParams(array $params): array;

    /**
     * Validate video generation parameters
     */
    public function validateVideoParams(array $params): array;
}
