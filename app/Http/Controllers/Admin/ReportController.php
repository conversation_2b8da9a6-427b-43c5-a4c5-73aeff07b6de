<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

use App\Models\Feedback;
use App\Models\User;
use App\Models\Persona;
use App\Models\Statistics\Login;
use App\Models\Setting;

class ReportController extends Controller
{

    public function index()
    {
        return view('admin.index');
    }

    public function feedbacks()
    {
        $feedbacks = Feedback::all();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="feedbacks.csv"',
        ];

        $columns = ['ID', 'User ID', 'Type', 'Score', 'Message', 'Created At'];

        $callback = function () use ($feedbacks, $columns) {
            $file = fopen('php://output', 'w');

            fputcsv($file, $columns);

            foreach ($feedbacks as $feedback) {
                fputcsv($file, [
                    $feedback->id,
                    $feedback->user_id,
                    $feedback->type,
                    $feedback->score,
                    $feedback->message,
                    $feedback->created_at,
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    public function logins($per = "day")
    {
        if ($per == "day") {
            $logins = Login::selectRaw('DATE(created_at) as date, COUNT(*) as total_logins')
                ->groupBy('date')
                ->orderBy('date', 'asc')
                ->get();
        }
            
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="logins_per_'.$per.'.csv"',
        ];

        $columns = ['Date', 'Total Logins'];

        $callback = function () use ($logins, $columns) {
            $file = fopen('php://output', 'w');

            fputcsv($file, $columns);

            foreach ($logins as $login) {
                fputcsv($file, [
                    $login->date,
                    $login->total_logins,
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    public function signups($per = "day")
    {
        if ($per == "day") {
            $signups = User::selectRaw('DATE(created_at) as date, COUNT(*) as total_signups')
                ->groupBy('date')
                ->orderBy('date', 'asc')
                ->get();
        }
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="signups_per_'.$per.'.csv"',
        ];

        $columns = ['Date', 'Total Signups'];

        $callback = function () use ($signups, $columns) {
            $file = fopen('php://output', 'w');

            fputcsv($file, $columns);

            foreach ($signups as $signup) {
                fputcsv($file, [
                    $signup->date,
                    $signup->total_signups,
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    public function affinitySearches()
    {
        $personas = Persona::all();
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="affinity_searches.csv"',
        ];

        $columns = [
            "Name",
            "General category",
            "Sub category",
            "Frequency weight",
            "Donation weight",
            "Average donation weight",
            "Number of donors weight",
            "Gender",
            "Age",
            "Location",
            "Area of focus",
            "Salary",
            "Created at",
        ];

        $callback = function () use ($personas, $columns) {
            $file = fopen('php://output', 'w');

            fputcsv($file, $columns);

            foreach ($personas as $persona) {
                fputcsv($file, [
                    $persona->name,
                    $persona->general_category,
                    $persona->sub_category,
                    $persona->frequency_weight,
                    $persona->donation_weight,
                    $persona->average_donation_weight,
                    $persona->number_of_donors_weight,
                    $persona->gender,
                    $persona->age,
                    $persona->location,
                    $persona->area_of_focus,
                    $persona->salary,
                    $persona->created_at,
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    public function settings(Request $request)
    {
        Setting::updateOrCreate([
            'key' => $request->key,
        ], [
            'value' => $request->value,
        ]);

        return redirect()->back()->with('success', 'Settings updated');
    }

}
