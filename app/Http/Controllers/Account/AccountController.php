<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

use App\Models\User;
use App\Models\Company;
use App\Models\Plan;

class AccountController extends Controller
{
    public function profiles(Request $request)
    {
        $user = auth()->user();

        $company = $user->company;

        $users = User::where('company_id', $company->id)->where('user_type', User::TYPE_CONSUMER)->get();

        return response()->json([
            'status' => 'success',
            'users' => $users,
        ]);
    }

    public function plans(Request $request)
    {
        $plans = Plan::all();

        return response()->json([
            'plans' => $plans,
        ]);   
    }

    public function refresh(Request $request)
    {
        auth()->user()->refresh();
        return response()->json(['status' => 'okay']);
    }
}
