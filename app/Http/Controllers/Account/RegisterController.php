<?php

namespace App\Http\Controllers\Account;

use App\Events\CreateSocialMediaAccountEvent;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\StripeApi;
use HubSpot\Factory;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Fortify\Fortify;

class RegisterController extends Controller
{
    public function register(Request $request)
    {
        $request->validate([
            'first_name' => ['nullable', 'string'],
            'last_name' => ['nullable', 'string'],
            'email' => ['required', 'unique:users,email'],
            'password' => ['required', 'string'],
            'role_id' => ['nullable', 'in:'.implode(',', User::getRoleIds())],
            'terms' => ['required', 'boolean', 'in:true,1,yes,on'],
            'marketing' => ['nullable', 'boolean'],

        ], [
            'terms' => 'Terms must be accepted',
            'role_id.required' => 'Please select a role.',
            'role_id.in' => 'The selected role is invalid.',
        ]);

        $user = User::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'password' => bcrypt($request->password),
            'role_id' => $request->role_id,
            'terms' => $request->terms,
            'marketing' => $request->marketing ?: false,
            'user_type' => User::TYPE_COMPANY_ADMIN,
        ]);

        /*try {
            $stripe = new StripeApi;

            $sC = $stripe->getCustomer(null, [
                'email' => $user->email,
                'name' => $user->first_name.' '.$user->last_name,
            ]);

            $user->stripe_id = $sC->id;
            $user->save();
        } catch (\Throwable $th) {}*/

        $client = Factory::createWithAccessToken(config('services.hubspot.key'));
        $propertiesApi = $client->crm()->properties()->coreApi();

        // 1. Check if the property exists, create it if not
        try {
            $propertiesApi->getByName('contacts', 'marketing_opt_in');
        } catch (\Exception $e) {
            // Property doesn't exist → create it with boolean options
            try {
                $propertiesApi->create('contacts', [
                    'name' => 'marketing_opt_in',
                    'label' => 'Marketing Opt-In',
                    'type' => 'enumeration', // Must be 'enumeration' for boolean checkbox
                    'fieldType' => 'booleancheckbox',
                    'groupName' => 'contactinformation',
                    'options' => [
                        [
                            'label' => 'Yes',
                            'value' => 'true',
                            'displayOrder' => 1,
                            'hidden' => false,
                        ],
                        [
                            'label' => 'No',
                            'value' => 'false',
                            'displayOrder' => 2,
                            'hidden' => false,
                        ],
                    ],
                ]);
            } catch (\Exception $e) {
                // return;
            }
        }

        // 2. Create the contact with the boolean value
        try {
            $client->crm()->contacts()->basicApi()->create([
                'properties' => [
                    'firstname' => $request->first_name,
                    'lastname' => $request->last_name,
                    'email' => $request->email,
                    'marketing_opt_in' => $request->marketing ? 'true' : 'false', // Must be string 'true'/'false'
                ],
            ]);
        } catch (\Exception $e) {
        }

        event(new CreateSocialMediaAccountEvent($user));

        Fortify::loginThrough(function () use ($user) {
            if ($user) {
                return $user;
            }
        });

        auth()->login($user, true);

        $userInstance = auth()->user();

        request()->session()->put([
            'password_hash' => $userInstance->getAuthPassword(),
            'password_hash_'.auth()->getDefaultDriver() => $userInstance->getAuthPassword(),
        ]);

        return to_route('setup.subscription');

    }
}
