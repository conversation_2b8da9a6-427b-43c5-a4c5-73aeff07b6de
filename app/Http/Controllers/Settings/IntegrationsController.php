<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;

class IntegrationsController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        // Get the related company
        $company = $user->getCompany()?->load('country'); // Load related 'country' if needed

        return Inertia::render('Settings/Integrations', [
            'company' => $company,
        ]);
    }
}
