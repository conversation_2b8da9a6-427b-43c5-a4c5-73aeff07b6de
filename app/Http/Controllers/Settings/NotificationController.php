<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;

use App\Models\User;
use App\Models\UserNotificationPreference;

use HubSpot\Factory;
use HubSpot\Client\Crm\Contacts\ApiException;

class NotificationController extends Controller
{
    public function index()
    {
        return Inertia::render('Settings/Notifications');
    }

    public function update(Request $request)
    {
        $request->validate([
            'marketing' => 'nullable|in:1,true,on,0,false,off',
        ]);

        $data = [
            'marketing' => $request->marketing ? true : false,
        ];

        $user = auth()->user();
        $user->fill($data);
        $user->save();

        if ($data['marketing']) {
            $this->addToHubSpotMailingList();
        } else {
            $this->removeFromHubSpot();
        }

        return back()->with('flash', [
            'message' => 'Your notification preferences have been updated.',
        ]);
    }

    public function addToHubSpotMailingList()
    {
        $client = Factory::createWithAccessToken(config('services.hubspot.key'));
        
        try {
            $client->crm()->contacts()->basicApi()->create([
                'properties' => [
                    'firstname' => auth()->user()->first_name,
                    'lastname' => auth()->user()->last_name,
                    'email' => auth()->user()->email,
                ],
            ]);
        } catch (\Exception $e) {}
    }

    public function removeFromHubSpot()
    {
        $hubSpot = Factory::createWithAccessToken(config('services.hubspot.key'));

        try {
            // Search for contact by email to get the contact ID
            $searchRequest = [
                'filterGroups' => [
                    [
                        'filters' => [
                            [
                                'propertyName' => 'email',
                                'operator' => 'EQ',
                                'value' => auth()->user()->email,
                            ],
                        ],
                    ],
                ],
            ];

            $response = $hubSpot->crm()->contacts()->searchApi()->doSearch($searchRequest);

            if ($response->getResults()) {
                $contactId = $response->getResults()[0]->getId();

                // Delete the contact
                $hubSpot->crm()->contacts()->basicApi()->archive($contactId);
            }

        } catch (ApiException $e) {
            // Handle errors if needed
            \Log::error('Error removing user from HubSpot: ' . $e->getMessage());
        }
    }
}
