<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ProfileController extends Controller
{
    public function index()
    {
        $user = auth()->user();

        $company = $user->getCompany();
        $companyAbout = $company ? $company->about : null;

        return Inertia::render('Settings/Profile', [
            'companyName' => $company ? $company->name : null,
            'companyAbout' => $company ? $company->about : null,
            'companyRegion' => $company ? $company->region : null,
            'companyLanguagePreference' => $company ? $company->language_preference : null,
            'companyTonePreference' => $company ? $company->tone_preference : null,
            'companyGeneralCategory' => $company ? $company->general_category : null,
            'companySubCategory' => $company ? $company->sub_category : null,
        ]);
    }

    public function update(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'phone' => 'nullable|string',
            'email' => 'required|email|unique:users,email,'.auth()->user()->id,
        ]);
        $user = auth()->user();

        $data = $request->toArray();

        $user->fill($data);

        $user->save();

        return back()->with('flash', [
            'message' => 'Your profile details have been updated.',
        ]);
    }

    public function password(Request $request)
    {
        $request->validate([
            'current_password' => 'required_with:new_password|current_password',
            'new_password' => 'nullable|string|min:8',
            'confirm_new_password' => 'required_with:new_password|same:new_password',
        ]);

        $data = $request->toArray();

        if ($request->new_password) {
            $data['password'] = bcrypt($request->new_password);
        }

        $user = auth()->user();

        $user->fill($data);

        $user->save();

        return back()->with('flash', [
            'message' => 'Your password has successfully been changed.',
        ]);
    }

    public function delete(Request $request)
    {
        $user = auth()->user();

        $user->delete();

        Auth::guard()->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return back()->with('flash', [
            'message' => 'Logged out',
        ]);
    }
}
