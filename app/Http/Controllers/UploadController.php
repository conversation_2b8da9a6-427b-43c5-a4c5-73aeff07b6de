<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class UploadController extends Controller
{
    // handle FilePond's process
    public function process(Request $request)
    {
        $request->validate([
            'filepond' => 'required|file|max:2048', // 2MB max size in kilobytes
        ]);

        $uploaded = $request->file('filepond');
        if (! $uploaded) {
            return response()->json(['error' => 'No file uploaded.'], 400);
        }

        $path = $uploaded->store('uploads/assets', config('filesystems.default'));

        return response()->json(['id' => $path]);
    }


    // handle FilePond's revert
    public function revert(Request $request)
    {
        $path = $request->getContent();            // serverId posted as raw body
        Storage::disk(config('filesystems.default'))->delete($path);
        return response('', 200);
    }
}
