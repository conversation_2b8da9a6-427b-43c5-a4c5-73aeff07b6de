<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Campaign;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

use Inertia\Inertia;


class CampaignsController extends Controller
{
    /**
     * Display a listing of the user's campaigns.
     */
    public function index(Request $request)
    {
        if (auth()->check()) {
            auth()->user()->refresh();
        }

        // Fetch saved campaigns for the authenticated user, ordered by latest first
        $saved_campaigns = Campaign::where('user_id', auth()->id())
        ->orderBy('created_at', 'desc')
        ->get();

        return Inertia::render('SavedCampaigns', [
            'saved_campaigns' => $saved_campaigns
        ]);
    }

    /**
     * Display the specified campaign by UUID.
     */
    public function show(Request $request, $uuid)
    {
        // Ensure the user is authenticated and refresh if needed.
        if (auth()->check()) {
            auth()->user()->refresh();
        }

        // Retrieve the specific campaign that belongs to the authenticated user using the UUID.
        $campaign = Campaign::where('user_id', auth()->id())
            ->where('uuid', $uuid)
            ->firstOrFail();

        // Render a dedicated page with the campaign data.
        return Inertia::render('CampaignSingle', [
            'campaign' => $campaign,
            'social_media_channels' => config('affinity.social_media_channels'),
            'social_url' => socialMediaService()->getSocialUrl(socialMediaService()->generateToken(auth()->user())),
            'communication_channels' => config('affinity.communication_channels'),
            'fundraising_campaigns' => config('affinity.fundraising_campaigns'),
        ]);
    }

    /**
     * Store a newly created campaign or update an existing one.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'uuid' => 'nullable|uuid',
            'gender' => 'nullable|string',
            'age' => 'nullable|string',
            'category' => 'nullable|string',
            'subcategory' => 'nullable|string',
            'data' => 'required|array',
        ]);

        // Check if a UUID was provided in the request.
        if (!empty($validated['uuid'])) {
            $result = Campaign::where('uuid', $validated['uuid'])->first();

            if ($result) {
                // Update existing entry
                $result->update([
                    'data' => json_encode($validated['data']),
                    'gender' => $validated['gender'],
                    'age' => $validated['age'],
                    'category' => $validated['category'],
                    'subcategory' => $validated['subcategory'],
                ]);

                return response()->json([
                    'uuid' => $result->uuid,
                    'status' => 1,
                    'message' => 'Updated successfully'
                ], 200);
            }
        }

        // If no UUID was provided or not found, create a new entry.
        $result = Campaign::create([
            'uuid' => Str::uuid(),
            'user_id' => auth()->user()->id,
            'data' => json_encode($validated['data']),
            'gender' => $validated['gender'],
            'age' => $validated['age'],
            'category' => $validated['category'],
            'subcategory' => $validated['subcategory'],
        ]);

        return response()->json([
            'uuid' => $result->uuid,
            'status' => 1,
            'message' => 'Created successfully'
        ], 201);
    }

}
