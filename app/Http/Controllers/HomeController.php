<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        //$this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        return view('home');
    }

    public function contact_us(Request $request)
    {
        $request->validate([
            'email' => 'required',
            'subject' => 'required|string',
            'message' => 'required|string',
            'g-recaptcha-response' => 'required|recaptcha'
        ]);

        $mail = \Mail::to(env('CONTACT_MAIL_TO_ADDRESS',  env('MAIL_FROM_ADDRESS', '<EMAIL>')))->send(new \App\Mail\ContactUsMail($request->toArray()));
        return back()->with('flash', [
            'message' => "Message sent",
        ]);
    }
}
