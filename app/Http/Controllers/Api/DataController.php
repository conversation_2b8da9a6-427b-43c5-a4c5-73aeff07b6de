<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use App\Models\Persona;
use App\Models\PersonaResult;

class DataController extends Controller
{
    public function getCharityCategories()
    {
        $cacheKey = 'charity_categories';
        $cacheTime = config('services.recommendation_engine.cache_time', env('CHARITY_CACHE_TIME', 60));

        $response = Cache::remember($cacheKey, $cacheTime, function () {
            return $this->httpClient()->get('/get_charity_categories')->json();
        });

        return response()->json($response);
    }

    public function getCharitySubCategories(Request $request)
    {
        $request->validate([
            'GENERAL_CATEGORY' => 'required|string',
        ]);

        $cacheKey = 'charity_sub_categories_' . md5($request->GENERAL_CATEGORY);
        $cacheTime = config('services.recommendation_engine.cache_time', env('CHARITY_CACHE_TIME', 60));

        $response = Cache::remember($cacheKey, $cacheTime, function () use ($request) {
            return $this->httpClient()->post('/get_how_charity_support', [
                'GENERAL_CATEGORY' => $request->GENERAL_CATEGORY,
            ])->json();
        });

        return response()->json($response);
    }

    public function getCharityAreaOfFocus(Request $request)
    {
        $request->validate([
            'GENERAL_CATEGORY' => 'required|string',
            'SUB_CATEGORY' => 'required|string',
        ]);

        $cacheKey = 'charity_area_focus_' . md5($request->GENERAL_CATEGORY . $request->SUB_CATEGORY);
        $cacheTime = config('services.recommendation_engine.cache_time', env('CHARITY_CACHE_TIME', 60));

        $response = Cache::remember($cacheKey, $cacheTime, function () use ($request) {
            return $this->httpClient()->post('/get_charity_area_of_focus', [
                'GENERAL_CATEGORY' => $request->GENERAL_CATEGORY,
                'SUB_CATEGORY' => $request->SUB_CATEGORY,
            ])->json();
        });

        return response()->json($response);
    }

    public function getTopDonorDetails(Request $request)
    {
        $data = $request->all();

        $request->validate([
            'REGION_CATEGORY' => 'required|string',
            'GENERAL_CATEGORY' => 'required|string',
            'SUB_CATEGORY' => 'nullable|string',
            'AREA_OF_FOCUS' => 'nullable|string',
            'FREQUENCY_WEIGHT' => 'nullable|numeric',
            'DONATION_WEIGHT' => 'nullable|numeric',
            'AVERAGE_DONATION_WEIGHT' => 'nullable|numeric',
            'NUMBER_OF_DONORS_WEIGHT' => 'nullable|numeric',
            'STRATEGY_TYPE' => 'nullable|string', // Optional field for automated campaigns
        ], [
            'REGION_CATEGORY' => "Region category is required",
            'GENERAL_CATEGORY' => "General category is required",
        ]);

        $data = array_change_key_case($data, CASE_LOWER);

        $response = $this->httpClient()->post('/top3_donor_details_using_lad_v5', $request->all())->json();

        if (isset($response['data'])) {

            $persona = new Persona;

            $persona->fill(array_merge([
                'user_id' => auth()->user()->id,
            ], $data));

            $persona->save();

            foreach ($response['data'] ?? [] as $resultData) {
                $personaResult = new PersonaResult;
                $personaResult->persona_id = $persona->id;
                $personaResult->affinity = $resultData['AFFINITY'];
                $personaResult->frequency = $resultData['FREQUENCY'];
                $personaResult->likelihood_to_give_regularly = $resultData['LIKELIHOOD_TO_GIVE_REGULARLY'];
                $personaResult->max_amount = $resultData['MAX_AMOUNT'];
                $personaResult->pravi_score = $resultData['PRAVI_SCORE'];
                $personaResult->predicted_average_donation = $resultData['PREDICTED_AVERAGE_DONATION'];
                $personaResult->total_donation_amount = $resultData['TOTAL_DONATION_AMOUNT'];
                $personaResult->total_number_of_donors = $resultData['TOTAL_NUMBER_OF_DONORS'];

                $personaResult->save();
            }
        }

        return response()->json($response);
    }

    private function httpClient()
    {
        $http = Http::baseUrl(config('services.recommendation_engine.url'));

        if (config('app.env') == "local") {
            return $http->withoutVerifying();
        }

        return $http;
    }
}
