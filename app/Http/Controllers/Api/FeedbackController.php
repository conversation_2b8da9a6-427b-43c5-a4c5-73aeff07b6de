<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Feedback;

class FeedbackController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'score' => [
                'nullable',
                'integer',
                'in:0,1'
            ],
            'message' => [
                'nullable',
                'string',
                'max:255'
            ],
            'type' => [
                'required',
                'in:find_new_donors_results,chatbot_response',
            ]
        ]);

        $data = array_merge($request->toArray(), ['user_id' => auth()->user()->id]);

        $feedback = new Feedback();
        $feedback->fill($data);
        $feedback->save();

        return response()->json($feedback);
    }
}
