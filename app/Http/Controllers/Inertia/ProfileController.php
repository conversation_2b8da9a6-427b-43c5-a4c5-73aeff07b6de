<?php

namespace App\Http\Controllers\Inertia;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ProfileController extends Controller
{
    public function notification_preferences(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'sms' => 'nullable|boolean',
            'email' => 'nullable|boolean',
            'marketing' => 'nullable|boolean',
            'account_alerts' => 'nullable|boolean',
            'news_updates' => 'nullable|boolean',
            'surveys_feedback' => 'nullable|boolean',
            'support_service' => 'nullable|boolean',
        ]);

        $notifications = auth()->user()->notification_preferences()->firstOrNew();

        $notifications->fill($request->toArray());
        $notifications->save();

        return back()->with('flash', [
            'success' => "Saved",
        ]);
    }
}
