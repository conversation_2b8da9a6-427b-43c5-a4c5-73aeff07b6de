<?php

namespace App\Http\Controllers\Inertia;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

use App\Models\Country;
use App\Models\Plan;
use App\Models\User;
use App\Services\StripeApi;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class SetupController extends Controller
{
    public function organisation_name(Request $request)
    {
        $user = auth()->user();
        $company = $user->getCompany();

        if (!$user->welcome_pack_sent_at && !app()->environment('testing')) {
            \Mail::to($user->email)->send(new \App\Mail\WelcomePackMail($user));
            $user->welcome_pack_sent_at = now();
            $user->save();
        }


        return Inertia::render('Auth/OrganisationName', [
            'company' => $company,
        ]);
    }

    public function organisation_mission(Request $request)
    {
        $user = auth()->user();

        $company = $user->getCompany();
        $about = $company ? $company->about : null;


        return Inertia::render('Auth/OrganisationMission', [
            'about' => $about,
        ]);
    }

    public function organisation_cause(Request $request)
    {
        $user = auth()->user();

        $company = $user->getCompany();
        $general_category = $company ? $company->general_category : null;
        $sub_category = $company ? $company->sub_category : null;


        return Inertia::render('Auth/OrganisationCause', [
            'general_category' => $general_category,
            'sub_category' => $sub_category,
        ]);
    }

    public function organisation_region(Request $request)
    {
        $user = auth()->user();

        $company = $user->getCompany();
        $region = $company ? $company->region : null;


        return Inertia::render('Auth/OrganisationRegion', [
            'region' => $region,
        ]);
    }

    public function organisation_language(Request $request)
    {
        $user = auth()->user();

        $company = $user->getCompany();
        $language_preference = $company ? $company->language_preference : null;


        return Inertia::render('Auth/OrganisationLanguage', [
            'language_preference' => $language_preference,
        ]);
    }

    public function organisation_tone(Request $request)
    {
        $user = auth()->user();

        $company = $user->getCompany();
        $tone_preference = $company ? $company->tone_preference : null;


        return Inertia::render('Auth/OrganisationTone', [
            'tone_preference' => $tone_preference,
        ]);
    }

    public function organisation_assets(Request $request)
    {
        $user = auth()->user();
        $company = $user->getCompany();


        return Inertia::render('Auth/OrganisationAssets', [
            // single logo
            'initialLogo' => $company->logo
            ? [
                'path' => $company->logo,
                'url'  => Storage::disk(config('filesystems.default'))->url($company->logo),
                'name' => basename($company->logo),
                'size' => Storage::disk(config('filesystems.default'))->size($company->logo),
                'mime' => Storage::disk(config('filesystems.default'))->mimeType($company->logo),
                ]
            : null,


            // collections of references & guidelines
            'initialReferences' => $company->references->map(fn($r) => [
                'path' => $r->path,
                'url'  => Storage::disk(config('filesystems.default'))->url($r->path),
                'name' => basename($r->path),
                'size' => Storage::disk(config('filesystems.default'))->size($r->path),
                'mime' => Storage::disk(config('filesystems.default'))->mimeType($r->path),
            ]),
            'initialGuidelines' => $company->guidelines->map(fn($g) => [
                'path' => $g->path,
                'url'  => Storage::disk(config('filesystems.default'))->url($g->path),
                'name' => basename($g->path),
                'size' => Storage::disk(config('filesystems.default'))->size($g->path),
                'mime' => Storage::disk(config('filesystems.default'))->mimeType($g->path),
            ]),
          ]);
    }

    // public function company_about(Request $request)
    // {
    //     $user = auth()->user();

    //     if (!$user->welcome_pack_sent_at) {
    //         \Mail::to($user->email)->send(new \App\Mail\WelcomePackMail($user));
    //         $user->welcome_pack_sent_at = now();
    //         $user->save();
    //     }

    //     $company = $user->getCompany();
    //     $about = $company ? $company->about : null;

    //     return Inertia::render('Auth/CompanyAbout', [
    //         'about' => $about,
    //     ]);
    // }


    public function subscription()
    {
        $plans = Plan::all();
        return Inertia::render('Auth/SubscriptionPlans', [
            'plans' => $plans,
        ]);
    }

    public function add_users()
    {
        $roles = User::getRoles();

        $user = auth()->user();

        //CHECK FOR ACTIVE SUBSCRIPTION BEFORE GOING TO THIS STEP
        // $stripeApi = new StripeApi;
        // $hasActiveSubscription = $stripeApi->hasActiveSubscription(null, $user->email);

        // if (!$hasActiveSubscription) {
        //     return back()->with('flash', [
        //         'status' => 'fail',
        //         'message' => "You must have a valid subscription before accessing this section."
        //     ]);
        // }


        $company = $user->company;
        $users = User::where('company_id', $company->id)->where('user_type', User::TYPE_CONSUMER)->get();

        return Inertia::render('Auth/AddUsers', [
            'roles' => $roles,
            'users' => $users,
        ]);
    }

    public function connect_accounts()
    {
        $user = Auth::user();
        // Get the related company
        $company = $user->getCompany()?->load('country'); // Load related 'country' if needed

        return Inertia::render('Auth/ConnectAccounts', [
            'company' => $company,
        ]);
    }

    public function thank_you()
    {
        return Inertia::render('Auth/ThankYou');
    }

}
