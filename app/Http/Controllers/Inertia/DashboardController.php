<?php

namespace App\Http\Controllers\Inertia;

use App\Http\Controllers\Controller;
use App\Models\AgeBand;
use App\Models\CompanyIntegration;
use App\Models\Gender;
use App\Models\Location;
use App\Models\SalaryBand;
use App\Services\FacebookService;
use App\Services\InstagramService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        if (auth()->check()) {
            auth()->user()->refresh();
        }

        // Redirect to the build index page as the main dashboard
        return redirect()->route('build.index');
    }

    public function settings_profile(Request $request)
    {
        return Inertia::render('Settings/Profile');
    }

    public function benchmarker(Request $request)
    {
        if (auth()->check()) {
            auth()->user()->refresh();
        }

        $company = auth()->user()->getCompany();

        return Inertia::render('Benchmarker', [
            'industry_standards' => config('values.industry_standards'),
            'company' => $company,
        ]);
    }

    public function facebook_data(Request $request)
    {
        if (auth()->check()) {
            auth()->user()->refresh();
        }

        $company = auth()->user()->getCompany();

        $integration = CompanyIntegration::where('company_id', $company->id)
            ->where('platform', 'meta')
            ->first();

        $facebookService = new FacebookService($integration);

        $pageData = collect($facebookService->fetchFacebookPageInsights())->toArray();
        $adData = collect($facebookService->fetchFacebookAdInsights())->toArray();

        $configData = json_decode($integration->data, true);

        return Inertia::render('Facebook', [
            'pageData' => $pageData,
            'adData' => $adData,
            'configData' => $configData,
        ]);
    }

    public function instagram_data(Request $request)
    {
        if (auth()->check()) {
            auth()->user()->refresh();
        }

        $company = auth()->user()->getCompany();

        $integration = CompanyIntegration::where('company_id', $company->id)
            ->where('platform', 'meta')
            ->first();

        $instagramService = new InstagramService($integration);

        $pageData = collect($instagramService->fetchInstagramInsights())->toArray();
        $adData = collect($instagramService->fetchInstagramAdInsights())->toArray();

        $configData = json_decode($integration->data, true);

        return Inertia::render('Instagram', [
            'pageData' => $pageData,
            'adData' => $adData,
            'configData' => $configData,
        ]);
    }
}
