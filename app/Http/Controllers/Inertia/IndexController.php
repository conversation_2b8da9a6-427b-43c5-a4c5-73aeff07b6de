<?php

namespace App\Http\Controllers\Inertia;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

use App\Models\Faq;
use App\Models\Plan;
use App\Models\PostCategory;
use App\Models\Post;

class IndexController extends Controller
{
    public function index()
    {
        return Inertia::render('Home', [
            'canLogin' => Route::has('login'),
            'canRegister' => Route::has('register'),
            'plans' => Plan::all(),
        ]);
    }

    public function faqs()
    {
        return Inertia::render('FAQ', [
            'faqs' => Faq::all(),
        ]);
    }

    public function contact_us()
    {
        return Inertia::render('ContactUs');
    }

    public function select_subscription()
    {
        $user = auth()->user();
        if ($user) {
            return to_route('setup.subscription');
        } else {
            return to_route('register');
        }
    }

    public function blog(Request $request)
    {
        $query = Post::with('categories')
            ->where('published', true)
            ->orderBy('created_at', 'desc');

        // Filter by category if provided
        if ($request->filled('category') && $request->category != 0) {
            $query->whereHas('categories', function ($q) use ($request) {
                 $q->where('post_categories.id', $request->category);
            });
        }

        // Filter by search query if provided
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Paginate after filtering
        $posts = $query->paginate(5);

        return Inertia::render('Blog', [
            'posts' => $posts,
            'categories' => PostCategory::all(),
            'selectedCategory' => $request->category,
            'search' => $request->input('search', ''),
        ]);
    }

    public function post_show($slug)
    {
        // Retrieve the post by slug along with its categories.
        $post = Post::where('slug', $slug)->with('categories')->firstOrFail();

        // Retrieve the 4 most recent posts (excluding the current one)
        $otherPosts = Post::where('id', '!=', $post->id)
            ->where('published', true)
            ->latest()
            ->take(4)
            ->with('categories')
            ->get();

        // Get all categories for the "All Categories" section
        $categories = PostCategory::all();

        return Inertia::render('PostShow', [
            'post' => $post,
            'otherPosts' => $otherPosts,
            'categories' => $categories,
        ]);
    }
}
