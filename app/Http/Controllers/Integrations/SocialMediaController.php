<?php

namespace App\Http\Controllers\Integrations;

use App\Http\Controllers\Controller;
use App\Custom\Facebook\Facebook;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Models\CompanyIntegration;

class SocialMediaController extends Controller
{
    public function redirectToMeta()
    {
        $fb = new Facebook([
            'app_id' => config('services.meta.app_id'),
            'app_secret' => config('services.meta.app_secret'),
            'default_graph_version' => config('services.meta.default_graph_version'),
        ]);

        $helper = $fb->getRedirectLoginHelper();
        $permissions = explode(",", config('services.meta.scopes'));
        $callbackUrl = route('auth.meta.callback');

        session()->regenerate(); // Regenerate session for state management
        $loginUrl = $helper->getLoginUrl($callbackUrl, $permissions);
        //$loginUrl .= "&config_id=" . config('services.facebook.app_config_id');

        return redirect($loginUrl);
    }

    public function handleMetaCallback(Request $request)
    {
        $fb = new Facebook([
            'app_id' => config('services.meta.app_id'),
            'app_secret' => config('services.meta.app_secret'),
            'default_graph_version' => config('services.meta.default_graph_version'),
        ]);

        $helper = $fb->getRedirectLoginHelper();

        if (isset($_GET['state'])) {
            $helper->getPersistentDataHandler()->set('state', $_GET['state']);
        }

        try {
            $accessToken = $helper->getAccessToken();
        } catch (\Exception $e) {

            if (str_contains($e->getMessage(), "code has been used")) {
                return redirect(route('auth.meta'));
            }

            throw $e;
        }

        if (!$accessToken) {
            return redirect('/build/campaign')->with('success', 'Access token not provided.');
        }

        $oAuth2Client = $fb->getOAuth2Client();
        $longLivedToken = $oAuth2Client->getLongLivedAccessToken($accessToken);

        $response = $fb->get('/me/accounts?fields=instagram_business_account,name,access_token', $longLivedToken);
        $pages = $response->getDecodedBody();
        $businessId = '';
        $businessRespose = $fb->get('/me/businesses', $longLivedToken);
        $businesses = $businessRespose->getDecodedBody();
        $businessId = $businesses['data'][0]['id'] ?? null;

        if ($businesses) {

            $adAccounts = $fb->get("/$businessId/owned_ad_accounts", $longLivedToken);
            $ads = $adAccounts->getDecodedBody();
        }

        $company = Auth::user()->getCompany();

        $eternalData = [
            'page' => null,
            'business' => null,
            'ad_account' => null,
            'instagram' => null,
        ];

        //PAGE
        if (isset($pages['data'][0])) {
            $eternalData['page'] = $pages['data'][0] ?? null;
        }

        //BUSINESS
        if (isset($businesses['data'][0])) {
            $eternalData['business'] = $businesses['data'][0] ?? null;
        }

        //AD ACCOUNT
        if (isset($ads['data'][0])) {
            $eternalData['ad_account'] = $ads['data'][0] ?? null;
        }

        //INSTAGRAM
        if (isset($pages['data'][0]['instagram_business_account'])) {
            $eternalData['instagram'] = $pages['data'][0]['instagram_business_account'] ?? null;
        }

        CompanyIntegration::updateOrCreate(
            [
                'company_id' => $company->id,
                'platform' => 'meta',
            ],
            [
                'access_token' => $longLivedToken,
                'data' => json_encode($eternalData),
            ]
        );

        echo "Meta account connected successfully. You can close this window OR click <a href='/insights/facebook'>here</a> to go to dashboard";
        //echo "Meta account connected successfully. You can close this window OR click <a href='/dashboard'>here</a> to go to dashboard";
        exit();

        return redirect('/settings/integrations')->with('success', 'Meta account connected successfully.');
    }

    public function delete(Request $request)
    {

        $company = Auth::user()->getCompany();

        $integration = $company->integrations()->where('platform', $request->type)->first();

        if ($integration) {
            $integration->delete();
        }

        if ($request->ajax()) {
            return response()->json(['message' => "ok"]);
        }
    }
}
