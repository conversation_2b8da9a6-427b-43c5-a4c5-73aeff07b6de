<?php

namespace App\Http\Controllers\Integrations;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CompanyIntegration;
use Illuminate\Support\Facades\Cache;
use App\Services\FacebookService;
use App\Services\InstagramService;

class InsightsController extends Controller
{   
    private $facebookService;
    private $instagramService;
    private $company;
    private $integration;

    public function __construct()
    {
        $this->company = auth()->user()->getCompany();

        $this->integration = CompanyIntegration::where('company_id', $this->company->id)
            ->where('platform', 'meta')
            ->first()
        ;

        $this->facebookService = new FacebookService($this->integration);
        $this->instagramService = new instagramService($this->integration);
    }

    public function facebookInsights(Request $request)
    {
        
        $pageStats = $this->facebookService->fetchFacebookPageInsights();
        $adStats = $this->facebookService->fetchFacebookAdInsights();

        return response()->json([
            'page' => $pageStats,
            'ad' => $adStats,
        ]);
    }

    public function instagramInsights(Request $request)
    {
        
        $adStats = $this->instagramService->fetchInstagramAdInsights($this->integration);
        $pageStats = $this->instagramService->fetchInstagramInsights($this->integration);

        return response()->json([
            'page' => $pageStats,
            'ad' => $adStats,
        ]);
    }
}
