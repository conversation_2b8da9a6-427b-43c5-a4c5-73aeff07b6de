<?php

namespace App\Http\Controllers\Integrations\Auth;
 
use App\Http\Controllers\Controller;
use Google\Client;
use Google\Service\Oauth2;
use Illuminate\Http\Request;
use App\Models\CompanyIntegration;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Google\Service\Analytics;
use Google\Service\AnalyticsReporting;
use Google\Analytics\Admin\V1alpha\AnalyticsAdminServiceClient;
use Google\Service\AnalyticsData;
use Google\Service\AnalyticsData\RunReportRequest;

use Google\Service\GoogleAnalyticsAdmin as AnalyticsAdmin;
use Lara<PERSON>\Socialite\Facades\Socialite;

class GoogleAuthController extends Controller
{
    public function redirectToGoogle()
    {
        $client = new Client();
        $client->setClientId(config('services.google.client.id'));
        $client->setClientSecret(config('services.google.client.secret'));
        $client->setRedirectUri(route('auth.google.callback'));
        $client->addScope(['https://www.googleapis.com/auth/analytics.readonly']);
        $client->setAccessType('offline'); // Allows refresh token

        return redirect($client->createAuthUrl());
    }

    public function handleGoogleCallback(Request $request)
    {
        $client = new Client();
        $client->setClientId(config('services.google.client.id'));
        $client->setClientSecret(config('services.google.client.secret'));
        $client->setRedirectUri(route('auth.google.callback'));

        // Exchange authorization code for access token
        $token = $client->fetchAccessTokenWithAuthCode($request->code);

        if (isset($token['error'])) {
            return redirect('/')->with('error', 'Google Authentication Failed');
        }

        $client->setAccessToken($token);


        // Initialize the Google Analytics Admin service (for GA4)
        $analyticsAdmin = new AnalyticsAdmin($client);


        // Create the Analytics service instance
        $analytics = new Analytics($client);

        // List all accounts for the authenticated user
        $accounts = $analytics->management_accounts->listManagementAccounts();

        $accountList = [];
        foreach ($accounts->getItems() as $account) {

            // Fetch the list of GA4 properties
            $properties = $analyticsAdmin->properties->listProperties([
                'filter' => 'parent:accounts/' . $account->getId(),
            ]);

            $propertyList = [];
            foreach ($properties->getProperties() as $property) {
                $propertyList[] = [
                    'id' => $property->getName(), // Format: properties/{property_id}
                    'name' => $property->getDisplayName(),
                    'account' => json_encode($account),
                    'property' => json_encode($property),
                ];
            }
        }

        return view('integrations.google.select_property', [
            'propertyList' => $propertyList,
            'token' => $token
        ]);
    }

    public function storeSelectedProperty(Request $request)
    {
        $request->validate([
            'property_id' => 'required|string',
            'token' => 'required',
        ]);

        $user = Auth::user();
        $company = $user->getCompany();

        $propertyData = json_decode($request->property_id, true);
        $token = json_decode($request->token, true);
        $property = json_decode($propertyData['property'] ?? '', true);
        $account = json_decode($propertyData['account'] ?? '', true);

        $hasProperty = $property['name'] ?? false;

        if (isset($token['access_token']) && isset($property['name'])) {
            $data = [
                'access_token' => $token,
                'property' => $property,
                'account' => $account,
            ];

            CompanyIntegration::updateOrCreate(
                [
                    'company_id' => $company->id,
                    'platform' => 'google_analytics'
                ],
                [
                    'access_token' => $token['access_token'] ?? null,
                    'entity_id' => $property['name'],
                    'data' => json_encode($data),
                ]
            );
            //dd("ASD");
            echo "Goolge Analytics account connected successfully. You can close this window.";
            //echo "Meta account connected successfully. You can close this window OR click <a href='/dashboard'>here</a> to go to dashboard";
            exit();
        }

        $noConnection = "There is an with the connection. ";

        if (!$hasProperty) {
            $noConnection .= "Please make sure you have Property set in your Google Analytics administrator panel.";
        }

        echo $noConnection;
        exit();
        
    }

    public function redirectToGoogleAuth()
    {
        return Socialite::driver('google')->redirect();
    }

    public function handleGoogleAuthCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();
            $user = User::where('google_id', $googleUser->id)->first();

            if (!$user) {
                $user = User::where('email', $googleUser->email)->first();
            }

            if (!$user) {
                return redirect()->to(route('register') . "?e=g-0&email=".$googleUser->email . "&first_name=" . $googleUser->user['given_name'] . "&last_name=" . $googleUser->user['family_name']);
            }

            \Illuminate\Support\Facades\Auth::login($user);

            \Laravel\Fortify\Fortify::loginThrough(function () use ($user) {
                if ($user) {
                    return $user;
                }
            });

            auth()->login($user, true);

            $userInstance = auth()->user();
            request()->session()->put([
                'password_hash' => $userInstance->getAuthPassword(),
                'password_hash_' . auth()->getDefaultDriver() => $userInstance->getAuthPassword(),
            ]);

            return redirect()->route('dashboard');
        } catch (\Exception $e) {
            return redirect()->route('login')->withErrors([
                'google' => 'Google login failed. Please try again.',
            ]);
        }
    }
}
