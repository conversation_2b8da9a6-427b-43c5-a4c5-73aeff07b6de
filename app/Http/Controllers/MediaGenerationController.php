<?php

namespace App\Http\Controllers;

use App\Contracts\Services\MediaGenerationServiceInterface;
use App\Http\Requests\GenerateImageRequest;
use App\Http\Requests\GenerateKlingVideoRequest;
use App\Http\Requests\GenerateMidjourneyImageRequest;
use App\Http\Requests\GenerateVideoRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MediaGenerationController extends Controller
{
    protected MediaGenerationServiceInterface $mediaService;

    public function __construct(MediaGenerationServiceInterface $mediaService)
    {
        $this->mediaService = $mediaService;
    }

    protected function generateMediaPrompt(string $postContent, $company, array $validated): string
    {
        $promptTemplate = config('prompts.media_generation');

        $spellingVariant = $validated['spelling_variant'] ?? null;
        if (! $spellingVariant) {
            $region = $company->region ?? '';
            $usStates = config('regions.us_states');
            $spellingVariant = in_array($region, $usStates) ? 'American English' : 'British English';
        }

        $replacements = [
            '{OrganisationName}' => $company->name ?? 'Organization',
            '{OrganisationRegion}' => $company->region ?? 'Global',
            '{Mission}' => $company->about ?? 'Our mission',
            '{PreferredLanguage}' => $company->language_preference ?? 'English',
            '{ToneOfVoice}' => $company->tone_preference ?? 'Professional',
            '{AudienceGender}' => $validated['audience_gender'] ?? 'Mixed',
            '{AudienceAge}' => $validated['audience_age'] ?? 'All ages',
            '{AudienceIncome}' => $validated['audience_income'] ?? 'All income levels',
            '{SelectedSocialMediaPlatform}' => $validated['selected_social_platform'] ?? 'social media',
            '{SelectedCommunicationChannel}' => $validated['selected_communication_channel'] ?? '',
            '{FundraisingCampaigns}' => $validated['fundraising_campaigns'] ?? '',
            '{PreferredCommunicationChannel}' => $validated['preferred_communication_channel'] ?? '',
            '{LikelyDonationAmount}' => $validated['likely_donation_amount'] ?? '',
            '{SpellingVariant}' => $spellingVariant,
            '{CampaignType}' => $validated['campaign_type'] ?? '',
            '{CampaignStartDate}' => $validated['campaign_start_date'] ?? '',
            '{CampaignEndDate}' => $validated['campaign_end_date'] ?? '',
            '{FundraisingTarget}' => $validated['fundraising_target'] ?? '',
            '{CallToAction}' => $validated['call_to_action'] ?? '',
            '{PostContent}' => $postContent,
        ];

        return strtr($promptTemplate, $replacements);
    }

    protected function determinePrompt(array $validated, $company): string
    {
        if (! empty($validated['post_content'])) {
            return $this->generateMediaPrompt($validated['post_content'], $company, $validated);
        }

        return $validated['prompt'];
    }

    protected function prepareMediaParams(array $validated, string $prompt): array
    {
        $mediaParams = array_filter($validated, function ($key) {
            return ! in_array($key, ['post_content', 'audience_gender', 'audience_age', 'audience_income', 'spelling_variant', 'selected_social_platform']);
        }, ARRAY_FILTER_USE_KEY);

        $mediaParams['prompt'] = $prompt;

        return $mediaParams;
    }

    protected function addPromptDataToResult(array $result, string $prompt, array $validated): array
    {
        $result['generated_prompt'] = $prompt;
        $result['original_post_content'] = $validated['post_content'] ?? null;

        return $result;
    }

    public function generateImage(GenerateImageRequest $request)
    {
        $user = Auth::user();
        $company = $user->getCompany();

        if (! $user || ! $company) {
            return response()->json(['error' => 'User or company not found'], 404);
        }

        $validated = $request->validated();
        $prompt = $this->determinePrompt($validated, $company);
        $mediaParams = $this->prepareMediaParams($validated, $prompt);

        $errors = $this->mediaService->validateImageParams($mediaParams);
        if (! empty($errors)) {
            return response()->json(['error' => 'Parameter validation failed', 'details' => $errors], 422);
        }

        try {
            $result = $this->mediaService->generateImage($mediaParams);
            $result = $this->addPromptDataToResult($result, $prompt, $validated);

            return response()->json([
                'success' => true,
                'data' => $result,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to generate image', 'details' => $e->getMessage()], 500);
        }
    }

    public function generateVideo(GenerateVideoRequest $request)
    {
        $user = Auth::user();
        $company = $user->getCompany();

        if (! $user || ! $company) {
            return response()->json(['error' => 'User or company not found'], 404);
        }

        $validated = $request->validated();
        $prompt = $this->determinePrompt($validated, $company);
        $mediaParams = $this->prepareMediaParams($validated, $prompt);

        $errors = $this->mediaService->validateVideoParams($mediaParams);
        if (! empty($errors)) {
            return response()->json(['error' => 'Parameter validation failed', 'details' => $errors], 422);
        }

        try {
            $result = $this->mediaService->generateVideo($mediaParams);
            $result = $this->addPromptDataToResult($result, $prompt, $validated);

            return response()->json([
                'success' => true,
                'data' => $result,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to generate video', 'details' => $e->getMessage()], 500);
        }
    }

    public function getTaskStatus(Request $request, string $taskId)
    {
        $user = Auth::user();

        if (! $user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        try {
            $result = $this->mediaService->getTaskStatus($taskId);

            return response()->json([
                'success' => true,
                'data' => $result,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to get task status', 'details' => $e->getMessage()], 500);
        }
    }

    public function cancelTask(Request $request, string $taskId)
    {
        $user = Auth::user();

        if (! $user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        try {
            $success = $this->mediaService->cancelTask($taskId);

            return response()->json([
                'success' => $success,
                'message' => $success ? 'Task cancelled successfully' : 'Failed to cancel task',
                'timestamp' => now()->toISOString(),
            ], $success ? 200 : 500);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to cancel task', 'details' => $e->getMessage()], 500);
        }
    }

    public function getAvailableModels()
    {
        try {
            return response()->json([
                'success' => true,
                'data' => [
                    'image_models' => $this->mediaService->getAvailableImageModels(),
                    'video_models' => $this->mediaService->getAvailableVideoModels(),
                ],
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to get available models', 'details' => $e->getMessage()], 500);
        }
    }

    public function generateMidjourneyImage(GenerateMidjourneyImageRequest $request)
    {
        $user = Auth::user();
        $company = $user->getCompany();

        if (! $user || ! $company) {
            return response()->json(['error' => 'User or company not found'], 404);
        }

        $validated = $request->validated();
        $prompt = $this->determinePrompt($validated, $company);
        $excludeKeys = ['prompt', 'post_content'];
        $filteredOptions = array_diff_key($validated, array_flip($excludeKeys));

        try {
            $result = $this->mediaService->generateMidjourneyImage($prompt, $filteredOptions);
            $result = $this->addPromptDataToResult($result, $prompt, $validated);

            return response()->json([
                'success' => true,
                'data' => $result,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to generate Midjourney image', 'details' => $e->getMessage()], 500);
        }
    }

    public function generateKlingVideo(GenerateKlingVideoRequest $request)
    {
        $user = Auth::user();
        $company = $user->getCompany();

        if (! $user || ! $company) {
            return response()->json(['error' => 'User or company not found'], 404);
        }

        $validated = $request->validated();
        $prompt = $this->determinePrompt($validated, $company);
        $excludeKeys = ['prompt', 'post_content'];
        $filteredOptions = array_diff_key($validated, array_flip($excludeKeys));

        try {
            $result = $this->mediaService->generateKlingVideo($prompt, $filteredOptions);
            $result = $this->addPromptDataToResult($result, $prompt, $validated);

            return response()->json([
                'success' => true,
                'data' => $result,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to generate Kling video', 'details' => $e->getMessage()], 500);
        }
    }
}
