<?php

namespace App\Http\Controllers;

use App\Mail\WelcomePackMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Stripe;

class WebhookController extends Controller
{
    protected $stripe;

    public function stripe(Request $request)
    {
        $this->stripe = new Stripe\StripeClient(config('services.stripe.secret'));
        
        $webhook = json_decode(json_encode($request->toArray()));
        $type = $webhook->type ?? "none";

        if ($type == "invoice.paid" || $type == "customer.subscription.created") {
            if (isset($webhook->data->object->customer)) {

                $customer = $this->stripe->customers->retrieve($webhook->data->object->customer, []);

                $user = \App\Models\User::where('email', $customer->email)->first();
                if ($customer && $user) {
                    Mail::to($customer->email)->send(new WelcomePackMail($user));

                    return response()->json(['status' => "success"]);
                }
            }
        }

        return response()->json(['status' => "success"]);

    }
}