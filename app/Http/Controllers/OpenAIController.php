<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use OpenAI;
use Illuminate\Support\Facades\Auth;

class OpenAIController extends Controller
{
    private function validateRequest(Request $request)
    {
        return $request->validate([
            'messages'       => 'sometimes|array',
            'messages.*.role'=> 'required_with:messages|string|in:system,user,assistant',
            'messages.*.content' => 'required_with:messages|string',
            'audience_gender'=> 'required|string',
            'audience_age'   => 'required|string',
            'audience_income'=> 'required|string',

            'feature'        => 'nullable|string|in:awareness,lead_capture,stewardship,campaigns,donations',
            // for awareness, must be passed in by button selected in modal
            'selected_social_platform' => 'nullable|string',
            // for stewardship, must be passed in by button selected in modal
            'selected_communication_channel' => 'nullable|string',
            // for campaigns, passed in from affinity results
            'fundraising_campaigns' => 'nullable|string',
            // for donations, passed in from affinity results (communication channels this person uses)
            'preferred_communication_channel' => 'nullable|string',
            // for donations, passed in from affinity results (Predicted average donation)
            'likely_donation_amount' => 'nullable|string',
            // for post count specification
            'post_count' => 'nullable|integer|min:1|max:10',
            'platform' => 'nullable|string',
            'context' => 'nullable|string'
        ]);
    }

    public function handleChat(Request $request)
    {
        $validated = $this->validateRequest($request);

        $user = Auth::user();
        $company = $user->getCompany();

        if (!$user || !$company) {
            return response()->json([
                'error' => 'User or company not found',
            ], 404);
        }


        // Get the main system prompt
        $systemPrompt = config('prompts')['system'];

        // Initialize the messages array with the system prompt
        $messages = [
            ['role' => 'system', 'content' => $systemPrompt],
        ];

        // Check for a template type in the request and add the corresponding prompt
        if (!empty($validated['feature'])) {
            $templatePrompt = $this->generateTemplatePrompt($validated['feature'], $company, $validated);
            $messages[] = ['role' => 'user', 'content' => $templatePrompt];
        } elseif (isset($validated['messages'])) {
            // If no template type is provided, fallback to user-provided messages
            $messages = array_merge($messages, $validated['messages']);
        }

        return $this->sendToOpenAI($messages);
    }

    private function generateTemplatePrompt($feature, $company, $validated)
    {
        // Determine which feature prompt to use (default to 'default' if not provided)
        $feature = $validated['feature'] ?? 'default';

        // Load the system prompts config
        $systemPrompts = config('prompts');

        // Get the specific system prompt
        $promptTemplate = $systemPrompts[$feature];

        // If no template found, fallback to a generic system prompt
        if (!$promptTemplate) {
            $promptTemplate = $systemPrompts['default'];
        }

        // Handle post count and generate instructions
        $postCount = $validated['post_count'] ?? 5;
        $postCountInstructions = $this->generatePostCountInstructions($postCount);

        // Prepare the replacement values
        $replacements = [
            '{OrganisationName}'     => $company->name,
            '{OrganisationRegion}'   => $company->region,
            '{Mission}'              => $company->about,
            '{PreferredLanguage}'    => $company->language_preference ?? 'not defined',
            '{ToneOfVoice}'          => $company->tone_preference ?? 'not defined',
            '{AudienceGender}'       => $validated['audience_gender'] ?? '',
            '{AudienceAge}'          => $validated['audience_age'] ?? '',
            '{AudienceIncome}'       => $validated['audience_income'] ?? '',
            '{SelectedSocialMediaPlatform}' => $validated['selected_social_platform'] ?? '',
            '{SelectedCommunicationChannel}' => $validated['selected_communication_channel'] ?? '',
            '{FundraisingCampaigns}' => $validated['fundraising_campaigns'] ?? '',
            '{PreferredCommunicationChannel}' => $validated['preferred_communication_channel'] ?? '',
            '{LikelyDonationAmount}' => $validated['likely_donation_amount'] ?? '',
            '{PostCount}' => $postCount,
            '{PostCountInstructions}' => $postCountInstructions,
        ];

        // Replace the placeholders in the template
        $specificPrompt = strtr($promptTemplate, $replacements);
        return $specificPrompt;
    }

    /**
     * Generate post count specific instructions
     */
    private function generatePostCountInstructions(int $postCount): string
    {
        if ($postCount === 1) {
            return 'Create one high-quality post that captures the essence of the message.';
        } elseif ($postCount <= 3) {
            return "Vary archetype across posts: {Story | Impact Stat | CTA}. Each post should have a distinct angle.";
        } elseif ($postCount <= 5) {
            return "Vary archetype sequentially: {Story | Impact Stat | Behind-Scenes | Quote | CTA Blast}. Each post should have a distinct angle and approach.";
        } else {
            return "Create diverse content types including: stories, impact statistics, behind-the-scenes content, quotes, testimonials, and call-to-action posts. Ensure each post has a unique angle and approach to maximize engagement.";
        }
    }

    private function sendToOpenAI($messages)
    {
        $client = OpenAI::client(config('services.openai.key'));

        try {
            $response = $client->chat()->create([
                'model' => 'gpt-4o',
                'messages' => $messages,
            ]);

            return response()->json([
                'message' => $response->choices[0]->message,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to process OpenAI request',
                'details' => $e->getMessage(),
            ], 500);
        }
    }
}
