<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use HubSpot\Factory;
use Inertia\Inertia;

class HubspotController extends Controller
{
    public function subscribe(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email',
            'organisation_name' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
        ]);

        $firstName = $request->input('first_name');
        $lastName = $request->input('last_name');
        $email = $request->input('email');
        $organisationName = $request->input('organisation_name');
        $country = $request->input('country');

        $client = Factory::createWithAccessToken(config('services.hubspot.key'));

        try {
            $client->crm()->contacts()->basicApi()->create([
                'properties' => [
                    'firstname' => $firstName,
                    'lastname' => $lastName,
                    'email' => $email,
                    'company' => $organisationName,
                    'country' => $country,
                ],
            ]);

            return back()->with([
                'success' => 'Thank you for subscribing!',
            ]);
        } catch (\Exception $e) {
            return back()->with([
                'error' => 'There was an error subscribing you.',
            ]);
        }
    }
}
