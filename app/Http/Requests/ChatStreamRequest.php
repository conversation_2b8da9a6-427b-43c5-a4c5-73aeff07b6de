<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ChatStreamRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'messages' => 'sometimes|array',
            'messages.*.role' => 'required_with:messages|string|in:system,user,assistant',
            'messages.*.content' => 'required_with:messages|string',
            'audience_gender' => 'required_without:messages|string',
            'audience_age' => 'required_without:messages|string',
            'audience_income' => 'required_without:messages|string',
            'feature' => 'nullable|string|in:awareness,lead_capture,stewardship,campaigns,donations,social_media_content',
            'selected_social_platform' => 'nullable|string',
            'selected_communication_channel' => 'nullable|string',
            'fundraising_campaigns' => 'nullable|string',
            'preferred_communication_channel' => 'nullable|string',
            'likely_donation_amount' => 'nullable|string',
            'stream' => 'sometimes|boolean',
            'session_id' => 'sometimes|string|max:255',
            'campaign_type' => 'nullable|string',
            'campaign_start_date' => 'nullable|date',
            'campaign_end_date' => 'nullable|date',
            'fundraising_target' => 'nullable|integer',
            'call_to_action' => 'nullable|string',
            'spelling_variant' => 'nullable|string',
            'post_count' => 'nullable|integer|min:1|max:10',
            'platform' => 'nullable|string',
            'context' => 'nullable|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'messages.*.role.in' => 'Message role must be one of: system, user, assistant',
            'audience_gender.required' => 'Audience gender is required for initial prompts',
            'audience_age.required' => 'Audience age is required for initial prompts',
            'audience_income.required' => 'Audience income is required for initial prompts',
            'feature.in' => 'Feature must be one of: awareness, lead_capture, stewardship, campaigns, donations, social_media_content',
        ];
    }
}
