<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GenerateMidjourneyImageRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'prompt' => 'sometimes|string|max:2000',
            'post_content' => 'sometimes|string|max:1000',
            'aspect_ratio' => 'sometimes|string',
            'version' => 'sometimes|string',
            'async' => 'sometimes|boolean',
            'selected_social_platform' => 'sometimes|string',
            'audience_gender' => 'sometimes|string',
            'audience_age' => 'sometimes|string',
            'audience_income' => 'sometimes|string',
            'spelling_variant' => 'sometimes|string',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $data = $validator->getData();
            if (empty($data['prompt']) && empty($data['post_content'])) {
                $validator->errors()->add('prompt', 'Either prompt or post_content is required');
            }
        });
    }
}
