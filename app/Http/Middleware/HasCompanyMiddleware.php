<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response;

class HasCompanyMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check()) {
            $user = auth()->user();
            if ($user->user_type == User::TYPE_COMPANY_ADMIN) {
                if ($user->company) {
                    return $next($request);
                } else {
                    return redirect()->to(route('setup.organisation-name'))->with('message', 'Please add your organisation details.');
                }
            } else if ($user->user_type == User::TYPE_CONSUMER) {
                if ($user->company_id) {
                    return $next($request);
                } else {
                    return Inertia::render('Error', ['status' => 422,])
                    ->toResponse($request)
                    ->setStatusCode(422);
                }
            }
        }

        return Inertia::render('Error', ['status' => 404,])
            ->toResponse($request)
            ->setStatusCode(404);

    }
}
