<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Password;

class InviteTeamMail extends Mailable
{
    use Queueable, SerializesModels;

    protected $owner;
    protected $user;

    /**
     * Create a new message instance.
     */
    public function __construct($owner, $user)
    {
        $this->owner = $owner;
        $this->user = $user;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Invitation',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $token = Password::createToken($this->user);
        $url = url('/reset-password').'/'.$token . "?email=" .$this->user->email;

        return new Content(
            markdown: 'emails.invitation',
            with: [
                'owner' => $this->owner,
                'user' => $this->user,
                'token' => $token,
                'url' => $url,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
