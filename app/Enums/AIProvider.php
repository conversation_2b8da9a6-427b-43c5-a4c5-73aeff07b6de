<?php

namespace App\Enums;

enum AIProvider: string
{
    case OpenAI = 'openai';
    case PiAPI = 'piapi';

    /**
     * Get all platform values
     *
     * @return array<string>
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human readable name
     */
    public function label(): string
    {
        return match ($this) {
            self::OpenAI => 'OpenAI',
            self::PiAPI => 'PiAPI',
        };
    }
}
