<?php

namespace App\Adaptors\SocialMedia;

use App\Contracts\SocialMedia\SocialMediaAdapterInterface;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SocialPilotAdapter implements SocialMediaAdapterInterface
{
    /**
     * Base API URL
     */
    protected string $baseUrl;

    /**
     * API key
     */
    protected ?string $apiKey;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->apiKey = Config::get('socialmedia.connections.socialpilot.api_key');
        $this->baseUrl = Config::get('socialmedia.connections.socialpilot.base_url', 'https://api.socialpilot.co/v2');
    }

    /**
     * Search for users by email.
     *
     * @param  string  $email  The email to search for
     * @return array|null User data or null if not found
     */
    public function searchUsersByEmail(string $email): ?array
    {
        try {
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->get($this->baseUrl.'/accounts/list', [
                'q' => $email,
            ]);

            if ($response->successful()) {
                $data = $response->json();
             
                if (isset($data['response']['accounts']) && is_array($data['response']['accounts'])) {
                    foreach ($data['response']['accounts'] as $account) {
                        if (isset($account['email']) && strtolower($account['email']) === strtolower($email)) {
                            $userId = $account['userId'] ?? $account['id'] ?? null;
                            if ($userId) {
                                return [
                                    'user_id' => $userId,
                                    'email' => $account['email'],
                                    'firstName' => $account['firstName'] ?? null,
                                    'lastName' => $account['lastName'] ?? null,
                                ];
                            }
                        }
                    }
                }

                return null;
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Exception while searching SocialPilot users by email', [
                'email' => $email,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Create a user account on SocialPilot.
     *
     * @return array|null Response data or null on failure
     */
    public function createUser(array $userData): ?array
    {
        try {
            $requiredFields = ['email', 'firstName', 'lastName'];
            foreach ($requiredFields as $field) {
                if (! isset($userData[$field]) || empty($userData[$field])) {
                    Log::error('SocialPilot create user: Missing required field', [
                        'field' => $field,
                        'email' => $userData['email'] ?? 'unknown',
                    ]);

                    return null;
                }
            }

            $data = [
                'email' => $userData['email'],
                'firstName' => $userData['firstName'],
                'lastName' => $userData['lastName'],
            ];

            if (isset($userData['role'])) {
                $data['role'] = $userData['role'];
                $data['accounts'] = $userData['accounts'] ?? [];
            }

            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl.'/user/create', $data);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['response']['userId'])) {
                    return ['user_id' => $data['response']['userId']];
                }

                Log::error('SocialPilot user creation response has unexpected format', [
                    'email' => $userData['email'],
                    'response' => $data,
                ]);

                return null;
            }

            $errorData = $response->json();

            // Check if the error is "Email already exist"
            if ($response->status() === 400 &&
                isset($errorData['status']) &&
                $errorData['status'] === 400 &&
                isset($errorData['reasons']) &&
                is_array($errorData['reasons'])) {
                foreach ($errorData['reasons'] as $reason) {
                    if (isset($reason['message']) &&
                        strpos(strtolower($reason['message']), 'email already exist') !== false &&
                        isset($reason['field']) &&
                        $reason['field'] === 'email') {

                       
                        $existingUser = $this->searchUsersByEmail($userData['email']);
               
                        if ($existingUser && isset($existingUser['user_id'])) {
                            return $existingUser;
                        }
                    }
                }
            }

            Log::error('Failed to create SocialPilot user', [
                'email' => $userData['email'],
                'statusCode' => $response->status(),
                'error' => $errorData,
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception while creating SocialPilot user', [
                'email' => $userData['email'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Generate an authentication token for a user.
     *
     * @return string|null Token or null on failure
     */
    public function generateToken(string $userId): ?string
    {
        try {
            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->get($this->baseUrl.'/generate-token', [
                'userId' => $userId,
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['response']['apiToken'])) {
                    return $data['response']['apiToken'];
                }

                Log::error('SocialPilot token generation response has unexpected format', [
                    'userId' => $userId,
                    'response' => $data,
                ]);

                return null;
            }

            $errorData = $response->json();
            $statusCode = $response->status();

            if ($statusCode === 401) {
                Log::error('Unauthorized access when generating SocialPilot token', [
                    'userId' => $userId,
                ]);
            } elseif ($statusCode === 404) {
                Log::error('User not found when generating SocialPilot token', [
                    'userId' => $userId,
                ]);
            } else {
                Log::error('Failed to generate SocialPilot token', [
                    'userId' => $userId,
                    'statusCode' => $statusCode,
                    'error' => $errorData,
                ]);
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Exception while generating SocialPilot token', [
                'userId' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Check if a user exists on SocialPilot.
     */
    public function userExists(string $userId): bool
    {
        try {

            $response = Http::withHeaders([
                'x-api-key' => $this->apiKey,
                'Accept' => 'application/json',
            ])->get($this->baseUrl.'/generate-token', [
                'userId' => $userId,
            ]);

            if ($response->successful()) {
                return true;
            }

            if ($response->status() === 404) {
                return false;
            }

            Log::error('Error checking if SocialPilot user exists', [
                'userId' => $userId,
                'statusCode' => $response->status(),
                'error' => $response->json(),
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('Exception while checking SocialPilot user', [
                'userId' => $userId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Check if a user exists by email on SocialPilot.
     */
    public function userExistsByEmail(string $email): bool
    {
        $user = $this->searchUsersByEmail($email);

        return $user !== null && isset($user['user_id']);
    }

    public function getPosts(string $userId): array
    {
        return [];
    }

    /**
     * Publish a post on SocialPilot.
     */
    public function publishPost(array $postData): array|bool
    {
        return false;
    }

    /**
     * Schedule a post on SocialPilot.
     */
    public function schedulePost(array $postData): bool
    {
        return false;
    }

    /**
     * Get analytics for a post.
     */
    public function getAnalytics(string $postId): array
    {
        return [];
    }

    /**
     * Get the social media URL for a user.
     */
    public function getSocialUrl(?string $token = null): ?string
    {
        try {
            $domain = Config::get('socialmedia.connections.socialpilot.main_url');

            if ($token) {
                return "{$domain}/custom-login?session={$token}";
            }

            return "{$domain}";
        } catch (\Exception $e) {
            Log::error('Exception while getting SocialPilot social URL', [
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }
}
