<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserNotificationPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'sms',
        'email',
        'marketing',
        'account_alerts',
        'news_updates',
        'surveys_feedback',
        'support_service',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
