<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserSocialAccount extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'platform',
        'username',
        'platform_user_id',
        'access_token',
        'refresh_token',
        'token_expires_at',
        'profile_data',
        'account_stats',
        'is_active',
        'is_verified',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'profile_data' => 'array',
        'account_stats' => 'array',
        'is_active' => 'boolean',
        'is_verified' => 'boolean',
        'token_expires_at' => 'datetime',
    ];

    /**
     * Get the user that owns the social account.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the token is expired.
     *
     * @return bool
     */
    public function isTokenExpired()
    {
        return $this->token_expires_at && now()->gt($this->token_expires_at);
    }

    /**
     * Get accounts by platform.
     *
     * @param  string  $platform
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function byPlatform($platform)
    {
        return static::where('platform', $platform)
            ->where('is_active', true);
    }
}
