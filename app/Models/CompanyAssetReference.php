<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class CompanyAssetReference extends CompanyAsset
{
    protected $table = "company_assets";

    protected static function booted()
    {
        static::addGlobalScope('type', function (Builder $builder) {
            $builder->where('type', 'references');
        });

        static::creating(function ($model) {
            $model->type = "references";
        });

        static::updating(function ($model) {
            $model->type = "references";
        });
    }
}
