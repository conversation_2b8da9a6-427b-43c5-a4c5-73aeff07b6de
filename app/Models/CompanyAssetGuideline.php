<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class CompanyAssetGuideline extends CompanyAsset
{
    protected $table = "company_assets";

    protected static function booted()
    {
        static::addGlobalScope('type', function (Builder $builder) {
            $builder->where('type', 'guidelines');
        });

        static::creating(function ($model) {
            $model->type = "guidelines";
        });

        static::updating(function ($model) {
            $model->type = "guidelines";
        });
    }
}
