<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Persona extends Model
{
    use HasFactory;

    // Allow mass assignment for these attributes
    protected $fillable = [
        'user_id',
        'name',
        'general_category',
        'sub_category',
        'frequency_weight',
        'donation_weight',
        'average_donation_weight',
        'number_of_donors_weight',
        'gender',
        'age',
        'location',
        'area_of_focus',
        'salary',
    ];

    // Define the relationship with PersonaResult
    public function results()
    {
        return $this->hasMany(PersonaResult::class);
    }
}
