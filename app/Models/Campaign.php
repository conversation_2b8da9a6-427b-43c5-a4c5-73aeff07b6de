<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Campaign extends Model
{
    protected $fillable = [
        'uuid',
        'user_id',
        'data',
        'gender',
        'age',
        'category',
        'subcategory',
    ];

    protected $appends = [
        'data_array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uuid = (string) Str::uuid();
        });
    }

    public function getDataArrayAttribute()
    {
        return json_decode($this->data, true);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
