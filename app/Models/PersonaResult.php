<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PersonaResult extends Model
{
    use HasFactory;

    // Allow mass assignment for these attributes
    protected $fillable = [
        'persona_id',
        'affinity',
        'frequency',
        'likelihood_to_give_regularly',
        'max_amount',
        'pravi_score',
        'predicted_average_donation',
        'total_donation_amount',
        'total_number_of_donors',
    ];

    // Define the relationship with Persona
    public function persona()
    {
        return $this->belongsTo(Persona::class);
    }
}
