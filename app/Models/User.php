<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Services\StripeApi;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Cache;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Laravel\Jetstream\HasProfilePhoto;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable implements FilamentUser
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;

    const ROLE_FUNDRAISER = 1;

    const ROLE_MARKETING = 2;

    const ROLE_CEO_EXECUTIVE = 3;

    const ROLE_CONSULTANT = 4;

    const ROLE_OTHER = 5;

    const TYPE_ADMIN = 'admin';

    const TYPE_COMPANY_ADMIN = 'company_admin';

    const TYPE_CONSUMER = 'consumer';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
        'user_type',
        'phone',
        'role_id',
        'company_id',
        'stripe_id',
        'stripe_subscription_id',
        'terms',
        'marketing',
        'welcome_pack_sent_at',
        'is_admin',
        'google_id',
        'avatar',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
        'name',
        'has_stripe_subscription',
        'is_company_owner',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public static function getRoles()
    {
        return [
            [
                'key' => self::ROLE_FUNDRAISER,
                'name' => 'Fundraiser',
            ],
            [
                'key' => self::ROLE_MARKETING,
                'name' => 'Marketing',
            ],
            [
                'key' => self::ROLE_CEO_EXECUTIVE,
                'name' => 'CEO / Executive',
            ],
            [
                'key' => self::ROLE_CONSULTANT,
                'name' => 'Consultant',
            ],
            [
                'key' => self::ROLE_OTHER,
                'name' => 'Other',
            ],
        ];
    }

    public static function getRoleIds()
    {
        return [
            self::ROLE_FUNDRAISER,
            self::ROLE_MARKETING,
            self::ROLE_CEO_EXECUTIVE,
            self::ROLE_CONSULTANT,
            self::ROLE_OTHER,
        ];
    }

    public function getCompany()
    {
        if ($this->user_type == self::TYPE_COMPANY_ADMIN) {
            return $this->company;
        }

        if ($this->user_type == self::TYPE_CONSUMER) {
            return $this->associatedCompany;
        }

        return null;
    }

    public function refresh()
    {
        Cache::forget('stripe-active-sub__'.$this->id);
    }

    public function getHasStripeSubscriptionAttribute()
    {
        if (! in_array(config('app.env'), [
            'production',
            'live',
        ])) {
            return true;
        }

        $key = 'stripe-active-sub__'.$this->id;

        //$data = Cache::remember($key, 60, function () {
            // The value to store if the key doesn't exist.

            $stripeApi = new StripeApi;
            if ($this->user_type == self::TYPE_COMPANY_ADMIN) {
                $hasActiveSubscription = $stripeApi->hasActiveSubscription($this->stripe_id ?: null, $this->email);
                //$value = $hasActiveSubscription;

                return $hasActiveSubscription;
            }

            if ($this->user_type == self::TYPE_CONSUMER) {
                if (!$this->stripe_subscription_id) {
                    return false;
                }

                $subscription = $stripeApi->getSubscription($this->stripe_subscription_id);

                if ($subscription && in_array($subscription->status, ["active", "trialing"])) {
                    return true;
                }
            }

            //return $value;
        //});

        //return $data;

        return false;
    }

    public function getNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function getIsCompanyOwnerAttribute()
    {
        return $this->user_type == self::TYPE_COMPANY_ADMIN;
    }

    // OWNED COMPANY
    // Company created by owner on step 1 of setup
    public function company()
    {
        return $this->hasOne(Company::class);
    }

    // ASSOCIATED COMPANY
    // Used for employees of company, for users created at step 3 of setup
    public function associatedCompany()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function notification_preferences()
    {
        return $this->hasOne(UserNotificationPreference::class);
    }

    public function personas()
    {
        return $this->hasMany(Persona::class);
    }

    // Filament admin panel access
    public function canAccessPanel(Panel $panel): bool
    {
        return $this->is_admin;
    }

    /**
     * Get the user's social media accounts.
     */
    public function socialAccounts()
    {
        return $this->hasMany(UserSocialAccount::class);
    }
}
