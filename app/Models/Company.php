<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Company extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'legal_name',
        'address',
        'city',
        'region',
        'postcode',
        'vat',
        'country_id',
        'user_id',
        'about',
        'logo',
        'language_preference',
        'tone_preference',
        'general_category',
        'sub_category',
    ];

    protected $appends = [
        'is_meta_integrated',
        'is_google_analytics_integrated',
        'is_facebook_integrated',
        'is_instagram_integrated',
        'logo_url',
    ];

    protected $with = [
        'integrations',
    ];

    public function getIsMetaIntegratedAttribute()
    {
        return $this->integrations()->where('platform', 'meta')->exists();
    }

    public function getIsFacebookIntegratedAttribute()
    {
        $integration = $this->integrations()->where('platform', 'meta')->first();

        if (!$integration) {
            return false;
        }

        if (!$integration->data) {
            return false;
        }

        $data = json_decode($integration->data, true);

        if (!count($data)) {
            return false;
        }

        if (!isset($data['page']['id'])) {
            return false;
        }

        if (!isset($data['business']['id'])) {
            return false;
        }

        return true;
    }
    public function getIsInstagramIntegratedAttribute()
    {
        $integration = $this->integrations()->where('platform', 'meta')->first();

        if (!$integration) {
            return false;
        }

        if (!$integration->data) {
            return false;
        }

        $data = json_decode($integration->data, true);

        if (!count($data)) {
            return false;
        }

        if (!isset($data['instagram']['id'])) {
            return false;
        }

        return true;
    }

    public function getIsGoogleAnalyticsIntegratedAttribute()
    {
        return $this->integrations()->where('platform', 'google_analytics')->exists();
    }

    public function getLogoUrlAttribute()
    {
        if (!$this->logo) {
            return null;
        }

        return Storage::disk(config('filesystems.default'))->url($this->logo);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function owner()
    {
        return $this->belongsTo(User::class);
    }

    public function integrations()
    {
        return $this->hasMany(CompanyIntegration::class);
    }

    public function references()
    {
        return $this->hasMany(CompanyAssetReference::class);
    }

    public function guidelines()
    {
        return $this->hasMany(CompanyAssetGuideline::class);
    }
}
