<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Plan extends Model
{
    use HasFactory;

    protected $fillable = [
        'stripe_price_id',
        'title',
        'subtitle',
        'description',
        'placeholder',
        'is_discounted',
        'checkout_url',
    ];

    protected $appends = [
        'description_formatted',
        'checkout_link_for_user',
    ];

    public function getCheckoutLinkForUserAttribute()
    {
        $c = $this->checkout_url;

        if (auth()->check()) {
            $c .= "?prefilled_email=" . auth()->user()->email;
        }

        return $c;
    }

    public function getDescriptionFormattedAttribute()
    {
        return explode("---", $this->description);
    }
}
