<?php

namespace App\Console\Commands;

use App\Enums\SocialPlatformType;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CreateSocialAccounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'social:create-accounts 
                           {--user-id= : Specific user ID to create account for}
                           {--all : Create accounts for all users}
                           {--platform=socialpilot : Platform to create account for (default: socialpilot)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create social media accounts for users';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $userId = $this->option('user-id');
        $all = $this->option('all');
        $platform = $this->option('platform');

        if (! $userId && ! $all) {
            $this->error('Please specify either --user-id or --all option');

            return 1;
        }

        try {
            $platformType = SocialPlatformType::from($platform);
        } catch (\ValueError $e) {
            $this->error("Invalid platform: {$platform}");
            $this->info('Available platforms: '.implode(', ', SocialPlatformType::values()));

            return 1;
        }

        if ($userId) {
            return $this->createAccountForUser($userId, $platformType);
        }

        return $this->createAccountsForAllUsers($platformType);
    }

    /**
     * Create a social media account for a specific user.
     */
    protected function createAccountForUser(int $userId, SocialPlatformType $platform): int
    {
        $user = User::find($userId);

        if (! $user) {
            $this->error("User with ID {$userId} not found");

            return 1;
        }

        $this->info("Creating {$platform->label()} account for user: {$user->email}");

        try {
            $result = socialMediaService()->createUserSocialAccount(
                $user,
                $platform,
                [
                    'email' => $user->email,
                    'firstName' => $user->first_name,
                    'lastName' => $user->last_name,
                ]
            );

            if ($result) {
                $this->info("Successfully created {$platform->label()} account for user: {$user->email}");

                return 0;
            }

            $this->error("Failed to create {$platform->label()} account for user: {$user->email}");

            return 1;
        } catch (\Exception $e) {
            $this->error("Error creating {$platform->label()} account: {$e->getMessage()}");
            Log::error("Failed to create {$platform->label()} account", [
                'user_id' => $user->id,
                'email' => $user->email,
                'platform' => $platform->value,
                'error' => $e->getMessage(),
            ]);

            return 1;
        }
    }

    /**
     * Create social media accounts for all users.
     */
    protected function createAccountsForAllUsers(SocialPlatformType $platform): int
    {
        $users = User::all();
        $this->info("Creating {$platform->label()} accounts for {$users->count()} users");

        $bar = $this->output->createProgressBar($users->count());
        $bar->start();

        $successCount = 0;
        $failureCount = 0;

        foreach ($users as $user) {
            try {
                $result = socialMediaService()->createUserSocialAccount(
                    $user,
                    $platform,
                    [
                        'email' => $user->email,
                        'firstName' => $user->first_name,
                        'lastName' => $user->last_name,
                    ]
                );

                if ($result) {
                    $successCount++;
                } else {
                    $failureCount++;
                }
            } catch (\Exception $e) {
                $failureCount++;
                Log::error("Error creating {$platform->label()} account", [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'platform' => $platform->value,
                    'error' => $e->getMessage(),
                ]);
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);

        $this->info("Completed: {$successCount} successful, {$failureCount} failed");

        return $failureCount > 0 ? 1 : 0;
    }
}
