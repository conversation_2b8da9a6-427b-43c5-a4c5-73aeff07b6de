<?php

namespace App\Console\Commands\Stripe;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

use Stripe;
use Stripe\SubscriptionSchedule;
use Stripe\Subscription;

class CreateTrialAccount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stripe:create-trial-account';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected $stripe;

    public function __construct()
    {
        parent::__construct();

        $this->stripe = new Stripe\StripeClient(config('services.stripe.secret'));
        Stripe\Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->ask('name');
        $email = $this->ask('email');

        $this->subscribe($name, $email);
    }

    public function subscribe($name, $email)
    {
        $priceId = null;

        if (config('app.env') == "local") {
            //TEST
            $priceId = "price_1Q2BJaIjPeWNHiTIVvMQ9jmg";
        } else {
            //LIVE
            $priceId = "price_1Q2BMXIjPeWNHiTIrI9kjSDy";
        }
        
        sleep(2);$this->info("Connecting to stripe.");
        sleep(2);$this->info("Connection established.");
        sleep(1);$this->info("Creating user.");
        
        $customer = $this->setStripeCustomer(null, ['name' => $name, 'email' => $email]);
        $customerId = $customer->id;
        sleep(1);$this->info("User created.");

        sleep(2);$this->info("Creating subscriptions.");

         // Define each phase with its own start and end date, set as trial phases
         $phases = [];
         $currentStart = Carbon::now();
 
         for ($i = 0; $i < 5; $i++) {
             // Define each trial phase duration (e.g., 1 year of trial per phase)
             $currentEnd = $currentStart->copy()->addYear();
 
             $phases[] = [
                 'items' => [
                    ['price' => $priceId],
                ],
                'trial' => true,
                'end_date' => $currentStart->timestamp,
             ];
 
             // Move to the next phase start time
             $currentStart = $currentEnd;
         }
 
         // Create the subscription schedule with the defined trial phases
         $schedule = SubscriptionSchedule::create([
             'customer' => $customerId,
             'phases' => $phases,
             'start_date' => "now",
         ]);
        sleep(2);$this->info("Subscriptions created.");
        sleep(2);$this->info("Verifying subscriptions.");
        sleep(2);$this->info($email . " is ready to use");

        

    }

    private function getCustomerSubscriptions($stripeCustomerId)
	{
		return $this->stripe->subscriptions->all(['customer' => $stripeCustomerId]);
	}

	private function getCustomerActiveSubscription($customerId)
	{
		$subscriptions = $this->getCustomerSubscriptions($customerId) ?: [];
		$subscription = null;
		foreach ($subscriptions as $sub) {
			if (($sub->status == 'active' || $sub->status == 'trialing' || $sub->status == "past_due"  || $sub->status == "unpaid")
            && ($sub->cancel_at_period_end == false && $sub->cancel_at == null)) {
				return $sub;
			}
		}

		return $subscription;
	}

    private function setStripeCustomer($customerId = null, $data = [], $update = false)
	{
        $customer = null;
		if ($customerId) {
			$customer = $this->stripe->customers->retrieve($customerId, []);
		}

		if (!$customerId && isset($data['email'])) {
			$customer = $this->stripe->customers->all(['limit' => 1, 'email' => $data['email']])->data[0] ?? null;
		}

        if ($customer && $update) {
            $toMerge = [
	    		'name' => $data['name'] ?? $data['email'] ?? ($customer->name ?: $customer->email),
		    ];

            if (isset($data['extra'])) {
                $toMerge = array_merge($toMerge, $data['extra']);
            }

            $this->stripe->customers->update($customerId, $toMerge);
        }

		if (!$customer) {
            $customer = $this->stripe->customers->create([
    			'email' => $data['email'],
	    		'name' => $data['name'] ?? $data['email'],
		    ]);
        }

		return $customer;
	}

    private function getStripeFee(float $amount)
    {
        $stripeFees = config('stripe-revenue.fees');
        return ($amount * ($stripeFees['percent'] / 100)) - $stripeFees['flat'];
    }
}
