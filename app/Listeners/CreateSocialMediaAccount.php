<?php

namespace App\Listeners;

use App\Enums\SocialPlatformType;
use App\Events\CreateSocialMediaAccountEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class CreateSocialMediaAccount implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(CreateSocialMediaAccountEvent $event): void
    {
        $user = $event->user;
        $platformFromEvent = $event->platform ?? config('socialmedia.default');

        try {
            socialMediaService()->createUserSocialAccount(
                $user,
                SocialPlatformType::from($platformFromEvent),
                []
            );
        } catch (\Exception $e) {
            Log::error('Failed to create social media account in background job', [
                'user_id' => $user->id,
                'platform' => $platformFromEvent ?? 'SocialPilot',
                'exception' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(CreateSocialMediaAccountEvent $event, \Throwable $exception): void
    {
        Log::error('Failed to create social media account for user', [
            'user_id' => $event->user->id,
            'platform' => $event->platform ?? 'SocialPilot',
            'exception' => $exception->getMessage(),
        ]);
    }
}
