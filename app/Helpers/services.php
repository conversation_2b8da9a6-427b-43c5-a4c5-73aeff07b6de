<?php

use App\Contracts\Services\SocialMediaServiceInterface;
use App\Contracts\Services\UserSocialAccountServiceInterface;

if (! function_exists('socialMediaService')) {
    /**
     * Get the social media service instance.
     */
    function socialMediaService(): SocialMediaServiceInterface
    {
        return app(SocialMediaServiceInterface::class);
    }
}

if (! function_exists('userSocialAccountService')) {
    /**
     * Get the user social account service instance.
     */
    function userSocialAccountService(): UserSocialAccountServiceInterface
    {
        return app(UserSocialAccountServiceInterface::class);
    }
}
