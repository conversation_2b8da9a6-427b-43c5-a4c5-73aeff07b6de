<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;

use Filament\Widgets\StatsOverviewWidget;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use App\Models\User;
use App\Models\Setting;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\Facades\Response;
use App\Models\Feedback;
use App\Models\Statistics\Login;
use App\Models\Persona;
use Filament\Actions\Action;

class Reports extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $title = 'Reports';
    protected static string $view = 'filament.pages.reports';

    protected function getActions(): array
    {
        return [
            Action::make('Download Feedback csv')
                ->url(route('admin.reports.feedbacks'))
                ->openUrlInNewTab(),

            Action::make('Download Logins csv')
                ->url(route('admin.reports.logins'))
                ->openUrlInNewTab(),

            Action::make('Download Signups csv')
                ->url(route('admin.reports.signups'), 'post')
                ->openUrlInNewTab(),

            Action::make('Download Affinity Searches csv')
                ->url(route('admin.reports.affinity-searches'))
                ->openUrlInNewTab(),
        ];
    }
}
