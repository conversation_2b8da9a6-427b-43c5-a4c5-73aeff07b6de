<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\User;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Users', User::count())->color('primary'),
            Stat::make('Active Sessions', 'TBD')->color('success'),
            Stat::make('Revenue', 'TBD')->color('warning'),
        ];
    }
}
