<?php

namespace App\Providers;

use App\Adaptors\SocialMedia\SocialPilotAdapter;
use App\Contracts\Services\SocialMediaServiceInterface;
use App\Services\V1\SocialMedia\SocialMediaService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;

class SocialMediaServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(SocialMediaServiceInterface::class, function ($app) {
            $service = new SocialMediaService;
            $this->registerAdapters($service);

            return $service;
        });

        $this->app->bind('social-media.adapter.socialpilot', function ($app) {
            return new SocialPilotAdapter;
        });

    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Register adapters with the service
     */
    protected function registerAdapters(SocialMediaService $service): void
    {
        $platforms = Config::get('socialmedia.connections', []);

        foreach ($platforms as $platform => $config) {
            if (isset($config['driver']) && $this->app->bound('social-media.adapter.'.$platform)) {
                $adapter = $this->app->make('social-media.adapter.'.$platform);
                $service->registerAdapter($platform, $adapter);
            }
        }
    }
}
