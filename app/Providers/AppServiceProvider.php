<?php

namespace App\Providers;

use App\Contracts\Services\MediaGenerationServiceInterface;
use App\Contracts\Services\SocialMediaServiceInterface;
use App\Contracts\Services\UserSocialAccountServiceInterface;
use App\Services\V1\AIService\MediaGenerationService;
use App\Services\V1\SocialMedia\SocialMediaService;
use App\Services\V1\UserSocial\UserSocialAccountService;
use Biscolab\ReCaptcha\Rules\ReCaptchaRule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->registerServices();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Validator::extend('recaptcha', ReCaptchaRule::class);
    }

    private function registerServices(): void
    {
        $this->app->bind(UserSocialAccountServiceInterface::class, UserSocialAccountService::class);
        $this->app->bind(SocialMediaServiceInterface::class, SocialMediaService::class);
        $this->app->bind(MediaGenerationServiceInterface::class, MediaGenerationService::class);
    }
}
