{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "awcodes/filament-tiptap-editor": "^3.0", "biscolab/laravel-recaptcha": "^6.1", "facebook/graph-sdk": "^5.1", "facebook/php-business-sdk": "^21.0", "filament/filament": "^3.2", "google/analytics-admin": "^0.26.0", "google/analytics-data": "^0.22.2", "google/apiclient": "*", "guzzlehttp/guzzle": "^7.0", "hubspot/api-client": "^11.3", "inertiajs/inertia-laravel": "^1.0", "laravel/framework": "^11.9", "laravel/jetstream": "^5.1", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.20", "laravel/tinker": "^2.9", "laravel/ui": "^4.5", "league/flysystem-aws-s3-v3": "^3.0", "mailgun/mailgun-php": "^4.3", "openai-php/client": "^0.10.1", "stripe/stripe-php": "^15.7", "symfony/http-client": "^7.1", "symfony/mailgun-mailer": "^7.1", "tightenco/ziggy": "^2.0", "versionwatch/laravel-error-reporter": "1.1.0", "wirebox/laravel-airbrake": "^1.0"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/services.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}