<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Social Media Platform
    |--------------------------------------------------------------------------
    |
    | This option controls the default social media platform that will be used
    | when no specific platform is requested.
    |
    */
    'default' => env('SOCIAL_MEDIA_PLATFORM', 'socialpilot'),

    /*
    |--------------------------------------------------------------------------
    | Social Media Platform Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each social media
    | platform that your application will use. Each platform has its own
    | driver with appropriate configurations.
    |
    */
    'connections' => [
        'socialpilot' => [
            'driver' => 'socialpilot',
            'api_key' => env('SOCIALPILOT_API_KEY'),
            'base_url' => env('SOCIALPILOT_BASE_URL', 'https://api.socialpilot.co/v1'),
            'main_url' => env('SOCIALPILOT_MAIN_URL', 'https://social.pravi.ai'),
        ],
    ],
];
