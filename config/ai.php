<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default AI Provider
    |--------------------------------------------------------------------------
    |
    | This option controls the default AI provider that will be used by the
    | AI service factory when no specific provider is requested.
    |
    */
    'default' => env('AI_DEFAULT_PROVIDER', 'openai'),

    /*
    |--------------------------------------------------------------------------
    | AI Providers
    |--------------------------------------------------------------------------
    |
    | Here you may configure the AI providers for your application. Each
    | provider has its own configuration including the driver class,
    | authentication credentials, and default settings.
    |
    */
    'providers' => [
        'openai' => [
            'driver' => \App\Drivers\AIDrivers\OpenAIDriver::class,
            'token' => env('OPENAI_API_KEY'),
            'model' => env('OPENAI_MODEL', 'gpt-4o'),
            'max_tokens' => env('OPENAI_MAX_TOKENS', 1000),
            'temperature' => env('OPENAI_TEMPERATURE', 0.7),
            'base_url' => env('OPENAI_BASE_URL', 'https://api.openai.com/v1'),
        ],

        'piapi' => [
            'driver' => \App\Drivers\AIDrivers\PiAPIDriver::class,
            'api_key' => env('PIAPI_API_KEY'),
            'llm_model' => env('PIAPI_LLM_MODEL', 'gpt-4o'),
            'default_image_model' => env('PIAPI_IMAGE_MODEL', 'midjourney'),
            'default_video_model' => env('PIAPI_VIDEO_MODEL', 'kling'),
            'max_wait_time' => env('PIAPI_MAX_WAIT_TIME', 300),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Settings
    |--------------------------------------------------------------------------
    |
    | These are the default settings that will be applied to all providers
    | unless overridden in the specific provider configuration.
    |
    */
    'defaults' => [
        'timeout' => 30,
        'retry_attempts' => 3,
        'retry_delay' => 1000, // milliseconds
    ],
];
