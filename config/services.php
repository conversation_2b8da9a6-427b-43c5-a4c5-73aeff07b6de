<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'stripe' => [
        'secret' => env('STRIPE_SECRET'),
        'key' => env('STRIPE_KEY'),
        'fees' => [
            'flat' => env('STRIPE_FLAT_FEE', .2),
            'percent' => env('STRIPE_PERCENTAGE', 1.4),
        ],
        'extra_user_plan_id' => env('STRIPE_EXTRA_USER_PLAN_ID', 'price_1RVUtMIjPeWNHiTImaXQ7iaA'),
    ],

    'hubspot' => [
        'key' => env('HUBSPOT_API_KEY'),
    ],

    'recommendation_engine' => [
        'url' => env('RECOMMENDATION_ENGINE_URL'),
        'cache_time', env('RECOMMENDATION_ENGINE_CACHE_TIME', 60),
    ],

    'meta' => [
        'app_id' => env('META_APP_ID'),
        'app_secret' => env('META_APP_SECRET'),
        'app_config_id' => env('META_APP_CONFIG_ID'),
        'default_graph_version' => env('META_GRAPH_VERSION', 'v21.0'),
        'scopes' => env('META_SCOPES', 'ads_read,business_management,pages_read_engagement,pages_show_list,read_insights,instagram_basic,instagram_manage_insights,instagram_manage_comments'),

        'facebook' => [
            'insight_metrics' => env('FACEBOOK_INSIGHT_METRICS', 'page_impressions,page_impressions_unique,page_posts_impressions_organic,page_posts_impressions_paid'),
            'insight_ad_metrics' => env('FACEBOOK_INSIGHT_AD_METRICS', 'cpc,ctr,actions,action_values'),
        ],

        'instagram' => [
            'insight_metrics' => [
                'daily' => env('INSTAGRAM_INSIGHT_METRICS_DAILY'),
                'daily_grouped' => env('INSTAGRAM_INSIGHT_METRICS_DAILY_GROUPED'),
                'lifetime' => env('INSTAGRAM_INSIGHT_METRICS_LIFETIME'),
                'lifetime_grouped' => env('INSTAGRAM_INSIGHT_METRICS_LIFETIME_GROUP'),
            ],
            'insight_metrics_grouped' => env('INSTAGRAM_INSIGHT_METRICS_GROUPED'),
            'insight_ad_metrics' => env('INSTAGRAM_INSIGHT_AD_METRICS'),
        ],
    ],

    'google' => [
        'analytics' => [
            'credentials' => env('GOOGLE_ANALYTICS_CREDENTIALS'),
            'property_id' => env('GOOGLE_ANALYTICS_PROPERTY_ID'),
        ],
        'client' => [
            'id' => env('GOOGLE_CLIENT_ID'),
            'secret' => env('GOOGLE_CLIENT_SECRET'),
        ],
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT_URI'),
    ],
    
    'openai' => [
        'key' => env('OPENAI_API_KEY'),
    ]

];
